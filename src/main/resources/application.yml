server:
  port: 8088
  compression:
    enabled: true

spring:
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB

  main:
    allow-bean-definition-overriding: true

  application:
    name: tms

  datasource:
    driver-class-name: org.mariadb.jdbc.Driver
    url: *******************************************
    username: bmea
    password: bmea!@34
    hikari:
      maximum-pool-size: 10 # default: 10

  jpa:
    generate-ddl: true
    show-sql: false
    defer-datasource-initialization: true
    properties:
      hibernate:
        jdbc:
          batch_size: 1000
        order_inserts: true
        order_updates: true
        generate_statistics: false
        dialect: org.hibernate.dialect.MariaDBDialect
        format_sql: true
        hbm2ddl:
          schema_filter_provider: com.logistics.tms.framework.provider.TmsSchemaFilterProvider
        transaction.jta.platform: org.hibernate.service.jta.JtaPlatform
    hibernate:
      ddl-auto: update
      naming:
        physical-strategy: com.logistics.tms.framework.strategy.TmsPhysicalNamingStrategy

  sql:
    init:
      platform: mariadb
      mode: always
      schema-locations: classpath:sql/schema-${spring.sql.init.platform}.sql
      data-locations: classpath:sql/data-${spring.sql.init.platform}.sql
      encoding: utf-8
      separator: $$
      continue-on-error: false

  task:
    scheduling:
      pool:
        size: 10

logging:
  level:
    org:
      hibernate:
        orm:
          jdbc:
            bind: info

spring-doc:
  swagger-ui:
    tags-sorter: alpha
    operations-sorter: alpha

upload:
  serverName: https://bmea-tms-api.logisteq.com
  general: /app/BMEA/
  binary: /app/BMEA/apps/binary/
  resource: /app/BMEA/apps/resource/
  transportSign: /app/BMEA/transportSign/
  driverSign: /app/BMEA/driverSign/

download:
  general: /file/
  binary: /file/apps/binary/
  resource: /file/apps/resource/
  transportSign: /file/transportSign/
  driverSign: /file/driverSign/

# JWT Token
jwt:
  secret: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAIeEpx0dsoqJZaACXtjheLFf/5IDauxcVpAckYSYEWiWIYre+ucg7k0bEKix+y6O7AiuvYpwHvtjmxn6xR4E9NcCAwEAAQ==
  access:
    header: Authorization
  access-token-validity-in-seconds: 86400
  refresh-token-validity-in-seconds: 864000

host:
  vms-url: https://bmea-api.logisteq.com

lbs:
  route:
    base-url: http://lbs-core-dev.logisteq.com:15001
    connect-timeout: 30m
    read-timeout: 30m
    debug-log: true
  search:
    base-url: http://lbs-core-dev.logisteq.com:15001
    connect-timeout: 30s
    read-timeout: 60s
    debug-log: true
  cluster:
    base-url: http://lbs-core-dev.logisteq.com:15001
    connect-timeout: 30m
    read-timeout: 30m
    debug-log: true
  link:
    base-url: http://lbs-core-dev.logisteq.com:15001
    connect-timeout: 30m
    read-timeout: 30m
    debug-log: true
  load-optimize:
    base-url: https://load-optimize.cartamobility.com

feign:
  autoconfiguration:
    jackson:
      enabled: true
  client:
    config:
      default:
        decoder: feign.jackson.JacksonDecoder
      load-optimize:
        connectTimeout: 5000
        readTimeout: 5000
        loggerLevel: full
  jackson:
    mapper:      
      FAIL_ON_UNKNOWN_PROPERTIES: false

cron:
  track:
    partition:
      expression: "0 1 1 * * ?"
    route:
      expression: "0 0/5 * * * ?"
  shipment:
    fix:
      expression: "0 0 17 * * ?"
