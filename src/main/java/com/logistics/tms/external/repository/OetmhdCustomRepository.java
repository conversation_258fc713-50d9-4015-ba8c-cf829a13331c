package com.logistics.tms.external.repository;

import com.logistics.tms.common.enumeration.PartnerTypeEnum;
import com.logistics.tms.external.dto.OetmhdPushDTO;
import com.logistics.tms.external.dto.OetmhdTmsInfoDTO;
import com.logistics.tms.external.model.QMdesma;
import com.logistics.tms.external.model.QMptnma;
import com.logistics.tms.external.model.QOetmhd;
import com.logistics.tms.shipment.entity.QShipment;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

@RequiredArgsConstructor
@Repository
public class OetmhdCustomRepository {

    private final JPAQueryFactory queryFactory;

    public List<OetmhdTmsInfoDTO> findOetmskyByShipmentKey(String shipmentKey) {
        QOetmhd oetmhd = QOetmhd.oetmhd;

        return queryFactory
                .select(Projections.fields(OetmhdTmsInfoDTO.class,
                        oetmhd.compkey, oetmhd.oetmsky, oetmhd.tshitst, oetmhd.trnstat, oetmhd.tmshpno
                ))
                .from(oetmhd)
                .where(oetmhd.tmshpno.eq(shipmentKey))
                .fetch();
    }

    public List<OetmhdPushDTO> findShipyardByShipmentkeyIn(List<String> shpmtky) {
        QOetmhd oetmhd = QOetmhd.oetmhd;
        QMptnma mptnma = QMptnma.mptnma;
        QMdesma mdesma = QMdesma.mdesma;
        QShipment mt = QShipment.shipment;

        return queryFactory
                .select(Projections.fields(OetmhdPushDTO.class,
                        oetmhd.compkey, oetmhd.oetmsky, oetmhd.tmshpno, oetmhd.opakqty,
                        mptnma.ptnamlc.as("ptnamlcs"),      // DTO 필드명과 맞춤
                        mdesma.denamlc.as("denamlc"),
                        oetmhd.saatc08.as("recTelnm"),
                        oetmhd.saatc06.as("recOrgan"),
                        oetmhd.saatc07.as("recinm"),
                        oetmhd.olequip.as("equipment"),
                        oetmhd.vhctncd, oetmhd.vehicno
                ))
                .from(oetmhd)
                .leftJoin(mptnma).on(oetmhd.compkey.eq(mptnma.compkey), oetmhd.ptnrkey.eq(mptnma.ptnrkey))
                .leftJoin(mdesma).on(oetmhd.compkey.eq(mdesma.compkey), oetmhd.custkey.eq(mdesma.ptnrkey), oetmhd.destkey.eq(mdesma.destkey))
                .leftJoin(mt).on(oetmhd.tmshpno.eq(mt.shpmtky))
                .where(oetmhd.tmshpno.in(shpmtky), mt.ptnrtyp.eq(PartnerTypeEnum.CUSTOMER))
                .fetch();
    }

    public OetmhdPushDTO findShipyardByOetmsky(String oetmsky) {
        QOetmhd oetmhd = QOetmhd.oetmhd;
        QMptnma mptnma = QMptnma.mptnma;
        QMdesma mdesma = QMdesma.mdesma;
        QShipment mt = QShipment.shipment;

        return queryFactory
                .select(Projections.fields(OetmhdPushDTO.class,
                        oetmhd.compkey, oetmhd.oetmsky, oetmhd.tmshpno, oetmhd.opakqty,
                        mptnma.ptnamlc.as("ptnamlcs"),      // DTO 필드명과 맞춤
                        mdesma.denamlc.as("denamlc"),
                        oetmhd.saatc08.as("recTelnm"),
                        oetmhd.saatc06.as("recOrgan"),
                        oetmhd.saatc07.as("recinm"),
                        oetmhd.olequip.as("equipment"),
                        oetmhd.vhctncd, oetmhd.vehicno
                ))
                .from(oetmhd)
                .leftJoin(mptnma).on(oetmhd.compkey.eq(mptnma.compkey), oetmhd.ptnrkey.eq(mptnma.ptnrkey))
                .leftJoin(mdesma).on(oetmhd.compkey.eq(mdesma.compkey), oetmhd.custkey.eq(mdesma.ptnrkey), oetmhd.destkey.eq(mdesma.destkey))
                .leftJoin(mt).on(oetmhd.tmshpno.eq(mt.shpmtky))
                .where(oetmhd.oetmsky.eq(oetmsky), mt.ptnrtyp.in(PartnerTypeEnum.CUSTOMER, PartnerTypeEnum.OUTCENTER))
                .fetchFirst();
    }

}
