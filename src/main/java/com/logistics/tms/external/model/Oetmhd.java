package com.logistics.tms.external.model;

import com.logistics.tms.common.enumeration.*;
import com.logistics.tms.framework.converter.AsetecBooleanConverter;
import com.logistics.tms.framework.converter.AsetecDateYmdConverter;
import com.logistics.tms.framework.converter.AsetecTimeHisConverter;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Getter
@IdClass(OetmhdKey.class)
@Entity
@Table(name = "oetmhd")
public class Oetmhd {

    @Id
    @Size(max = 20)
    @NotNull
    @Column(name = "COMPKEY", nullable = false, length = 20)
    private String compkey;

    /** 운송실행오더 */
    @Id
    @Size(max = 10)
    @NotNull
    @Column(name = "OETMSKY", nullable = false, length = 10)
    private String oetmsky;

    @OneToMany(mappedBy = "oetmhd", fetch = FetchType.LAZY)
    private List<Oetmit> oetmit;

    /** 화주 */
    @Size(max = 20)
    @NotNull
    @Column(name = "OWNERKY", nullable = false, length = 20)
    private String ownerky;

    /** 운송오더 패키지명 */
    @Size(max = 100)
    @Column(name = "OETMSNM", length = 100)
    private String oetmsnm;

    /** 서비스오더 키 / 수주 오더그룹(교환/반송) 사용 */
    @Size(max = 20)
    @Column(name = "OSVRKEY", length = 20)
    private String osvrkey;

    /** From 발송지(기자재사) */
    @Size(max = 20)
    @Column(name = "PTNRKEY", length = 20)
    private String ptnrkey;

    /** From 발송지(상차지) */
    @Size(max = 20)
    @Column(name = "LOADKEY", length = 20)
    private String loadkey;

    /** From 발송지 Dock명칭 */
    @Size(max = 100)
    @Column(name = "DOCKNAM", length = 100)
    private String docknam;

    /** To고객 */
    @Size(max = 20)
    @Column(name = "CUSTKEY", length = 20)
    private String custkey;

    /** To고객 하차지 */
    @Size(max = 20)
    @Column(name = "DESTKEY", length = 20)
    private String destkey;

    /** 화물운영방식 */
    @Size(max = 10)
    @Column(name = "DCTCTYP", length = 10)
    private String dctctyp;

    /** 패키지운송상태 */
    @Size(max = 10)
    @Column(name = "TSHITST", length = 10)
    private OrderStatusEnum tshitst;

    /** 운송차량상태 */
    @Size(max = 10)
    @Column(name = "TRNSTAT", length = 10)
    private VehicleStatusEnum trnstat;

    /** 수동배차 필요여부(TMS 용도 문의) */
    @Column(name = "MANUVHP")
    private Integer manuvhp;

    /** 화주패키지 키 */
    @Size(max = 20)
    @Column(name = "OCOPAKY", length = 20)
    private String ocopaky;

    /** 오더 쉬먼트키 (화주+YYYYMMDD+번호3) */
    @Size(max = 40)
    @Column(name = "OSHPKEY", length = 40)
    private String oshpkey;

    /** 포장(패키지)형태 */
    @Size(max = 10)
    @Column(name = "OPKCATE", length = 10)
    private PackageCategoryEnum opkcate;

    /** 포장(패키지)타입 */
    @Size(max = 10)
    @Column(name = "OPKTYPE", length = 10)
    private PackageTypeEnum opktype;

    /** 포장ONE-SIDE 타입 */
    @Size(max = 10)
    @Column(name = "ONSIDTY", length = 10)
    private String onsidty;

    /** 요구차량 톤수 선택 */
    @Size(max = 10)
    @Column(name = "OPVHTON", length = 10)
    private OrderVehicleTonEnum opvhton;

    /** 요구차량 적재함 */
    @Size(max = 10)
    @Column(name = "OPVHTYP", length = 10)
    private String opvhtyp;

    /** 요구차량 옵션 */
    @Size(max = 10)
    @Column(name = "OPVHOPT", length = 10)
    private String opvhopt;

    /** 팔레트 박스 수량 */
    @Column(name = "OPAKQTY")
    private Integer opakqty;

    /** 운송화물방식 */
    @Size(max = 10)
    @Column(name = "CARGOTY", length = 10)
    private TruckLoadTypeEnum cargoty;

    /** 상하역장비 */
    @Size(max = 100)
    @Column(name = "OLEQUIP", length = 100)
    private String olequip;

    /** 가로(mm) */
    @Column(name = "PAKWIDH")
    private Integer pakwidh;

    /** 세로(mm) */
    @Column(name = "PAKDEPT")
    private Integer pakdept;

    /** 높이(mm) */
    @Column(name = "PAKHEIG")
    private Integer pakheig;

    /** 중량(kg) */
    @Column(name = "PKRWEIG", precision = 10, scale = 3)
    private BigDecimal pkrweig;

    /** 수주속성C01 */
    @Size(max = 10)
    @Column(name = "SAATC01", length = 10)
    private String saatc01;

    /** 수주속성C02 */
    @Size(max = 20)
    @Column(name = "SAATC02", length = 20)
    private String saatc02;

    /** 수주속성C04 */
    @Size(max = 50)
    @Column(name = "SAATC04", length = 50)
    private String saatc04;

    /** 수주속성C05 적치장 */
    @Size(max = 20)
    @Column(name = "SAATC05", length = 20)
    private String saatc05;

    /** 수주속성C06 인수부서명 */
    @Size(max = 20)
    @Column(name = "SAATC06", length = 20)
    private String saatc06;

    /** 수주속성C07 인수담당자 */
    @Size(max = 10)
    @Column(name = "SAATC07", length = 10)
    private String saatc07;

    /** 수주속성C08 인수담당자연락처 */
    @Size(max = 100)
    @Column(name = "SAATC08", length = 100)
    private String saatc08;

    /** 수주속성C09 */
    @Size(max = 100)
    @Column(name = "SAATC09", length = 100)
    private String saatc09;

    /** From 담당자 정보 */
    @Size(max = 100)
    @Column(name = "PTMANMO", length = 100)
    private String ptmanmo;

    /** From 상차 요청일 */
    @Size(max = 8)
    @Column(name = "LODRQDT", length = 8)
    @Convert(converter = AsetecDateYmdConverter.class)
    private LocalDate lodrqdt;

    /** From 상차 요청시간 */
    @Size(max = 6)
    @Column(name = "LODRQTM", length = 6)
    @Convert(converter = AsetecTimeHisConverter.class)
    private LocalTime lodrqtm;

    /** From 운송시작일자-상차지로 이동(TMS입력) */
    @Size(max = 8)
    @Column(name = "LODSTDT", length = 8)
    @Convert(converter = AsetecDateYmdConverter.class)
    private LocalDate lodstdt;

    /** From 운송시작시간-상차지로 이동(TMS입력) */
    @Size(max = 6)
    @Column(name = "LODSTTM", length = 6)
    @Convert(converter = AsetecTimeHisConverter.class)
    private LocalTime lodsttm;

    /** From 상차지 (최초) 도착예정 일자(TMS) */
    @Size(max = 8)
    @Column(name = "LOPLDAT", length = 8)
    @Convert(converter = AsetecDateYmdConverter.class)
    private LocalDate lopldat;

    /** From 상차지 (최초) 도착예정 시간(TMS) */
    @Size(max = 6)
    @Column(name = "LOPLTIM", length = 6)
    @Convert(converter = AsetecTimeHisConverter.class)
    private LocalTime lopltim;

    /** From 상차지 (구간) 도착예정 일자(TMS) */
    @Size(max = 8)
    @Column(name = "LOPGDAT", length = 8)
    @Convert(converter = AsetecDateYmdConverter.class)
    private LocalDate lopgdat;

    /** From 상차지 (구간) 도착예정 시간(TMS) */
    @Size(max = 6)
    @Column(name = "LOPGTIM", length = 6)
    @Convert(converter = AsetecTimeHisConverter.class)
    private LocalTime lopgtim;

    /** From 상차지 도착 일자(TMS 앱) */
    @Size(max = 8)
    @Column(name = "LOENDAT", length = 8)
    @Convert(converter = AsetecDateYmdConverter.class)
    private LocalDate loadArrDat;

    /** From 상차지 도착 시간(TMS 앱) */
    @Size(max = 6)
    @Column(name = "LOENTIM", length = 6)
    @Convert(converter = AsetecTimeHisConverter.class)
    private LocalTime loadArrTim;

    /** From 상차 확인완료일자(TMS입력) */
    @Size(max = 8)
    @Column(name = "LOADDAT", length = 8)
    @Convert(converter = AsetecDateYmdConverter.class)
    private LocalDate loaddat;

    /** From 상차 확인완료시간(TMS입력) */
    @Size(max = 6)
    @Column(name = "LOADTIM", length = 6)
    @Convert(converter = AsetecTimeHisConverter.class)
    private LocalTime loadtim;

    /** (우든타입)Bottom Stackable YN 하단 적재 가능 */
    @Size(max = 1)
    @Column(name = "BOTSKYN", length = 1)
    @Convert(converter = AsetecBooleanConverter.class)
    private Boolean botskyn;

    /** (우든타입)Top Stackable YN 상단 적재가능 */
    @Size(max = 1)
    @Column(name = "TOPSKYN", length = 1)
    @Convert(converter = AsetecBooleanConverter.class)
    private Boolean topskyn;

    /** To고객 도착예정일(납품예정일) 기자재사 요청한날자 */
    @Size(max = 8)
    @Column(name = "ULDRQDT", length = 8)
    @Convert(converter = AsetecDateYmdConverter.class)
    private LocalDate uldrqdt;

    /** To고객 도착예정시간(납품예정일) 기자재사 요청한시간 */
    @Size(max = 6)
    @Column(name = "ULDRQTM", length = 6)
    @Convert(converter = AsetecTimeHisConverter.class)
    private LocalTime uldrqtm;

    /** To고객 긴급운송여부(납품예정일) */
    @Size(max = 1)
    @Column(name = "TURGTYN", length = 1)
    @Convert(converter = AsetecBooleanConverter.class)
    private Boolean turgtyn;

    /** To하차지 (최초) 도착예정 일자(TMS) */
    @Size(max = 8)
    @Column(name = "DVPLDAT", length = 8)
    @Convert(converter = AsetecDateYmdConverter.class)
    private LocalDate dvpldat;

    /** To하차지 (최초) 도착예정 시간(TMS) */
    @Size(max = 6)
    @Column(name = "DVPLTIM", length = 6)
    @Convert(converter = AsetecTimeHisConverter.class)
    private LocalTime dvpltim;

    /** To하차지 (구간) 도착예정 일자(TMS) */
    @Size(max = 8)
    @Column(name = "DVPGDAT", length = 8)
    @Convert(converter = AsetecDateYmdConverter.class)
    private LocalDate dvpgdat;

    /** To하차지 (구간) 도착예정 시간(TMS) */
    @Size(max = 6)
    @Column(name = "DVPGTIM", length = 6)
    @Convert(converter = AsetecTimeHisConverter.class)
    private LocalTime dvpgtim;

    /** To하차지 도착 일자(TMS 앱) */
    @Size(max = 8)
    @Column(name = "DVENDAT", length = 8)
    @Convert(converter = AsetecDateYmdConverter.class)
    private LocalDate unloadArrDat;

    /** To하차지 도착 시간(TMS 앱) */
    @Size(max = 6)
    @Column(name = "DVENTIM", length = 6)
    @Convert(converter = AsetecTimeHisConverter.class)
    private LocalTime unloadArrTim;

    /** 영업일자-납품완료시 날짜 업데이트(TMS입력) */
    @Size(max = 8)
    @Column(name = "POSTDAT", length = 8)
    @Convert(converter = AsetecDateYmdConverter.class)
    private LocalDate postdat;

    /** To고객 납품완료일 (TMS입력) 차량도착 날자 */
    @Size(max = 8)
    @Column(name = "DVCMPDT", length = 8)
    @Convert(converter = AsetecDateYmdConverter.class)
    private LocalDate dvcmpdt;

    /** To고객 납품완료시간 (TMS입력) 차량도착 시간 */
    @Size(max = 6)
    @Column(name = "DVCMPTM", length = 6)
    @Convert(converter = AsetecTimeHisConverter.class)
    private LocalTime dvcmptm;

    /** To 고객(조선소) 입고완료일 */
    @Size(max = 8)
    @Column(name = "CUCMPDT", length = 8)
    @Convert(converter = AsetecDateYmdConverter.class)
    private LocalDate cucmpdt;

    /** To 고객(조선소) 입고완료시간 */
    @Size(max = 6)
    @Column(name = "CUCMPTM", length = 6)
    @Convert(converter = AsetecTimeHisConverter.class)
    private LocalTime cucmptm;

    /** 납품운송사 */
    @Size(max = 20)
    @Column(name = "CARRKEY", length = 20)
    private String carrkey;

    /** 납품차량번호 (TMS입력) */
    @Size(max = 20)
    @Column(name = "VEHICNO", length = 20)
    private String vehicno;

    /** 쉬뭔트 계획번호 (TMS입력) */
    @Size(max = 20)
    @Column(name = "TMSHPNO", length = 20)
    private String tmshpno;

    /** 차량톤수(TMS입력) */
    @Size(max = 10)
    @Column(name = "VHCTNCD", length = 10)
    private String vhctncd;

    /** 차량적재함 타입(TMS입력) */
    @Size(max = 10)
    @Column(name = "VHCTYPE", length = 10)
    private String vhctype;

    /** 차량옵션 타입(TMS 입력) */
    @Size(max = 10)
    @Column(name = "VHCOPTI", length = 10)
    private String vhcopti;

    /** 사진 패킹 기자재 */
    
    @Column(name = "OMFILES")
    private String omfiles;

    /** 사진 상차 기사앱 */
    
    @Column(name = "UPFILES")
    private String upfiles;

    /** 사진 하차 기사앱 */
    
    @Column(name = "DNFILES")
    private String dnfiles;

    /** 오더마감여부 */
    @Size(max = 1)
    @Column(name = "CLOSLYN", length = 1)
    @Convert(converter = AsetecBooleanConverter.class)
    private Boolean closlyn;

    /** 오더마감 일자 */
    @Size(max = 8)
    @Column(name = "CLODATE", length = 8)
    @Convert(converter = AsetecDateYmdConverter.class)
    private LocalDate clodate;

    /** 오더마감 시간 */
    @Size(max = 6)
    @Column(name = "CLOTIME", length = 6)
    @Convert(converter = AsetecTimeHisConverter.class)
    private LocalTime clotime;

    /** 오더마감 사용자 */
    @Size(max = 60)
    @Column(name = "CLOUSER", length = 60)
    private String clouser;

    /** 오더마감 번호 */
    @Size(max = 10)
    @Column(name = "CLOSENO", length = 10)
    private String closeno;

    /** 오더마감 아이템 */
    @Column(name = "CLOSEIT")
    private Integer closeit;

    /** 기본운임 */
    @Column(name = "STDHFEE")
    private Long stdhfee;

    /** 운임할증 */
    @Column(name = "DISHRAT")
    private Long dishrat;

    /** 운임할증사유 */
    @Size(max = 200)
    @Column(name = "DISHRSN", length = 200)
    private String dishrsn;

    /** 기타운임 */
    @Column(name = "OTHECHG")
    private Integer othechg;

    /** 기타운임사유 */
    @Size(max = 200)
    @Column(name = "OTHERSN", length = 200)
    private String othersn;

    /** 운송메모 */
    @Size(max = 200)
    @Column(name = "TPTMEMO", length = 200)
    private String tptmemo;

    /** 청구금액 Total */
    @Column(name = "TDLVFEE")
    private Integer tdlvfee;

    /** SYS 가로(mm) */
    @Column(name = "SYSWIDH")
    private Integer syswidh;

    /** SYS 세로(mm) */
    @Column(name = "SYSDEPT")
    private Integer sysdept;

    /** SYS 높이(mm) */
    @Column(name = "SYSHEIG")
    private Integer sysheig;

    /** SYS 중량(kg) */
    @Column(name = "SYSWEIG", precision = 10, scale = 3)
    private BigDecimal sysweig;

    /** SYS Dimension 입력 방식(ai_vision, device) */
    @Size(max = 20)
    @Column(name = "SYSMETH", length = 20)
    private String sysmeth;

    /** SYS Input method 비고 */
    @Size(max = 200)
    @Column(name = "SYSMEMO", length = 200)
    private String sysmemo;

    /** 생성일자 */
    @Size(max = 8)
    @Column(name = "CREDATE", length = 8)
    @Convert(converter = AsetecDateYmdConverter.class)
    private LocalDate credate;

    /** 생성시간 */
    @Size(max = 6)
    @Column(name = "CRETIME", length = 6)
    @Convert(converter = AsetecTimeHisConverter.class)
    private LocalTime cretime;

    /** 생성사용자 */
    @Size(max = 60)
    @NotNull
    @Column(name = "CREUSER", nullable = false, length = 60)
    private String creuser;

    /** 수정일자 */
    @Size(max = 8)
    @Column(name = "LMODATE", length = 8)
    @Convert(converter = AsetecDateYmdConverter.class)
    private LocalDate lmodate;

    /** 수정시간 */
    @Size(max = 6)
    @Column(name = "LMOTIME", length = 6)
    @Convert(converter = AsetecTimeHisConverter.class)
    private LocalTime lmotime;

    /** 수정사용자 */
    @Size(max = 60)
    @NotNull
    @Column(name = "LMOUSER", nullable = false, length = 60)
    private String lmouser;

    /** Business logic indicator */
    @Size(max = 1)
    @Column(name = "INDIBZL", length = 1)
    private String indibzl;

    /** Archive indicator */
    @Size(max = 1)
    @Column(name = "INDIARC", length = 1)
    private String indiarc;

    /** Update check */
    @NotNull
    @Column(name = "UPDTCHK", nullable = false)
    private Integer updtchk;

}