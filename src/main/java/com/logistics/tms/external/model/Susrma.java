package com.logistics.tms.external.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.hibernate.annotations.ColumnTransformer;

@Getter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "susrma")
public class Susrma {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "USERASQ")
    private Long userasq;

    @Size(max = 60)
    @NotNull
    @Column(name = "USERACT", nullable = false, length = 60)
    private String useract;

    @Size(max = 20)
    @NotNull
    @Column(name = "COMPKEY", nullable = false, length = 20)
    private String compkey;

    @Size(max = 100)
    @NotNull
    @Column(name = "PASSWRD", nullable = false, length = 100)
    private String passwrd;

    @Size(max = 60)
    @NotNull
    @Column(name = "USERNAM", nullable = false, length = 60)
    private String usernam;

    @Size(max = 10)
    @NotNull
    @Column(name = "USERTYP", nullable = false, length = 10)
    private String usertyp;

    @Size(max = 20)
    @NotNull
    @Column(name = "PTNRKEY", nullable = false, length = 20)
    private String ptnrkey;

//    @Size(max = 20)
//    @NotNull
//    @Column(name = "ROLGKEY", nullable = false, length = 20)
//    private String rolgkey;

    @Size(max = 100)
    @NotNull
    @Column(name = "ADDRESS", nullable = false, length = 100)
    private String address;

    @Size(max = 20)
    @Column(name = "CITYCOD", length = 20)
    private String citycod;

    @Size(max = 10)
    @Column(name = "RSONKEY", length = 10)
    private String rsonkey;

    @Size(max = 10)
    @Column(name = "POSTCOD", length = 10)
    private String postcod;

    @Size(max = 20)
    @Column(name = "TELPHNM", length = 20, columnDefinition = "TEXT")
    @ColumnTransformer(
            read = "FN_AES_DECRYPT(TELPHNM)",
            write = "FN_AES_ENCRYPT(?)"
    )
    private String telphnm;

    @Size(max = 50)
    @Column(name = "EMAILAD", length = 50)
    private String emailad;

    @Size(max = 50)
    @Column(name = "DEPARTM", length = 50)
    private String departm;

    @Size(max = 50)
    @Column(name = "EMPLOID", length = 50)
    private String emploid;

    @Size(max = 50)
    @Column(name = "ACAPDOC", length = 50)
    private String acapdoc;

    @Size(max = 20)
    @Column(name = "USERGR1", length = 20)
    private String usergr1;

    @Size(max = 20)
    @Column(name = "USERGR2", length = 20)
    private String usergr2;

    @Size(max = 20)
    @Column(name = "USERGR3", length = 20)
    private String usergr3;

    @Size(max = 3)
    @Column(name = "LANGKEY", length = 3)
    private String langkey;

    @Size(max = 20)
    @Column(name = "DATEFMT", length = 20)
    private String datefmt;

    @Size(max = 20)
    @Column(name = "TIMEFMT", length = 20)
    private String timefmt;

    @Size(max = 20)
    @Column(name = "DECIFMT", length = 20)
    private String decifmt;

    @Column(name = "UTIMZON")
    private Integer utimzon;

    @Size(max = 8)
    @NotNull
    @Column(name = "PWEXPDT", nullable = false, length = 8)
    private String pwexpdt;

    @NotNull
    @Column(name = "PWERCNT", nullable = false)
    private Integer pwercnt;

    @Size(max = 8)
    @NotNull
    @Column(name = "IDEXPDT", nullable = false, length = 8)
    private String idexpdt;

    @Size(max = 1)
    @NotNull
    @Column(name = "ALOCKED", nullable = false, length = 1)
    private String alocked;

    @Size(max = 100)
    @NotNull
    @Column(name = "ALOCKMO", nullable = false, length = 100)
    private String alockmo;

    @Size(max = 1)
    @NotNull
    @Column(name = "POLPRYN", nullable = false, length = 1)
    private String polpryn;

    @Size(max = 1)
    @NotNull
    @Column(name = "POLSVYN", nullable = false, length = 1)
    private String polsvyn;

    @Size(max = 1)
    @NotNull
    @Column(name = "POLSMYN", nullable = false, length = 1)
    private String polsmyn;

    @Size(max = 1)
    @NotNull
    @Column(name = "POLEMYN", nullable = false, length = 1)
    private String polemyn;

    @Size(max = 8)
    @NotNull
    @Column(name = "POLDATE", nullable = false, length = 8)
    private String poldate;

    @Size(max = 6)
    @NotNull
    @Column(name = "POLTIME", nullable = false, length = 6)
    private String poltime;

    @Size(max = 1)
    @NotNull
    @Column(name = "APPROYN", nullable = false, length = 1)
    private String approyn;

    @Size(max = 1)
    @Column(name = "SOTAPYN", length = 1)
    private String sotapyn;

    @Size(max = 8)
    @Column(name = "SOTDATE", length = 8)
    private String sotdate;

    @Size(max = 6)
    @Column(name = "SOTTIME", length = 6)
    private String sottime;

    @Size(max = 10)
    @NotNull
    @Column(name = "THEMETY", nullable = false, length = 10)
    private String themety;

    @Size(max = 1)
    @Column(name = "FAVORYN", length = 1)
    private String favoryn;

    @Size(max = 1000)
    @Column(name = "RETKKEY", length = 1000)
    private String retkkey;

    @Size(max = 8)
    @Column(name = "RETKEDT", length = 8)
    private String retkedt;

    @Size(max = 8)
    @Column(name = "CREDATE", length = 8)
    private String credate;

    @Size(max = 6)
    @Column(name = "CRETIME", length = 6)
    private String cretime;

    @Size(max = 60)
    @Column(name = "CREUSER", length = 60)
    private String creuser;

    @Size(max = 8)
    @Column(name = "LMODATE", length = 8)
    private String lmodate;

    @Size(max = 6)
    @Column(name = "LMOTIME", length = 6)
    private String lmotime;

    @Size(max = 60)
    @Column(name = "LMOUSER", length = 60)
    private String lmouser;

    @Size(max = 1)
    @Column(name = "INDIBZL", length = 1)
    private String indibzl;

    @Size(max = 1)
    @Column(name = "INDIARC", length = 1)
    private String indiarc;

    @Column(name = "UPDTCHK")
    private Integer updtchk;

}