package com.logistics.tms.external.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class OetmhdPushDTO {
    private String compkey;
    private String oetmsky;
    private String tmshpno;
    private Integer opakqty;
    private String ptnamlc;             // 상차지 파트너 명칭
    private String denamlc;             // 하차지 도착지 명칭(상세)
    private String recTelnm;            // 인수자 전화번호
    private String recOrgan;            // 인수 부서
    private String recinm;              // 인수자 이름
    private String equipment;           // 하차장비
    private String vhctncd;             // 차량 톤수
    private String vehicno;             // 차량 번호
    private String drivernm;            // 드라이버 이름
    private String driverTel;           // 드라이버 전화번호

    public OetmhdPushDTO(OetmhdPushDTO val) {
        this.compkey = val.getCompkey();
        this.oetmsky = val.getOetmsky();
        this.tmshpno = val.getTmshpno();
        this.opakqty = val.getOpakqty();
        this.ptnamlc = val.getPtnamlc();
        this.denamlc = val.getDenamlc();
        this.recTelnm = val.getRecTelnm();
        this.recOrgan = val.getRecOrgan();
        this.recinm = val.getRecinm();
        this.equipment = val.getEquipment();
        this.vhctncd = val.getVhctncd();
        this.vehicno = val.getVehicno();
        this.drivernm = "";
        this.driverTel = "";
    }
}
