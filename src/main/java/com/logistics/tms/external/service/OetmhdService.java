package com.logistics.tms.external.service;

import com.logistics.tms.external.dto.OetmhdPushDTO;
import com.logistics.tms.external.dto.OetmhdTmsInfoDTO;
import com.logistics.tms.external.repository.OetmhdCustomRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Transactional(readOnly = true)
@Service
public class OetmhdService {
    private final OetmhdCustomRepository oetmhdCustomRepository;

    public List<OetmhdTmsInfoDTO> findOetmskyByShipmentKey(String shipmentKey) {
        return oetmhdCustomRepository.findOetmskyByShipmentKey(shipmentKey);
    }

    public List<OetmhdPushDTO> findShipyardByShipmentkey(String shpmtky) {
        List<String> oetmsky = findOetmskyByShipmentKey(shpmtky).stream()
                .map(e -> e.getOetmsky())
                .toList();

        return oetmhdCustomRepository.findShipyardByShipmentkeyIn(oetmsky);
    }

    public OetmhdPushDTO findShipyardByOetmsky(String oetmsky) {

        return oetmhdCustomRepository.findShipyardByOetmsky(oetmsky);
    }

}
