package com.logistics.tms.shipment.dto;

import com.logistics.tms.common.util.AuthUtils;
import com.logistics.tms.configuration.security.AuthDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Objects;
import java.util.Optional;

import com.logistics.tms.common.constant.TmsConstant;

/**
 * 운송 OMS 아이템 관련 커스텀 DTO 클래스
 * 운송 아이템의 추가 및 생성 로직을 포함
 */
public class ShipmentOmsItemCustomDTO {

	/**
	 * 운송 아이템 추가용 DTO
	 * 클라이언트에서 운송 아이템을 새로 생성할 때 사용하는 입력 데이터 구조
	 */
	@Schema(description = "운송ITEM ADD")
	@Setter
	@Getter
	@NoArgsConstructor
	@AllArgsConstructor
	@SuperBuilder
	public static class ShipmentOmsItemAddDTO {
		/** 운송 키 */
		private String shpmtky;
		/** OE 팀 키 */
		private String oetmsky;
		/** 적재 Y 팔레트 */
		private Integer loadyplt;
		/** 적재 X 팔레트 */
		private Integer loadxplt;
		/** 적재 Y 단위 */
		private Integer loadyunit;
		/** 적재 X 단위 */
		private Integer loadxunit;
		/** 적재 단위 깊이 */
		private Integer loadUnitDepth;
		/** 적재 단위 너비 */
		private Integer loadUnitWidth;
		/** 적재 레벨 */
		private Integer level;
		/** 회사 키 */
		private String compkey;
	}

	// ----------------------------------------------------
	// --- static Functions
	// ----------------------------------------------------

	/**
	 * ShipmentOmsItemAddDTO를 기반으로 새로운 ShipmentOmsItemDTO 객체를 생성
	 * 
	 * 주요 기능:
	 * - 입력 데이터를 완전한 엔티티 DTO로 변환
	 * - 시스템 기본값 설정 (생성일시, 사용자 정보 등)
	 * - 인증된 사용자 정보 자동 설정
	 * - level 필드의 안전한 기본값 처리
	 * 
	 * @param add 운송 아이템 추가용 DTO
	 * @return 완전히 초기화된 ShipmentOmsItemDTO 객체
	 */
	public static ShipmentOmsItemDTO newShipmentOmsItemDTO(ShipmentOmsItemAddDTO add) {
		ShipmentOmsItemDTO item = new ShipmentOmsItemDTO();

		// 비즈니스 데이터 복사
		item.setShpmtky(add.getShpmtky());
		item.setOetmsky(add.getOetmsky());
		item.setLoadyplt(add.getLoadyplt());
		item.setLoadxplt(add.getLoadxplt());
		item.setLoadyunit(add.getLoadyunit());
		item.setLoadxunit(add.getLoadxunit());
		item.setLoadUnitDepth(add.getLoadUnitDepth());
		item.setLoadUnitWidth(add.getLoadUnitWidth());

		// level 필드의 null 안전 처리 - 기본값 0 설정
		item.setLevel(Optional.ofNullable(add.getLevel()).orElse(0));
		item.setCompkey(add.getCompkey());

		// 시스템 생성 정보 자동 설정
		item.setCredate(LocalDate.now());
		item.setCretime(LocalTime.now());

		// 인증된 사용자 정보 설정 (없을 경우 기본값 사용)
		final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
		if (Objects.nonNull(authDTO)) {
			item.setCreuser(authDTO.getId());
			item.setLmouser(authDTO.getId());
		} else {
			// 인증 정보가 없는 경우 시스템 기본 사용자로 설정
			item.setCreuser(TmsConstant.DEFAULT_CREUSER);
			item.setLmouser(TmsConstant.DEFAULT_LMOUSER);
		}

		// 업데이트 체크 플래그 초기화
		item.setUpdtchk(0);

		return item;
	}
}
