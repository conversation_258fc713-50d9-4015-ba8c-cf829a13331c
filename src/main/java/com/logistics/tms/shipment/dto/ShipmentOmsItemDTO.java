package com.logistics.tms.shipment.dto;

import com.logistics.tms.common.dto.BaseDTO;
import com.logistics.tms.shipment.entity.Shipment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ShipmentOmsItemDTO extends BaseDTO {
    @Schema(description = "운송ITEM 번호")
    private Long shpitky;

    @Schema(description = "Shipment No.")
    private String shpmtky;

    @Schema(description = "운송주문번호")
    private String oetmsky;

    @Schema(description = "팔레트 위치 (Y) mm")
    private Integer loadyplt;

    @Schema(description = "팔레트 위치 (X) mm")
    private Integer loadxplt;

    @Schema(description = "팔레트 위치 (Y) unit")
    private Integer loadyunit;

    @Schema(description = "팔레트 위치 (X) unit")
    private Integer loadxunit;

    @Schema(description = "팔레트 unit 단위 깊이")
    private Integer loadUnitDepth;

    @Schema(description = "팔레트 unit 단위 폭")
    private Integer loadUnitWidth;

    @Schema(description = "적재 층수 (0: 하단/1층, 1: 상단/2층) 없을 경우 0으로 간주함")
    private Integer level;

    @Schema(description = "삭제일시")
    private LocalDateTime deletat;

    @Schema(description = "Service Client")
    private String compkey;

}
