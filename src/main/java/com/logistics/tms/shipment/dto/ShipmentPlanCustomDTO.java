package com.logistics.tms.shipment.dto;

import com.logistics.tms.common.constant.TmsConstant;
import com.logistics.tms.common.enumeration.*;
import com.logistics.tms.loadingoptimizationmanager.feign.type.MultiTruckLoadingResult;
import com.logistics.tms.shipment.entity.ShipmentPlan;

import org.locationtech.jts.geom.Point;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;


public class ShipmentPlanCustomDTO {

    @Schema(description = "운송계획 생성 결과")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentPlanMakeResponseDTO {
        private ShipmentPlanMakeDetailDTO detailShipPlan;
        private String errorMsg;
    }

    @Schema(description = "운송계획 생성 결과 Detail")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentPlanMakeDetailDTO {
        private String shpplky;
        private Integer shpmtkyCount;
    }

    @Schema(description = "운송계획 생성 결과 Dummy")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentPlanMakeDummyDTO {
        private ShipmentPlan shipPlan;
        private String errorMsg;
        private String shpplky;
        private String oldShpplky;
        private Boolean isFixedPlan;
        private Integer shpmtkyCount;
        private List<String> shpmtkyList;
        private List<String> oetmskyValues;
        private List<ShipmentOetmskyPairDTO> manualPairList;
        private List<List<ShipmentOmsItemDTO>> shipItemLists;
    }

    @Schema(description = "운송계획 LoadOptimizer Result")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentPlanLoadOptimizerResultDTO {
        private MultiTruckLoadingResult loadResult;
        private String errorMsg;
    }

    @Schema(description = "운송계획 MAIN (결과 생성여부)")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentPlanCheckMadeDTO {

        @Schema(description = "Service Client", example = TmsConstant.DEFAULT_COMPKEY)
        private String compkey;

        @Schema(description = "운송계획 생성여부 (True:운송계획결과정보 존재)")
        private Boolean ismade;

        @Schema(description = "운송계획변경여부 (True:변경팝업)")
        private Boolean ischanged;

        @Schema(description = "Tooltip 보여주기")
        private Boolean showTooltip;

        @Schema(description = "운송일")
        private LocalDate shpmtdt;

        @Schema(description = "운송회차")
        private Integer roundno;

        @Schema(description = "운송계획번호")
        private String shpplky;

        @Schema(description = "변경 일자")
        private LocalDate changedshpmtdt;

        @Schema(description = "변경 회차")
        private Integer changedroundno;

        @Schema(description = "생성 일자")
        private LocalDateTime simulat;

        @Schema(description = "확정 일자")
        private LocalDateTime fixedat;
    }

    @Schema(description = "운송계획결과정보갯수 DUMMY")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentResultCountDummyDTO {

        @Schema(description = "Service Client", example = TmsConstant.DEFAULT_COMPKEY)
        private String compkey;

        @Schema(description = "운송계획번호")
        private String shpplky;

        @Schema(description = "Shipment No.")
        private String shpmtky;

        @Schema(description = "협력업체키")
        private String ptnrkey;

        @Schema(description = "협력업체 명칭")
        private String ptnamlc;

        @Schema(description = "협력업체 타입")
        private PartnerTypeEnum ptnrtyp;

        @Schema(description = "단독/혼적")
        private TruckLoadTypeEnum cargoty;

        @Schema(description = "확정일시")
        private LocalDateTime fixedat;

        @Schema(description = "차량중량")
        private ShipmentVehicleTonEnum vhctncd;

        @Schema(description = "차량별 수")
        private Integer vhctncd_count;
    }

    @Schema(description = "운송계획결과정보갯수")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentResultCountDTO {

        @Schema(description = "운송계획번호")
        private String shpplky;

        @Schema(description = "운송계획변경여부 (True:변경팝업)")
        private Boolean ischanged;

        //@Schema(description = "운송계획확정여부")
        //private Boolean isFixed;

        @Schema(description = "배차확정여부")
        private Boolean isDispatch;

        @Schema(description = "운송계획확정일시")
        private LocalDateTime fixedat;

        @Schema(description = "차량중량별 수")
        Map<ShipmentVehicleTonEnum, Integer> vhctnCount;

        @Schema(description = "리스트")
        List<ShipmentResultCountItemDTO> countItemList;
    }

    @Schema(description = "운송계획결과정보갯수 Item")
    @Getter
    @Setter
    public static class ShipmentResultCountItemDTO {

        @Schema(description = "Service Client", example = TmsConstant.DEFAULT_COMPKEY)
        private String compkey;

        @Schema(description = "협력업체키")
        private String ptnrkey;

        @Schema(description = "협력업체 명칭")
        private String ptnamlc;

        @Schema(description = "협력업체 타입")
        private PartnerTypeEnum ptnrtyp;

        @Schema(description = "전체 차량수")
        private Integer totcont;

        @Schema(description = "단독 차량수")
        private Integer ftlcont;

        @Schema(description = "혼적 차량수")
        private Integer ltlcont;

        @Schema(description = "차량중량별 수")
        private Map<ShipmentVehicleTonEnum, Integer> vhctnCount;

        public ShipmentResultCountItemDTO(String compkey, String ptnrkey, String ptnamlc,
                                    PartnerTypeEnum ptnrtyp,
                                    Integer totcont, Integer ftlcont, Integer ltlcont) {
            this.compkey = compkey;
            this.ptnrkey = ptnrkey;
            this.ptnamlc = ptnamlc;
            this.ptnrtyp = ptnrtyp;
            this.totcont = totcont;
            this.ftlcont = ftlcont;
            this.ltlcont = ltlcont;
        }
    }

    @Schema(description = "운송계획결과정보 Dummy")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentResultDummyDTO {

        @Schema(description = "Service Client", example = TmsConstant.DEFAULT_COMPKEY)
        private String compkey;

        @Schema(description = "Shipment No.")
        private String shpmtky;

        @Schema(description = "운송화물방식: " + TruckLoadTypeEnum.COLUMN_COMMENT)
        private TruckLoadTypeEnum cargoty;

        @Schema(description = "차량톤수")
        private ShipmentVehicleTonEnum vhctncd;

        @Schema(description = "상차")
        private String piaddr;

        @Schema(description = "하차")
        private String deaddr;

        @Schema(description = "적재율")
        private Integer loadrat;

        @Schema(description = "협력업체키")
        private String ptnrkey;

        @Schema(description = "도착지 key")
        private String destkey;

        @Schema(description = "운송일")
        private LocalDate shpmtdt;

        @Schema(description = "운송주문번호")
        private String oetmsky;

        @Schema(description = "협력업체 명칭")
        private String ptnamlc;

        @Schema(description = "협력업체 타입")
        private PartnerTypeEnum ptnrtyp;

        @Schema(description = "운송계획번호")
        private String shpplky;

        @Schema(description = "상차지 수")
        private Integer countofpickup;

        @Schema(description = "하차지 수")
        private Integer countofdropoff;
    }

    @Schema(description = "운송계획결과정보및갯수")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentTargetResultCountDTO {
        @Schema(description = "운송계획 ResultCount")
        private ShipmentResultCountDTO resultCount;

        @Schema(description = "운송계획 TargetCount")
        private ShipmentPlanChangedTargetDTO targetCount;
    }

    @Schema(description = "운송계획결과정보 ListDummy")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentResultListDummyDTO {

        @Schema(description = "Service Client", example = TmsConstant.DEFAULT_COMPKEY)
        private String compkey;

        @Schema(description = "Shipment No.")
        private String shpmtky;

        @Schema(description = "운송화물방식: " + TruckLoadTypeEnum.COLUMN_COMMENT)
        private TruckLoadTypeEnum cargoty;

        @Schema(description = "차량톤수")
        private ShipmentVehicleTonEnum vhctncd;

        @Schema(description = "상차")
        private String piaddr;

        @Schema(description = "하차")
        private String deaddr;

        @Schema(description = "적재율")
        private Integer loadrat;

        @Schema(description = "협력업체키")
        private String ptnrkey;

        @Schema(description = "도착지 key")
        private String destkey;

        @Schema(description = "운송일")
        private LocalDate shpmtdt;

        @Schema(description = "운송주문번호")
        private String oetmsky;

        @Schema(description = "협력업체 명칭")
        private String ptnamlc;

        @Schema(description = "협력업체 타입")
        private PartnerTypeEnum ptnrtyp;

        @Schema(description = "운송계획번호")
        private String shpplky;

        @Schema(description = "상차지 수")
        private Integer countofpickup;

        @Schema(description = "하차지 수")
        private Integer countofdropoff;

        @Schema(description = "상하차 타입")
        private VisitTypeEnum vsttype;

        @Schema(description = "운행 순서")
        private Integer vstordr;

        @Schema(description = "도착지 업체이름")
        private String scptnamlc;

        @Schema(description = "팔레트 박스 수량")
        private Integer opakqty;

        @Schema(description = "중량")
        private BigDecimal pkrweig;

        @Schema(description = "예상운임비")
        private Integer estimatedfee;

    }

    @Schema(description = "운송계획결과정보 ITEM")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentResultItemDTO {

        @Schema(description = "Shipment No.")
        private String shpmtky;

        @Schema(description = "운송화물방식: " + TruckLoadTypeEnum.COLUMN_COMMENT)
        private TruckLoadTypeEnum cargoty;

        @Schema(description = "차량톤수")
        private ShipmentVehicleTonEnum vhctncd;

        @Schema(description = "상차")
        private String piaddr;

        @Schema(description = "하차")
        private String deaddr;

        @Schema(description = "적재율")
        private Integer loadrat;

        @Schema(description = "도착지 key")
        private String destkey;

        @Schema(description = "운송주문번호")
        private String oetmsky;

        @Schema(description = "상차지 수")
        private Integer countofpickup;

        @Schema(description = "하차지 수")
        private Integer countofdropoff;

        @Schema(description = "예상 운임비(VAT 포함)")
        private Integer estimatedfee;

        @Schema(description = "총중량")
        private BigDecimal totalWeight;

        @Schema(description = "제약 사항", example = "크레인 상차, 윙바디 불가")
        private String restriction;

        @Schema(description = "제약 사항들", example = "크레인 상차, 윙바디 불가")
        private List<String> restrictions;

        @Schema(description = "DEBUG puaddr")
        private List<String> debug_puaddr;

        @Schema(description = "DEBUG dfaddr")
        private List<String> debug_dfaddr;

    }

    @Schema(description = "운송계획결과정보")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentResultDTO {

        @Schema(description = "Service Client", example = TmsConstant.DEFAULT_COMPKEY)
        private String compkey;

        @Schema(description = "운송계획번호")
        private String shpplky;

        @Schema(description = "협력업체키")
        private String ptnrkey;

        @Schema(description = "협력업체 명칭")
        private String ptnamlc;

        @Schema(description = "협력업체 타입")
        private PartnerTypeEnum ptnrtyp;

        @Schema(description = "전체차량수")
        private Integer totalcount;

        @Schema(description = "단독차량수")
        private Integer ftlcount;

        @Schema(description = "혼적차량수")
        private Integer ltlcount;

        @Schema(description = "운송일")
        private LocalDate shpmtdt;

        @Schema(description = "운송회차")
        private Integer roundno;

        @Schema(description = "운송항목")
        private List<ShipmentResultItemDTO> shipmentItems;
    }

    @Schema(description = "운송계획결과정보 지도")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentMapResultDTO {

        @Schema(description = "Service Client", example = TmsConstant.DEFAULT_COMPKEY)
        private String compkey;

        @Schema(description = "협력업체키")
        private String ptnrkey;

        @Schema(description = "협력업체 명칭")
        private String ptnamlc;

        @Schema(description = "협력업체 타입")
        private PartnerTypeEnum ptnrtyp;

        @Schema(description = "협력업체 위치")
        private Point decoord;

        @Schema(description = "전체차량수")
        private Integer totalvhctncd;

        @Schema(description = "단독차량수")
        private Integer ftlvhctncd;

        @Schema(description = "혼적차량수")
        private Integer ltlvhctncd;

        @Schema(description = "단독차량 구성")
        private Map<ShipmentVehicleTonEnum, Integer> ftlVehicleTonMap;

        @Schema(description = "혼적차량 구성")
        private Map<ShipmentVehicleTonEnum, Integer> ltlVehicleTonMap;

        @Schema(description = "단독차량 구성")
        private String ftlVehicleTonStr;

        @Schema(description = "혼적차량 구성")
        private String ltlVehicleTonStr;

        @Schema(description = "상하차리스트")
        private List<ShipmentMapDest> shipmentMapDest;
    }

    @Schema(description = "운송계획결과정보 Dest")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentMapDest {

        @Schema(description = "Shipment No.")
        private String shpmtky;

        @Schema(description = "운송화물방식: " + TruckLoadTypeEnum.COLUMN_COMMENT)
        private TruckLoadTypeEnum cargoty;

        @Schema(description = "협력업체키")
        private String ptnrkey;

        @Schema(description = "협력업체 명칭")
        private String ptnamlc;

        @Schema(description = "협력업체 타입")
        private PartnerTypeEnum ptnrtyp;

        @Schema(description = "상하차도착 key")
        private String destkey;

        @Schema(description = "상하차도착 명칭")
        private String denamlc;

        @Schema(description = "상하차도착 위치")
        private Point decoord;

        @Schema(description = "상하차 구분")
        private VisitTypeEnum vsttype;

        @Schema(description = "운송주문번호")
        private String oetmsky;
    }

    @Schema(description = "운송계획결과정보 MAP DUMMY")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentMapDummyDTO {

        @Schema(description = "Shipment No.")
        private String shpmtky;

        @Schema(description = "운송화물방식: " + TruckLoadTypeEnum.COLUMN_COMMENT)
        private TruckLoadTypeEnum cargoty;

        @Schema(description = "차량톤수")
        private ShipmentVehicleTonEnum vhctncd;

        @Schema(description = "적재율")
        private Integer loadrat;

        // for PICKUP
        @Schema(description = "협력업체키")
        private String puptnrkey;

        @Schema(description = "협력업체 명칭")
        private String puptnamlc;

        @Schema(description = "협력업체 타입")
        private PartnerTypeEnum puptnrtyp;

        @Schema(description = "상하차도착 key")
        private String pudestkey;

        @Schema(description = "상하차도착 명칭")
        private String pudenamlc;

        @Schema(description = "상하차도착 위치")
        private Point pudecoord;

        @Schema(description = "상하차 구분")
        private VisitTypeEnum puvsttype;

        @Schema(description = "운송주문번호")
        private String puoetmsky;

        // for DROPOFF
        @Schema(description = "협력업체키")
        private String dfptnrkey;

        @Schema(description = "협력업체 명칭")
        private String dfptnamlc;

        @Schema(description = "협력업체 타입")
        private PartnerTypeEnum dfptnrtyp;

        @Schema(description = "상하차도착 key")
        private String dfdestkey;

        @Schema(description = "상하차도착 명칭")
        private String dfdenamlc;

        @Schema(description = "상하차도착 위치")
        private Point dfdecoord;

        @Schema(description = "상하차 구분")
        private VisitTypeEnum dfvsttype;

        @Schema(description = "운송주문번호")
        private String dfoetmsky;
    }

    @Schema(description = "운송계획 확정 확인 DUMMY")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentPlanFixDummyDTO {

        @Schema(description = "협력업체키")
        private String ptnrkey;

        @Schema(description = "협력업체 명칭")
        private String ptnamlc;

        @Schema(description = "협력업체 타입")
        private PartnerTypeEnum ptnrtyp;

        @Schema(description = "Shipment No.")
        private String shpmtky;

        @Schema(description = "운송화물방식: " + TruckLoadTypeEnum.COLUMN_COMMENT)
        private TruckLoadTypeEnum cargoty;

        @Schema(description = "차량톤수")
        private ShipmentVehicleTonEnum vhctncd;

        @Schema(description = "적재율")
        private Integer loadrat;

        @Schema(description = "용차 섭외 금액")
        private Integer vehcost;

        @Schema(description = "총운송비용")
        private Integer totalcost;

        @Schema(description = "예상운임비")
        private Integer estimatedfee;

        @Schema(description = "운송구간 번호 Pickup")
        private String puscky;

        @Schema(description = "운송구간 번호 Dropoff")
        private String dfscky;

        @Schema(description = "일자")
        private LocalDate shpmtdt;

        @Schema(description = "회차")
        private Integer roundno;

        @Schema(description = "운송계획번호")
        private String shpplky;
    }

    @Schema(description = "운송계획 확정 확인 Shipment No")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentPlanFixShipmentDTO {

        @Schema(description = "Shipment No.")
        private String shpmtky;

        @Schema(description = "운송화물방식: " + TruckLoadTypeEnum.COLUMN_COMMENT)
        private TruckLoadTypeEnum cargoty;

        @Schema(description = "차량톤수")
        private ShipmentVehicleTonEnum vhctncd;

        @Schema(description = "적재율")
        private Integer loadrat;

        @Schema(description = "상차지 수")
        private Integer countofpickup;

        @Schema(description = "하차지 수")
        private Integer countofdropoff;

        @Schema(description = "예상 운임비")
        private Integer estimatedfee;
    }

    @Schema(description = "운송계획 확정 확인")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentPlanFixPartnerDTO {

        @Schema(description = "협력업체키")
        private String ptnrkey;

        @Schema(description = "협력업체 명칭")
        private String ptnamlc;

        @Schema(description = "협력업체 타입")
        private PartnerTypeEnum ptnrtyp;

        @Schema(description = "단독 차량수")
        private Integer vehforftl;

        @Schema(description = "혼적 차량수")
        private Integer vehforltl;

        @Schema(description = "운송필요차량")
        private Integer totalveh;

        @Schema(description = "Shipment 리스트")
        private List<ShipmentPlanFixShipmentDTO> shipmentList;
    }

    @Schema(description = "운송계획 확정 확인")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentPlanFixPrevDTO {

        @Schema(description = "일자")
        private LocalDate shpmtdt;

        @Schema(description = "회차")
        private Integer roundno;

        @Schema(description = "운송계획번호")
        private String shpplky;

        @Schema(description = "Partner 리스트")
        private List<ShipmentPlanFixPartnerDTO> partnerList;
    }

    @Schema(description = "운송계획결과정보 변경이력")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentPlanOrderHistoryDTO {

        @Schema(description = "변경ID")
        private Long changid;

        @Schema(description = "Service Client")
        private String compkey;

        @Schema(description = "운송주문번호")
        private String oetmsky;

        @Schema(description = "변경일시")
        private LocalDateTime changat;

        @Schema(description = "운송오더 상태 (구): " + OrderStatusEnum.COLUMN_COMMENT)
        private OrderStatusEnum tshitsto;

        @Schema(description = "운송오더 상태 (신): " + OrderStatusEnum.COLUMN_COMMENT)
        private OrderStatusEnum tshitstn;

        @Schema(description = "To고객 도착예정일 (구)")
        private LocalDate uldrqdto;

        @Schema(description = "To고객 도착예정일 (신)")
        private LocalDate uldrqdtn;

        @Schema(description ="운송화물방식 (OLD)")
        private TruckLoadTypeEnum cargotyo;

        @Schema(description ="운송화물방식 (NEW)")
        private TruckLoadTypeEnum cargotyn;

        @Schema(description = "포장형태 (구): " + PackageCategoryEnum.COLUMN_COMMENT)
        private PackageCategoryEnum opkcateo;

        @Schema(description = "포장형태 (신): " + PackageCategoryEnum.COLUMN_COMMENT)
        private PackageCategoryEnum opkcaten;

        @Schema(description = "포장타입 (구): " + PackageTypeEnum.COLUMN_COMMENT)
        private PackageTypeEnum opktypeo;

        @Schema(description = "포장타입 (신): " + PackageTypeEnum.COLUMN_COMMENT)
        private PackageTypeEnum opktypen;

        @Schema(description = "포장ONE-SIDE 타입 (구)")
        private String onsidtyo;

        @Schema(description = "포장ONE-SIDE 타입 (신)")
        private String onsidtyn;

        @Schema(description = "팔레트 박스 수량 (구)")
        private Integer opakqtyo;

        @Schema(description = "팔레트 박스 수량 (신)")
        private Integer opakqtyn;

    }

    // 납품정보변경 관련 DTO

    @Schema(description = "납품정보변경 아이템")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentChangedOetmskyDTO {

        @Schema(description = "변경상태 (삭제,추가,변경)")
        private OrderStatusEnum tshitst;

        @Schema(description = "Shipment No.")
        private String shpmtky;

        @Schema(description = "운송주문번호")
        private String oetmsky;

        @Schema(description = "상태 (추가,삭제,변경)")
        private ShipmentChangeStatusEnum orderStatus;

        @Schema(description = "납품예정일")
        private LocalDate shpmtdt;

        @Schema(description = "운송화물방식")
        private TruckLoadTypeEnum cargoty;

        @Schema(description = "포장형태")
        private PackageCategoryEnum opkcate;

        @Schema(description = "포장타입")
        private PackageTypeEnum opktype;

        @Schema(description = "포장ONE-SIDE 타입")
        private String onsidty;

        @Schema(description = "팔레트 박스 수량")
        private Integer opakqty;

        @Schema(description = "변경이력")
        private ShipmentPlanOrderHistoryDTO itemDetail;
    }

    @Schema(description = "납품정보변경 아이템")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentChangedPartnerDTO {

        @Schema(description = "협력업체키")
        private String ptnrkey;

        @Schema(description = "협력업체 명칭")
        private String ptnamlc;

        @Schema(description = "협력업체 타입")
        private PartnerTypeEnum ptnrtyp;

        @Schema(description = "납품정보변경 Item 리스트")
        List<ShipmentChangedOetmskyDTO> oetmskyList;
    }

    @Schema(description = "납품정보변경 리스트")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentChangedListDTO {

        @Schema(description = "D-day")
        private Integer dday;

        @Schema(description = "일자")
        private LocalDate shpmtdt;

        @Schema(description = "회차")
        private Integer roundno;

        @Schema(description = "운송계획번호")
        private String shpplky;

        @Schema(description = "납품정보변경 파트너 리스트")
        List<ShipmentChangedPartnerDTO> partnerList;
    }

    @Schema(description = "운송계획 납품정보변경안내")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentPlanChangedDTO {
        private LocalDate today;
        private LocalDate endday;
        private Boolean isChanged;

        @Schema(description = "Service Client")
        private String compkey;

        @Schema(description = "리스트")
        List<ShipmentChangedListDTO> changedList;
    }

    @Schema(description = "운송계획 삭제결과")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentPlanDeletedDTO {
        @Schema(description = "message")
        private String message;

        private Integer errorCode;

        private LocalDate uldrqdt;

        private Integer roundno;

        private String shpplky;
    }

    @Schema(description = "운송계획 확정결과")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentPlanFixDTO {
        @Schema(description = "message")
        private String message;

        private Integer errorCode;

        private String shpplky;
    }

    @Schema(description = "운송계획 납품정보변경 DUMMY")
    @Getter
    public static class ShipmentPlanChangedDummyDTO {

        @Schema(description = "일자")
        private LocalDate shpmtdt;

        @Schema(description = "회차")
        private Integer roundno;

        @Schema(description = "운송계획번호")
        private String shpplky;

        @Schema(description = "협력업체키")
        private String ptnrkey;

        @Schema(description = "협력업체 명칭")
        private String ptnamlc;

        @Schema(description = "협력업체 타입")
        private PartnerTypeEnum ptnrtyp;

        @Schema(description = "Shipment No.")
        private String shpmtky;

        @Schema(description = "운송화물방식")
        private TruckLoadTypeEnum cargoty;

        @Schema(description = "운송오더 상태")
        private OrderStatusEnum tshitst;

        @Schema(description = "포장형태")
        private PackageCategoryEnum opkcate;

        @Schema(description = "포장타입")
        private PackageTypeEnum opktype;

        @Schema(description = "포장ONE-SIDE 타입")
        private String onsidty;

        @Schema(description = "팔레트 박스 수량")
        private Integer opakqty;

        @Schema(description = "운송주문번호")
        private String oetmsky;

        @Schema(description = "변경여부")
        private Boolean isupdate;

        @Schema(description = "변경일시")
        private LocalDateTime changat;

        @Schema(description = "운송오더 상태 (구): " + OrderStatusEnum.COLUMN_COMMENT)
        private OrderStatusEnum tshitsto;

        @Schema(description = "운송오더 상태 (신): " + OrderStatusEnum.COLUMN_COMMENT)
        private OrderStatusEnum tshitstn;

        @Schema(description = "To고객 도착예정일 (구)")
        private LocalDate uldrqdto;

        @Schema(description = "To고객 도착예정일 (신)")
        private LocalDate uldrqdtn;

        @Schema(description ="운송화물방식 (OLD)")
        private TruckLoadTypeEnum cargotyo;

        @Schema(description ="운송화물방식 (NEW)")
        private TruckLoadTypeEnum cargotyn;

        @Schema(description = "포장형태 (구): " + PackageCategoryEnum.COLUMN_COMMENT)
        private PackageCategoryEnum opkcateo;

        @Schema(description = "포장형태 (신): " + PackageCategoryEnum.COLUMN_COMMENT)
        private PackageCategoryEnum opkcaten;

        @Schema(description = "포장타입 (구): " + PackageTypeEnum.COLUMN_COMMENT)
        private PackageTypeEnum opktypeo;

        @Schema(description = "포장타입 (신): " + PackageTypeEnum.COLUMN_COMMENT)
        private PackageTypeEnum opktypen;

        @Schema(description = "포장ONE-SIDE 타입 (구)")
        private String onsidtyo;

        @Schema(description = "포장ONE-SIDE 타입 (신)")
        private String onsidtyn;

        @Schema(description = "팔레트 박스 수량 (구)")
        private Integer opakqtyo;

        @Schema(description = "팔레트 박스 수량 (신)")
        private Integer opakqtyn;

        @Override
        public String toString() {
            return
                "\n ShipmentPlanChangedDummyDTO { \n" +
                        "\t shpmtdt = " + shpmtdt + " \n" +
                        "\t roundno = " + roundno + " \n" +
                        "\t shpplky = " + shpplky + " \n" +
                        "\t ptnrkey = " + ptnrkey + " \n" +
                        "\t shpmtky = " + shpmtky +  " \n" +
                        "\t oetmsky = " + oetmsky +  " \n" +
                " }";
        }
    }

    @Schema(description = "운송계획 납품정보변경 SIMPLE")
    @Getter
    public static class ShipmentPlanChangedSimpleDTO {

        @Schema(description = "일자")
        private LocalDate shpmtdt;

        @Schema(description = "회차")
        private Integer roundno;

        @Schema(description = "운송계획번호")
        private String shpplky;

        @Schema(description = "운송주문번호")
        private String oetmsky;

        @Schema(description = "운송오더 상태 (구): " + OrderStatusEnum.COLUMN_COMMENT)
        private OrderStatusEnum tshitsto;

        @Schema(description = "운송오더 상태 (신): " + OrderStatusEnum.COLUMN_COMMENT)
        private OrderStatusEnum tshitstn;

        @Schema(description ="운송화물방식 (OLD)")
        private TruckLoadTypeEnum cargotyo;

        @Schema(description ="운송화물방식 (NEW)")
        private TruckLoadTypeEnum cargotyn;

        @Schema(description = "팔레트 박스 수량 (구)")
        private Integer opakqtyo;

        @Schema(description = "팔레트 박스 수량 (신)")
        private Integer opakqtyn;

        private LocalDate lodrqdto;
        private LocalDate lodrqdtn;
        private LocalTime lodrqtmo;
        private LocalTime lodrqtmn;
        private LocalDate uldrqdto;
        private LocalDate uldrqdtn;
        private LocalTime uldrqtmo;
        private LocalTime uldrqtmn;

        private String onsidtyo;
        private String onsidtyn;
        private PackageCategoryEnum opkcateo;
        private PackageCategoryEnum opkcaten;
        private PackageTypeEnum opktypeo;
        private PackageTypeEnum opktypen;
        private OrderVehicleTonEnum opvhtono;
        private OrderVehicleTonEnum opvhtonn;

        private Integer pakdepto;
        private Integer pakdeptn;
        private Integer pakheigo;
        private Integer pakheign;
        private Integer pakwidho;
        private Integer pakwidhn;
        private BigDecimal pkrweigo;
        private BigDecimal pkrweign;

        private LocalDateTime changat;
        private String crudmod;
        private LocalDate credate;
        private LocalTime cretime;
        private LocalDate lmodate;
        private LocalTime lmotime;

        @Schema(description = "Shipment No.")
        private String shpmtky;
    }

    @Schema(description = "운송계획 납품정보변경 CHECK")
    @Getter
    @Setter
    public static class ShipmentPlanIsChangedDAO {
        @Schema(description = "변경 여부")
        private Boolean isChanged;

        @Schema(description = "일자")
        private LocalDate shpmtdt;

        @Schema(description = "회차")
        private Integer roundno;
    }

    @Schema(description = "운송계획 납품정보변경 SIMPLE")
    @Getter
    @Setter
    public static class ShipmentPlanChangedTargetDTO {

        @Schema(description = "Message")
        private String msg;

        @Schema(description = "변경 Tooltip")
        private Boolean showTooltip;

        @Schema(description = "리스트")
        private List<ShipmentTargetCountDTO> targetList;
    }

    @Schema(description = "LoadOptimizer TruckCapacity")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class LoadOptimizerTruckCapacityDTO {
        private Integer CapStandard; // 갯수
        private Integer CapNonStandard; // mm 단위
        private Integer CapOneSide; // mm 단위
        private String TruckTon;
        private String TruckStyle;
    }

    @Schema(description = "운송Section정보변경결과")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentOetmskyChangeOldInfo {
        private Boolean updated;
        private String oetmsky;
        private String shipmentKey;
        private List<String> shpsckys;
    }

    @Schema(description = "운송정보 OETMSKY")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentOetmskyPairDTO {
        private String shpmtky;
        private String oetmsky;
    }

    @Schema(description = "운송주문정보 OETMSKY")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentOetmsInfo {
        private String oetmsky;
        private String oneShpplky;
        private List<String> shpplkys;
        private List<String> shpmtkys;
        private List<String> shpsckys;
    }

    @Schema(description = "운송정보변경 OETMSKY")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentChangePlanOetmskyPairDTO {
        private String orgShpplky;
        private String orgShpmtky;
        private String oetmsky;
        private ShipmentOetmsInfo oetmsShipInfo;
        private List<ShipmentOetmskyPairDTO> pairList;
    }

    @Schema(description = "운송구간정보 Two")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentSectionTwoDTO {
        private String shpl;
        private String shmt;
        private String oetmsky;
        private List<String> shscList;
    }

    @Schema(description = "운송구간정보 One")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentSectionOneDTO {
        private String shpl;
        private String shmt;
        private String shsc;
        private String oetmsky;
    }

    @Schema(description = "운송계획 삭제 Info")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentPlanDeletedInfoDTO {
        private String shpplky;
        private List<String> shpmtkyList;
        private List<String> oetmskyValues;
        private List<ShipmentSectionOneDTO> oetmskyManualList;
    }

    @Schema(description = "운송계획대상정보 DUMMY")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentTargetCountDummyDAO {

        @Schema(description = "Service Client", example = TmsConstant.DEFAULT_COMPKEY)
        private String compkey;

        @Schema(description = "상하차지 고객")
        private String custkey;

        @Schema(description = "상하차지 키")
        private String destkey;

        @Schema(description = "상하차지 고객")
        private String ptnrkey;

        @Schema(description = "상하차지 키")
        private String loadkey;

        @Schema(description = "상하차지명")
        private String denamlc;

        @Schema(description = "협력업체 타입")
        private PartnerTypeEnum ptnrtyp;

        @Schema(description = "to고객/협력업체 명칭")
        private String ptnamlc;

        @Schema(description = "운송방식(단,혼적)")
        private TruckLoadTypeEnum cargoty;

        @Schema(description = "상차 요청일")
        private LocalDate lodrqdt;

        @Schema(description = "상차 요청시간")
        private LocalTime lodrqtm;

        @Schema(description = "하차 요청일")
        private LocalDate uldrqdt;

        @Schema(description = "하차 요청시간")
        private LocalTime uldrqtm;

        @Schema(description = "운송오더상태")
        private OrderStatusEnum tshitst;

        @Schema(description = "Shipment No.")
        private String tmshpno;

        @Schema(description = "수동여부")
        private Integer manuvhp;

        @Schema(description = "긴급운송여부")
        private Boolean turgtyn;

    }

    @Getter
    @Setter
    public static class ShipmentSimpleInfoPlanDAO {
        private String compkey;
        private String shpplky;
        private String ptnrkey; // plan ptnrkey
        private LocalDate shpmtdt;
        private Integer roundno;
        private String shpmtky;
        private Boolean mtmanlshp;
        private LocalDateTime pldeletat;
        private LocalDateTime mtdeletat;
    }

    @Getter
    @Setter
    public static class ShipmentTestArgsDTO {
        private String compkey;
        private String shpplky;
        private String ptnrkey; // plan ptnrkey
        private LocalDate shpmtdt;
        private Integer roundno;
        private String shpmtky;
        private String oetmsky;
    }

    @Schema(description = "운송계획 삭제 가능 결과")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class AvailableDeletePlanDTO {
        private Boolean availableDelete;
        private LocalDate fixedDate;
        private Integer fixedRound;
        private String shpplky;
        private Integer deletedPlanCount;
    }

    @Schema(description = "LocalDate and LocalTime Pair")
    @Setter
    @Getter
    @AllArgsConstructor
    @SuperBuilder
    public static class PickupDropoffDateAndTime {
        private LocalDate puDate;
        private LocalTime puTime;
        private LocalDate dfDate;
        private LocalTime dfTime;
    }

    @Schema(description = "운송계획 Date Param")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentPlan2DateTimesDTO {
        private LocalDate oldPuDate; // 이전 상차 일
        private LocalTime oldPuTime; // 이전 상차 시
        private LocalDate oldDfDate; // 이전 하차 일
        private LocalTime oldDfTime; // 이전 하차 시
        private LocalDate newPuDate; // 변경 상차 일
        private LocalTime newPuTime; // 변경 상차 시
        private LocalDate newDfDate; // 변경 하차 일
        private LocalTime newDfTime; // 변경 하차 시
        private String oetmsky;
    }

    @Schema(description = "ShipmentPlan DateRound Info")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentPlanDateRoundInfo {
        private LocalDate shpmtdt;
        private Integer roundno;
    }

    @Setter
    @Getter
    public static class SimpleParams {
        private String str1;
        private String str2;
        private String str3;
        private Integer int1;
        private Integer int2;
        private Integer int3;
        private LocalDate date1;
        private LocalDate date2;
        private LocalTime time1;
        private LocalTime time2;
        private LocalDateTime dtime1;
        private LocalDateTime dtime2;
    }

    @Schema(description = "운송계획 정보")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentPlanSimpleInfo {
        private String compkey;
        private String shpplky;
        private String ptnrkey;
        private LocalDate shpmtdt;
        private Integer roundno;
        private SimulationOptionEnum simulop;
        private LocalDateTime simulat;
        private LocalDateTime fixedat;
        private Boolean showTooltip;
        private Boolean manlshp;
    }

    // ----------------------------------------------------
    // --- static Functions
    // ----------------------------------------------------

}
