package com.logistics.tms.shipment.dto;

import com.logistics.tms.common.enumeration.PartnerTypeEnum;
import com.logistics.tms.common.enumeration.ShipmentVehicleTonEnum;
import com.logistics.tms.common.enumeration.TruckLoadTypeEnum;
import com.logistics.tms.common.enumeration.VehicleTypeEnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Getter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;


@Getter
@Builder
public class ShipmentDTO {
    @Schema(description = "Service Client")
    private String compkey;

    @Schema(description = "Shipment No.")
    private String shpmtky;

    private List<ShipmentSectionDTO> shipmentSectionDTOList;

    @Schema(description = "시뮬레이션시작기준일시")
    private LocalDateTime simulat;

    @Schema(description = "확정일시")
    private LocalDateTime fixedat;

    @Schema(description = "수동여부")
    private Boolean manlshp;

    @Schema(description = "차량톤수")
    private ShipmentVehicleTonEnum vhctncd;

    @Schema(description = VehicleTypeEnum.COLUMN_COMMENT, example = VehicleTypeEnum.EXAMPLE)
    private VehicleTypeEnum vehicleType; // 차량종류

    @Schema(description = "단독/혼적")
    private TruckLoadTypeEnum cargoty;

    @Schema(description = "적재율")
    private Integer loadrat;

    @Schema(description = "용차 섭외 금액")
    private Integer vehcost;

    @Schema(description = "총운송비용")
    private Integer totalcost;

    @Schema(description = "예상운임비")
    private Integer estimatedfee;

    @Schema(description = "도착 협력업체키")
    private String ptnrkey;

    @Schema(description = "도착 협력업체 명칭")
    private String ptnamlc;

    @Schema(description = "도착 협력업체 타입")
    private PartnerTypeEnum ptnrtyp;

    @Schema(description = "기타운임")
    private Integer othechg;

    @Schema(description = "기타운임사유")
    private String othersn;

    @Schema(description = "삭제일시")
    private LocalDateTime deletat;

    @Schema(description = "마감 여부 (y/n)")
    private String closlyn;

    @Schema(description = "마감 일자 (yyyymmdd 형식)")
    private String clodate;

    @Schema(description = "마감 시간 (hhmmss 형식)")
    private String clotime;

    @Schema(description = "마감 사용자 (담당자 id 또는 이름)")
    private String clouser;

    @Schema(description = "마감 번호 (마감을 식별하는 고유 번호)")
    private String closeno;

    @Schema(description = "대체가능차량톤수")
    private ShipmentVehicleTonEnum repVhctn;

    @Schema(description = "자동배차기사")
    private Long autoDrvId;

    @Schema(description = "자동배차차량")
    private Long autoVhcId;

    @Schema(description = "운송완료일자", example = "2024-10-08")
    private LocalDate completedDate;

    @Schema(description = "영업일자 - 운송완료일자", example = "2024-10-08")
    private LocalDate postdat;
}
