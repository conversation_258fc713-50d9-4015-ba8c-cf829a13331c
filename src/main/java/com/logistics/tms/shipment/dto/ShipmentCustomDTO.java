package com.logistics.tms.shipment.dto;

import com.logistics.tms.common.constant.TmsConstant;
import com.logistics.tms.common.enumeration.OrderStatusEnum;
import com.logistics.tms.common.enumeration.PackageCategoryEnum;
import com.logistics.tms.common.enumeration.PackageTypeEnum;
import com.logistics.tms.common.enumeration.PartnerTypeEnum;
import com.logistics.tms.common.enumeration.ShipmentVehicleTonEnum;
import com.logistics.tms.common.enumeration.TruckLoadTypeEnum;
import com.logistics.tms.common.enumeration.VehicleStatusEnum;
import com.logistics.tms.common.enumeration.VehicleTypeEnum;
import com.logistics.tms.common.enumeration.VisitTypeEnum;
import com.logistics.tms.loadingoptimizationmanager.feign.type.Position;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.time.LocalTime;
import java.math.BigDecimal;
import java.util.List;


public class ShipmentCustomDTO {

    @Schema(description = "운송계획결과정보 적재정보 Item")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentLoadingItemDTO {

        @Schema(description = "상차지 주소")
        private String puaddr;

        @Schema(description = "하차지 주소")
        private String dfaddr;

        @Schema(description = "상차 협력업체 명칭")
        private String puptnamlc;

        @Schema(description = "하차 협력업체 명칭")
        private String dfptnamlc;

        @Schema(description = "상차지 명칭")
        private String pudenamlc;

        @Schema(description = "하차지 명칭")
        private String dfdenamlc;

        @Schema(description = "도착지 key")
        private String destkey;

        @Schema(description = "운송주문번호")
        private String oetmsky;

        @Schema(description = "포장형태")
        private PackageCategoryEnum opkcate;

        @Schema(description = "포장타입")
        private PackageTypeEnum opktype;

        @Schema(description = "포장one-side 타입")
        private String onsidty;

        @Schema(description = "팔레트 박스 수량")
        private Integer opakqty;

        @Schema(description = "중량")
        private BigDecimal pkrweig;

        @Schema(description = "가로 mm")
        private Integer pakwidh;

		@Schema(description = "세로 mm")
        private Integer pakdept;

        @Schema(description = "높이 mm")
        private Integer pakheig;

        @Schema(description = "가로 unit")
        private Integer unitwidh;

		@Schema(description = "세로 unit")
        private Integer unitdept;

        private List<Position> positions;

        private Integer puvstordr;
        private Integer dfvstordr;

        //// [향후 삭제 예정]
        @Schema(description = "팔레트 위치 (Y) mm")
        private Integer loadyplt;

        @Schema(description = "팔레트 위치 (X) mm")
        private Integer loadxplt;

        @Schema(description = "팔레트 위치 (Y) unit")
        private Integer loadyunit;

        @Schema(description = "팔레트 위치 (X) unit")
        private Integer loadxunit;

        @Schema(description = "팔레트 unit 단위 깊이")
        private Integer loadUnitDepth;

        @Schema(description = "팔레트 unit 단위 폭")
        private Integer loadUnitWidth;
        //// ~ [향후 삭제 예정]

    }

    @Schema(description = "운송계획결과정보 적재정보")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentLoadingInfoDTO {

        @Schema(description = "Service Client", example = TmsConstant.DEFAULT_COMPKEY)
        private String compkey;

        @Schema(description = "Shipment No.")
        private String shpmtky;

        @Schema(description = "운송화물방식: " + TruckLoadTypeEnum.COLUMN_COMMENT)
        private TruckLoadTypeEnum cargoty;
 
        @Schema(description = "차량톤수")
        private ShipmentVehicleTonEnum vhctncd;

        @Schema(description = VehicleTypeEnum.COLUMN_COMMENT, example = VehicleTypeEnum.EXAMPLE)
        private VehicleTypeEnum vehicleType;

        @Schema(description = "적재물정보")
        private List<ShipmentLoadingItemDTO> itemList;
    }

    @Schema(description = "운송계획결과정보 적재정보 Dummy")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentLoadingDummyDTO {

        @Schema(description = "Service Client", example = TmsConstant.DEFAULT_COMPKEY)
        private String compkey;

        @Schema(description = "Shipment No.")
        private String shpmtky;

        @Schema(description = "운송화물방식: " + TruckLoadTypeEnum.COLUMN_COMMENT)
        private TruckLoadTypeEnum cargoty;
 
        @Schema(description = "차량톤수")
        private ShipmentVehicleTonEnum vhctncd;

        @Schema(description = VehicleTypeEnum.COLUMN_COMMENT)
        private VehicleTypeEnum vehicleType;

        @Schema(description = "상차지 주소")
        private String puaddr;

        @Schema(description = "하차지 주소")
        private String dfaddr;

        @Schema(description = "상차 협력업체 명칭")
        private String puptnamlc;

        @Schema(description = "하차 협력업체 명칭")
        private String dfptnamlc;

        @Schema(description = "상차지 명칭")
        private String pudenamlc;

        @Schema(description = "하차지 명칭")
        private String dfdenamlc;

        @Schema(description = "도착지 key")
        private String destkey;

        @Schema(description = "운송주문번호")
        private String oetmsky;

        @Schema(description = "포장형태")
        private PackageCategoryEnum opkcate;

        @Schema(description = "포장타입")
        private PackageTypeEnum opktype;

        @Schema(description = "포장one-side 타입")
        private String onsidty;

        @Schema(description = "팔레트 박스 수량")
        private Integer opakqty;

        @Schema(description = "중량")
        private BigDecimal pkrweig;

        @Schema(description = "가로")
        private Integer pakwidh;

        @Schema(description = "세로")
        private Integer pakdept;

        @Schema(description = "높이")
        private Integer pakheig;
 
        private Integer puvstordr;
        private Integer dfvstordr;

        @Schema(description = "팔레트 위치 (Y) mm")
        private Integer loadyplt;

        @Schema(description = "팔레트 위치 (X) mm")
        private Integer loadxplt;

        @Schema(description = "팔레트 위치 (Y) unit")
        private Integer loadyunit;

        @Schema(description = "팔레트 위치 (X) unit")
        private Integer loadxunit;

        @Schema(description = "팔레트 unit 단위 깊이")
        private Integer loadUnitDepth;
        
        @Schema(description = "팔레트 unit 단위 폭")
        private Integer loadUnitWidth;
        
    }

    @Schema(description = "운송계획결과정보 Shipment No.정보")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentTrackInfoDTO {

        @Schema(description = "Service Client", example = TmsConstant.DEFAULT_COMPKEY)
        private String compkey;

        @Schema(description = "Shipment No.")
        private String shpmtky;

        @Schema(description = "운송화물방식: " + TruckLoadTypeEnum.COLUMN_COMMENT)
        private TruckLoadTypeEnum cargoty;

        @Schema(description = "상차 협력업체 명칭 리스트")
        private String puptnamlc;

        @Schema(description = "하차 협력업체 명칭 리스트")
        private String dfptnamlc;

        @Schema(description = "상차지 명칭")
        private String pudenamlc;

        @Schema(description = "하차지 명칭")
        private String dfdenamlc;

        @Schema(description = "적재율")
        private Integer loadrat;
    
        @Schema(description = "총소요시간")
        private Integer totalesttime;

        @Schema(description = "예상 운임비(VAT 포함)")
        private Integer estimatedfee;

        @Schema(description = "상하차 리스트")
        private List<ShipmentSectionSimpleTrackDTO> shipmentSectionList;

        List<String> orgPuNames;
        List<String> orgDfNames;
        List<String> orgAllNames;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentAllTrackInfoDTO {
        private String shpplky;
        private LocalDate uldrqdt;
        private Integer roundno;
        private String ptnrkey;
        private List<ShipmentTrackInfoDTO> trackInfos;
    }

    @Getter
    @Setter
    @SuperBuilder
    public static class ShipmentSectionSimpleTrackDTO {

        @Schema(description = "상/하차")
        private VisitTypeEnum vsttype;

        @Schema(description = "협력업체 명칭")
        private String ptnamlc;

        @Schema(description = "협력업체 타입")
        private PartnerTypeEnum ptnrtyp;

        @Schema(description = "운행순서")
        private Integer vstordr;

        @Schema(description = "도착지 명칭")
        private String denamlc;

        @Schema(description = "상하차 요청일")
        private LocalDate ludrqdt;

        @Schema(description = "상하차 요청시각")
        private LocalTime ludrqtm;

        @Schema(description = "상하차 예상소요시간(분)")
        @Builder.Default
        private Integer ludptim = 0;

        @Schema(description = "적재율")
        @Builder.Default
        private Integer loadrat = 0;

        @Schema(description = "예상운행시간")
        @Builder.Default
        private Integer estsecs = 0;
   
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CarrierInfoDTO {
        private String compkey;
        private String useract;
        private String usertyp;
        private String ptnrkey;
    }

    @Getter
    @Setter
    public static class OetmhdSimpleDTO {
        private String oetmsky;
        private String ptnrkey;
        private String loadkey;
        private String custkey;
        private String destkey;
        private OrderStatusEnum tshitst;
        private VehicleStatusEnum trnstat;
        private Integer manuvhp;
        private PackageCategoryEnum opkcate;
        private PackageTypeEnum opktype;
        private String onsidty;
        private Integer opakqty;
        private TruckLoadTypeEnum cargoty;
        private Integer pakwidh;
        private Integer pakdept;
        private Integer pakheig;
        private BigDecimal pkrweig;
        private LocalDate lodrqdt;
        private LocalTime lodrqtm;
        private LocalDate uldrqdt;
        private LocalTime uldrqtm;
        private String tmshpno;
        private Boolean turgtyn; // 긴급운송여부
        private String vhctncd;

        private Integer stdhfee;		//기본운임
        private Integer dishrat;		//운임할증
        private Integer othechg;		//기타운임
        private Integer tdlvfee;		//토탈 청구금액
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentSimpleInfoDTO {
        @Schema(description = "Service Client")
        private String compkey;

        @Schema(description = "Shipment No.")
        private String shpmtky;

        @Schema(description = "운송계획 번호")
        private String shpplky;
    }

}
