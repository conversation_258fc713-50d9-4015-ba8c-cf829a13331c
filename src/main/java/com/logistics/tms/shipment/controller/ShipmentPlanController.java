package com.logistics.tms.shipment.controller;

import com.logistics.tms.common.enumeration.SimulationOptionEnum;
import com.logistics.tms.common.enumeration.VisitTypeEnum;
import com.logistics.tms.common.util.AuthUtils;
import com.logistics.tms.configuration.error.ShipmentException;
import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.configuration.security.AuthUser;
import com.logistics.tms.dispatch.service.DispatchPlanService;
import com.logistics.tms.external.dto.OetmhdTmsInfoDTO;
import com.logistics.tms.external.service.OetmhdService;
import com.logistics.tms.shipment.constant.ShipmentConstant;
import com.logistics.tms.shipment.dto.*;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentResultDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentTargetResultCountDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentTestArgsDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.SimpleParams;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.ShipmentAllTrackInfoDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.AvailableDeletePlanDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentMapResultDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlan2DateTimesDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanChangedDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanChangedTargetDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanCheckMadeDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanDeletedDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanFixDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanDeletedInfoDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanFixPrevDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanMakeDetailDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanMakeDummyDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanMakeResponseDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanSimpleInfo;
import com.logistics.tms.shipment.entity.Shipment;
import com.logistics.tms.shipment.entity.ShipmentPlan;
import com.logistics.tms.shipment.entity.ShipmentSection;
import com.logistics.tms.shipment.interfaces.ShipmentPlanSwagger;
import com.logistics.tms.shipment.service.ShipmentPlanService;

import com.logistics.tms.shipment.service.ShipmentService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Objects;
import java.util.stream.Collectors;
import java.time.LocalDate;
import java.util.List;

@Slf4j
@Validated
@RestController
@RequestMapping(ShipmentConstant.SHIPMENT_PLAN_ROOT_URL)
@RequiredArgsConstructor
public class ShipmentPlanController implements ShipmentPlanSwagger {

    private final ShipmentPlanService shipmentPlanService;
    private final DispatchPlanService dispatchPlanService;
    private final OetmhdService oetmhdService;

    @Override
    @GetMapping(path = "/targets", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<ShipmentTargetDTO> getShipmentTargetOne(@RequestParam @NotBlank final String compkey,
                                                        @RequestParam @NotNull final LocalDate uldrqdt,
                                                        @RequestParam @NotNull final Integer roundno,
                                                        @RequestParam @NotNull final VisitTypeEnum visityp,
                                                        @RequestParam @NotBlank final String ptnrkey,
                                                        @RequestParam @NotBlank final String destkey) {

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        final String _CompKey = Objects.requireNonNull(authDTO).getCompany();
        log.info("targets: {} / {} / {} / {}", _CompKey, Objects.requireNonNull(authDTO).getId(), uldrqdt, roundno);
        return shipmentPlanService.getShipmentTargetOne(authDTO, _CompKey,
                uldrqdt,
                roundno,
                visityp,
                ptnrkey,
                destkey);
    }


    @Override
    @GetMapping(path = "/targets/count", produces = MediaType.APPLICATION_JSON_VALUE)
    public ShipmentPlanChangedTargetDTO getShipmentTargetCount(@RequestParam @NotBlank final String compkey,
                                                               @RequestParam @NotNull final LocalDate uldrqdt,
                                                               @RequestParam @NotNull final Integer roundno,
                                                               @RequestParam @NotNull final VisitTypeEnum visityp,
                                                               @Nullable final Boolean changedflag,
                                                               @Nullable final LocalDate changedshpmtdt,
                                                               @Nullable final Integer changedroundno
                                                               ) {

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        final String _CompKey = Objects.requireNonNull(authDTO).getCompany();
        log.info("targets/count: {} / {} / {} / {}", _CompKey, Objects.requireNonNull(authDTO).getId(), uldrqdt, roundno);
        return shipmentPlanService.getShipmentTargetCount(authDTO, _CompKey,
                uldrqdt,
                roundno,
                visityp,
                changedflag, // changedshpmtdt, changedroundno,
                false);
    }


    @Hidden
    @PostMapping(path = "/simulation", produces = MediaType.APPLICATION_JSON_VALUE)
    public ShipmentPlanMakeResponseDTO makeShipmentPlan(@RequestParam @NotBlank final String compkey,
                                                        @RequestParam @NotNull final LocalDate uldrqdt,
                                                        @RequestParam @NotNull final Integer roundno,
                                                        @Nullable SimulationOptionEnum simulop) {

        if (simulop == null) simulop = SimulationOptionEnum.DESIGNATED;
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        final String _CompKey = Objects.requireNonNull(authDTO).getCompany();
        String _PartnerKey = authDTO.getPartnerKey();

        ShipmentPlanMakeResponseDTO responseMake = new ShipmentPlanMakeResponseDTO();

        /*
        List<String> oldShpmtkyList = shipmentPlanService.getShipmentListByDateRound(authDTO, uldrqdt, roundno);
        if ( oldShpmtkyList != null && oldShpmtkyList.size() > 0 ) {
            // Boolean isCheck = true 이면 chekc만 하고, isCheck = false 면 실제 DB 에서 지우도록 하였습니다.
            Boolean isCheck = true;
            Boolean bRet = dispatchPlanService.cancelDispatch(oldShpmtkyList, isCheck);
            if ( bRet == false ) {
                responseMake.setError("배차완료된 운송계획은 삭제할 수 없습니다.");
                return responseMake; // comment LOCALTEST
            }
        }
        */

        ShipmentPlanMakeDummyDTO retMake = shipmentPlanService.makeShipmentPlan(authDTO,
                                _CompKey, authDTO.getId(), _PartnerKey, uldrqdt, roundno, simulop);

        retMake = shipmentPlanService.saveShipmentPlan(authDTO, _CompKey, retMake);
        retMake = shipmentPlanService.saveShipmentOmsItem(authDTO, _CompKey, retMake);

        shipmentPlanService.updateOetmhdTmshpno(retMake.getShipPlan(), retMake.getOetmskyValues());
        shipmentPlanService.updateOetmskyShipmentKey(retMake.getManualPairList());
        //shipmentPlanService.deleteShipmentOmsItems(retMake.getShpmtkyList());

        ShipmentPlanMakeDetailDTO detail = new ShipmentPlanMakeDetailDTO();
        detail.setShpplky(retMake.getShpplky());
        detail.setShpmtkyCount(retMake.getShpmtkyCount());

        responseMake.setDetailShipPlan(detail);
        responseMake.setErrorMsg(retMake.getErrorMsg());
        log.info("Make ShipmentPlan, shpplky: {}", retMake.getShpplky());
        return responseMake;
    }

    @Override
    @PostMapping(path = "", produces = MediaType.APPLICATION_JSON_VALUE)
    public ShipmentPlanMakeResponseDTO makeShipmentPlanSimulation(@RequestParam @NotBlank final String compkey,
                                                                  @RequestParam @NotNull final LocalDate uldrqdt,
                                                                  @RequestParam @NotNull final Integer roundno,
                                                                  @Nullable SimulationOptionEnum simulop) {

        if (simulop == null) simulop = SimulationOptionEnum.DESIGNATED;
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        final String _CompKey = Objects.requireNonNull(authDTO).getCompany();
        String _PartnerKey = authDTO.getPartnerKey();

        ShipmentPlanMakeResponseDTO responseMake = new ShipmentPlanMakeResponseDTO();

        ShipmentPlanMakeDummyDTO retMake = shipmentPlanService.makeShipmentPlan(authDTO,
                _CompKey, authDTO.getId(), _PartnerKey, uldrqdt, roundno, simulop);

        retMake = shipmentPlanService.saveShipmentPlan(authDTO, _CompKey, retMake);
        retMake = shipmentPlanService.saveShipmentOmsItem(authDTO, _CompKey, retMake);

        ShipmentPlan retMakeShipPlan = retMake.getShipPlan();
        List<String> shipmentKeyList = new ArrayList<>();
        List<String> oetmskyList = new ArrayList<>();
        String shipmentPlanKey = null;
        if(retMakeShipPlan != null) {
            shipmentPlanKey = retMakeShipPlan.getShpplky();
            List<Shipment> shipmentList = retMakeShipPlan.getShipmentList();
            if(shipmentList != null) {
                for(Shipment shipment : shipmentList) {
                    shipmentKeyList.add(shipment.getShpmtky());
                    List<ShipmentSection> shipmentSectionList = shipment.getShipmentSectionList();
                    if(shipmentSectionList != null) {
                        for(ShipmentSection shipmentSection : shipmentSectionList) {
                            oetmskyList.add(shipmentSection.getOetmsky());
                        }
                    }
                }
            }
        }

        final List<String> updateOetmskyList = oetmskyList.stream()
                .distinct()
                .collect(Collectors.toList());
        log.info("makeShipmentPlanSimulation : shipmentPlanKey -> {}", shipmentPlanKey);
        log.info("makeShipmentPlanSimulation : shipmentKeyList -> {}", shipmentKeyList);
        log.info("makeShipmentPlanSimulation : updateOetmskyList -> {}", updateOetmskyList);

        shipmentPlanService.updateOetmhdTmshpno(retMake.getShipPlan(), updateOetmskyList);
        shipmentPlanService.updateOetmskyShipmentKey(retMake.getManualPairList());
        //shipmentPlanService.deleteShipmentOmsItems(shipmentKeyList);

        shipmentPlanService.clearDeletedShipments();

        ShipmentPlanMakeDetailDTO detail = new ShipmentPlanMakeDetailDTO();
        detail.setShpplky(shipmentPlanKey);
        detail.setShpmtkyCount(shipmentKeyList.size());

        responseMake.setDetailShipPlan(detail);
        responseMake.setErrorMsg(retMake.getErrorMsg());
        log.info("makeShipmentPlanSimulation : shpplky: {}", retMake.getShpplky());
        return responseMake;
    }


    @Override
    @PutMapping(path = "/fix", produces = MediaType.APPLICATION_JSON_VALUE)
    public ShipmentPlanFixDTO fixShipmentPlan(@RequestParam @NotBlank final String compkey,
                                              @RequestParam @NotBlank final String shpplky) {

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        final String _CompKey = Objects.requireNonNull(authDTO).getCompany();

        ShipmentPlanFixDTO response = new ShipmentPlanFixDTO();

        ShipmentPlan savedEntity = shipmentPlanService.fixShipmentPlan(authDTO, _CompKey, shpplky);
        if(Objects.isNull(savedEntity)) {
            log.info("fixShipmentPlan : 해당하는 운송계획이 존재하지 않습니다.");
            response.setErrorCode(2);
            response.setMessage("해당하는 운송계획이 존재하지 않습니다.");
            response.setShpplky(shpplky);
            return response;
        }
        List<String> oetmskyValues = shipmentPlanService.getOetmskyByShipmentPlan(savedEntity);
        shipmentPlanService.updateOetmhdTshitstPlanFix(oetmskyValues);
        shipmentPlanService.updateDispatchShipmentPlan(savedEntity);

        if ( savedEntity == null ) log.info("Cannot fix ShipmentPlan, shpplky: {}", shpplky);
        else log.info("Fix ShipmentPlan, shpplky: {}", shpplky);

        response.setErrorCode(0);
        response.setShpplky(shpplky);
        return response;
    }

    @Override
    @PutMapping(path = "/fix/shipment", produces = MediaType.APPLICATION_JSON_VALUE)
    public ShipmentPlanFixDTO fixSelectedShipmentPlan(@AuthUser final AuthDTO authDTO,
                                                      @RequestParam @NotNull final String shpplky,
                                                      @RequestParam @NotNull final List<String> shipmentList) {

        final String _CompKey = authDTO.getCompany();

        ShipmentPlanFixDTO response = new ShipmentPlanFixDTO();

        ShipmentPlan savedEntity = shipmentPlanService.fixShipmentPlanByShipment(_CompKey, shpplky, shipmentList);
        if(Objects.isNull(savedEntity)) {
            log.info("fixShipmentPlanByShipment : 해당하는 운송계획이 존재하지 않습니다.");
            response.setErrorCode(2);
            response.setMessage("해당하는 운송계획이 존재하지 않습니다.");
            response.setShpplky(shpplky);
            return response;
        }
        List<String> oetmskyValues = shipmentPlanService.getOetmskyByShipmentPlanByShipment(savedEntity, shipmentList);
        shipmentPlanService.updateOetmhdTshitstPlanFix(oetmskyValues);
        shipmentPlanService.updateDispatchShipmentPlanByShipmnet(savedEntity, shipmentList);

        if ( savedEntity == null ) log.info("Cannot fix ShipmentPlan, shpplky: {}", shpplky);
        else log.info("Fix ShipmentPlan, shpplky: {}", shpplky);

        response.setErrorCode(0);
        response.setShpplky(shpplky);
        return response;
    }

    @Override
    @GetMapping(path = "/fix-popup", produces = MediaType.APPLICATION_JSON_VALUE)
    public ShipmentPlanFixPrevDTO getFixPrevPopupShipmentPlan(@RequestParam @NotBlank final String compkey,
                                                              @RequestParam @NotBlank final String shpplky) {

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        final String _CompKey = Objects.requireNonNull(authDTO).getCompany();
        return shipmentPlanService.getFixPrevPopupShipmentPlan(authDTO, _CompKey, shpplky);
    }


    @Override
    @GetMapping(path = "/check-plan", produces = MediaType.APPLICATION_JSON_VALUE)
    public ShipmentPlanCheckMadeDTO getShipmentPlanCheckMade(@RequestParam @NotBlank final String compkey,
                                                             @RequestParam @NotNull final LocalDate uldrqdt,
                                                             @RequestParam @NotNull final Integer roundno
    ) {

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        final String _CompKey = Objects.requireNonNull(authDTO).getCompany();
        if ( ShipmentConstant.USE_CHANGE_DB_OETMHI ) {
            return shipmentPlanService.getShipmentPlan3CheckMade(authDTO, _CompKey, uldrqdt, roundno); // NEW CHANGEDEV
        } else {
            return shipmentPlanService.getShipmentPlanCheckMade(authDTO, _CompKey, uldrqdt, roundno); // OLD CHANGEDEV
        }
    }


    @Override
    @GetMapping(path = "", produces = MediaType.APPLICATION_JSON_VALUE)
    public ShipmentPlanDTO getShipmentPlan(@RequestParam @NotBlank final String compkey,
                                           @RequestParam @NotBlank final String shpplky
    ) {

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        final String _CompKey = Objects.requireNonNull(authDTO).getCompany();
        return shipmentPlanService.getShipmentPlan(authDTO, _CompKey, shpplky)
                .orElseThrow(() -> new ShipmentException(HttpStatus.NOT_FOUND, "해당하는 운송계획이 존재하지 않습니다."));
    }


    @Hidden
    @Override
    @GetMapping(path = "/page", produces = MediaType.APPLICATION_JSON_VALUE)
    public Page<ShipmentPlanDTO> getShipmentPlanPage(@RequestParam @NotBlank final String compkey,
                                                     @Nullable final Pageable pageable
    ) {

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        final String _CompKey = Objects.requireNonNull(authDTO).getCompany();
        return shipmentPlanService.getShipmentPlanPage(_CompKey, pageable);
    }


    @Override
    @GetMapping(path = "/result/count", produces = MediaType.APPLICATION_JSON_VALUE)
    public ShipmentTargetResultCountDTO apiGetShipmentResultCount(@RequestParam @NotBlank final String compkey,
                                                                  @RequestParam @NotNull final LocalDate uldrqdt,
                                                                  @RequestParam @NotNull final Integer roundno) {

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        final String _CompKey = Objects.requireNonNull(authDTO).getCompany();
        log.info("result/count: {} / {} / {} / {}", _CompKey, Objects.requireNonNull(authDTO).getId(), uldrqdt, roundno);
        // return shipmentPlanService.getShipmentResultCount(authDTO, _CompKey, uldrqdt, roundno);
        return shipmentPlanService.getShipmentTargetResultCount(authDTO, _CompKey, uldrqdt, roundno);
    }


    @Override
    @GetMapping(path = "/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public ShipmentResultDTO getShipmentResultList(@RequestParam @NotBlank final String compkey,
                                                   @RequestParam @NotNull final LocalDate uldrqdt,
                                                   @RequestParam @NotNull final Integer roundno,
                                                   @RequestParam @NotBlank final String ptnrkey
    ) {

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        final String _CompKey = Objects.requireNonNull(authDTO).getCompany();
        log.info("results: {} / {} / {} / {}", _CompKey, Objects.requireNonNull(authDTO).getId(), uldrqdt, roundno);
        return shipmentPlanService.getShipmentResult2List(
                                                        authDTO, _CompKey,
                                                        uldrqdt,
                                                        roundno,
                                                        ptnrkey);
    }


    @Override
    @GetMapping(path = "/mobile-sub2", produces = MediaType.APPLICATION_JSON_VALUE)
    public ShipmentAllTrackInfoDTO getShipmentAllTrackList(@RequestParam @NotBlank final String compkey,
                                                           @RequestParam @NotNull final LocalDate uldrqdt,
                                                           @RequestParam @NotNull final Integer roundno,
                                                           @RequestParam @NotBlank final String ptnrkey
    ) {

        log.info("mobile sub2: {} / {} / {}", uldrqdt, roundno, ptnrkey);
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        return shipmentPlanService.getShipmentAllTrackList(
                                                        authDTO,
                                                        uldrqdt,
                                                        roundno,
                                                        ptnrkey);
    }


    @Override
    @GetMapping(path = "/map-results", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<ShipmentMapResultDTO> getShipmentMapResultList(@RequestParam @NotBlank final String compkey,
                                                               @RequestParam @NotNull final LocalDate uldrqdt,
                                                               @RequestParam @NotNull final Integer roundno
    ) {

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        final String _CompKey = Objects.requireNonNull(authDTO).getCompany();
        return shipmentPlanService.getShipmentMapResultList(authDTO, _CompKey,
                                                            uldrqdt,
                                                            roundno);
    }


    @Override
    @GetMapping(path = "/changed-plan", produces = MediaType.APPLICATION_JSON_VALUE)
    public ShipmentPlanChangedDTO getChangedShipmentPlan(@Nullable final LocalDate uldrqdt, Integer roundno) {
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        final String _CompKey = Objects.requireNonNull(authDTO).getCompany();
        log.info("changed-plan: {} / {} / {}", Objects.requireNonNull(authDTO).getId(), uldrqdt, roundno);
        ShipmentPlanChangedDTO result = null;

        if ( ! shipmentPlanService.isAvailableDeletePlan(authDTO, uldrqdt, roundno) ) {
            result = new ShipmentPlanChangedDTO();
            result.setToday(uldrqdt);
            result.setEndday(uldrqdt);
            result.setCompkey(_CompKey);
            result.setIsChanged(false);
            return result;
        }

        if ( ShipmentConstant.USE_CHANGE_DB_OETMHI ) {
            result = shipmentPlanService.getChanged3ShipmentPlan(authDTO, _CompKey, uldrqdt, roundno); // NEW CHANGEDEV
        } else {
            result = shipmentPlanService.getChangedShipmentPlan(authDTO, _CompKey, uldrqdt, roundno); // OLD CHANGEDEV
        }

        if ( result != null && result.getIsChanged() ) {
            List<String> oetmskyValues = shipmentPlanService.deleteChangedShipmentPlan(authDTO, _CompKey, result);
            shipmentPlanService.clearOetmhdTmshpno(oetmskyValues);
        } else {
            log.info("changed-plan: IsChanged false");
        }
        return result;
    }


    @Override
    @PutMapping(path = "/delete/plan", produces = MediaType.APPLICATION_JSON_VALUE)
    public ShipmentPlanDeletedDTO deleteShipmentPlans(@RequestParam @NotBlank final String compkey,
                                                      @RequestParam @NotNull final LocalDate uldrqdt,
                                                      @RequestParam @NotNull final Integer roundno) {

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        final String _CompKey = Objects.requireNonNull(authDTO).getCompany();
        ShipmentPlanDeletedDTO response = new ShipmentPlanDeletedDTO();

        List<String> oldShpmtkyList = null;
        /*
        Boolean isFixedPlan = shipmentPlanService.isFixedShipmentPlanByDateRound(authDTO, uldrqdt, roundno);
        if ( isFixedPlan ) {
            oldShpmtkyList = shipmentPlanService.getShipmentListByDateRound(authDTO, uldrqdt, roundno);
            log.info("DeletePlan, oldShpmtkyList. {}", oldShpmtkyList);
            if ( oldShpmtkyList != null && oldShpmtkyList.size() > 0 ) {
                // Boolean isCheck = true 이면 chekc만 하고, isCheck = false 면 실제 DB 에서 지우도록 하였습니다.
                Boolean isCheck = true;
                Boolean bRet = dispatchPlanService.cancelDispatch(oldShpmtkyList, isCheck);
                if ( bRet == false ) {
                    response.setMessage("배차완료된 운송계획은 삭제할 수 없습니다.");
                    response.setErrorCode(2);
                    // 배차확정이 아니더라도 배차에 리스트가 없으면 false 결과
                    return response;
                }
            }
        }
        */

        // Cancel DispatchPlan
        oldShpmtkyList = shipmentPlanService.getShipmentListByDateRound(authDTO, uldrqdt, roundno);
        if(oldShpmtkyList != null && !oldShpmtkyList.isEmpty()) {
            /*
            List<String> dispatchConfirmList = dispatchPlanService.cancelCheckDispatch(oldShpmtkyList);
            if(dispatchConfirmList != null && !dispatchConfirmList.isEmpty()) {
                if(dispatchConfirmList.contains(oldShpmtkyList)) {
                    response.setMessage("배차완료된 운송계획은 삭제할 수 없습니다. : " + oldShpmtkyList);
                    response.setErrorCode(2);
                    return response;
                }
            }
            */

            for(String shipmentKey : oldShpmtkyList) {
                Boolean bRet = dispatchPlanService.cancelSelectedDispatch(shipmentKey);
                if(!bRet) {
                    log.info("deleteSelectedShipmentPlans : 배차계획 삭제 실패!! -> " + shipmentKey);
                } else {
                    log.info("deleteSelectedShipmentPlans : 배차계획 삭제 완료!! -> " + shipmentKey);
                }
            }
        }

        ShipmentPlanDeletedInfoDTO deletedInfo = shipmentPlanService.deleteShipmentPlans(
                        authDTO, _CompKey, uldrqdt, roundno, oldShpmtkyList);

        shipmentPlanService.clearDeletedShipmentPlans();

        shipmentPlanService.clearOetmhdTmshpno(deletedInfo.getOetmskyValues());
        //shipmentPlanService.deleteShipmentOmsItems(deletedInfo.getShpmtkyList());

        response.setErrorCode(0);
        response.setUldrqdt(uldrqdt);
        response.setRoundno(roundno);
        response.setShpplky(deletedInfo.getShpplky());
        log.info("Delete ShipmentPlan, shpplky: {}", deletedInfo.getShpplky());
        return response;
    }

    // @Override
    @PutMapping(path = "/check-delete-plan", produces = MediaType.APPLICATION_JSON_VALUE)
    public AvailableDeletePlanDTO checkAvailableDeleteShipmentPlanByDate(
            @RequestBody ShipmentPlan2DateTimesDTO inDto
    ) {
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        return shipmentPlanService.checkAvailableDeleteShipmentPlanByDate(authDTO, inDto);
    }

    // @Override
    @PutMapping(path = "/delete-double-plan", produces = MediaType.APPLICATION_JSON_VALUE)
    public AvailableDeletePlanDTO deleteDoubleShipmentPlanByDate(
                                                @RequestBody ShipmentPlan2DateTimesDTO inDto) {
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        return shipmentPlanService.deleteDoubleShipmentPlanByDate(authDTO, inDto);
    }

    // TEST
    @Hidden
    @PutMapping(path = "/delete-one", produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean deleteShipmentPlanOne(@RequestBody SimpleParams params) {
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        final String compkey = Objects.requireNonNull(authDTO).getCompany();

        final String shpplky = params.getStr1();
        if ( ! shipmentPlanService.isAvailableDeletePlan(authDTO, shpplky) ) {
            log.info("delete ShipmentPlanOne, 배차된 운송건으로 삭제 불가. {}", shpplky);
            return false;
        }

        Boolean result = true;
        if ( shipmentPlanService.deletePlanDispatch(authDTO, shpplky, false) == false ) {
            log.info("delete ShipmentPlanOne, 배차삭제 실패. {}", shpplky);
            result = false;
        } else {
            log.info("delete ShipmentPlanOne, 배차삭제 성공. {}", shpplky);
        }

        List<String> oetmskyValues = shipmentPlanService.deleteShipmentPlanOne(compkey, shpplky);
        // 중복제거
        List<String> uniqueValues = null;
        if ( oetmskyValues != null && oetmskyValues.size() > 0 ) {
            uniqueValues = oetmskyValues.stream().distinct().collect(Collectors.toList());
        }
        log.info("delete ShipmentPlanOne, uniqueValues : {}", uniqueValues);
        shipmentPlanService.clearOetmhdTmshpno(uniqueValues);

        return result;
    }

    // TEST
    @Hidden
    @GetMapping(path = "/replace-truck", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<?> checkMakeReplaceShipmentPlan(@NotBlank String compkey,
                                            @NotBlank String shpmtky) {

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        return shipmentPlanService.makeReplaceShipmentPlan(authDTO, shpmtky, null);
    }

    // TEST
    @Hidden
    @PostMapping(path = "/test-a", produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean callTestFuncA(@RequestBody ShipmentTestArgsDTO args) {
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        return shipmentPlanService.testFuncA(authDTO, args);
    }

    @Hidden
    @GetMapping(path = "/oetmsky", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<OetmhdTmsInfoDTO> findOetmskyByShipmentKey(@RequestParam @NotNull String shipmentKey) {

        return oetmhdService.findOetmskyByShipmentKey(shipmentKey);
    }

    @Hidden
    @GetMapping(path = "/simple/info", produces = MediaType.APPLICATION_JSON_VALUE)
    public ShipmentPlanSimpleInfo findShipmentPlanSimpleInfo(@RequestParam @NotNull String compkey,
                                                             @RequestParam @NotNull String shpplky) {

        return shipmentPlanService.findShipmentPlanSimpleInfo(compkey, shpplky);
    }

}
