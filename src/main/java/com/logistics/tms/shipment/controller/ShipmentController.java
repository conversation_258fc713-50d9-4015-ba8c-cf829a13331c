package com.logistics.tms.shipment.controller;

import com.logistics.tms.common.util.AuthUtils;
import com.logistics.tms.configuration.error.ShipmentException;
import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.shipment.constant.ShipmentConstant;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.ShipmentLoadingInfoDTO;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.ShipmentTrackInfoDTO;
import com.logistics.tms.shipment.dto.ShipmentDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.SimpleParams;
import com.logistics.tms.shipment.interfaces.ShipmentSwagger;
import com.logistics.tms.shipment.service.ShipmentService;

import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

@Slf4j
@Validated
@RestController
@RequestMapping(ShipmentConstant.SHIPMENT_ROOT_URL)
@RequiredArgsConstructor
public class ShipmentController implements ShipmentSwagger {

    private final ShipmentService shipmentService;

    @Override
    @GetMapping(path = "", produces = MediaType.APPLICATION_JSON_VALUE)
    public ShipmentDTO getShipment(@RequestParam @NotBlank final String compkey,
                                   @RequestParam @NotBlank final String shpmtky) {

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        final String _CompKey = Objects.requireNonNull(authDTO).getCompany();
        return shipmentService.getShipment(_CompKey, shpmtky)
                .orElseThrow(() -> new ShipmentException(HttpStatus.NOT_FOUND, "해당하는 운송이 존재하지 않습니다."));
    }

    @Hidden
    @Override
    @GetMapping(path = "/page", produces = MediaType.APPLICATION_JSON_VALUE)
    public Page<ShipmentDTO> getShipmentPage(@RequestParam @NotBlank final String compkey,
                                             @Nullable final Pageable pageable) {

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        final String _CompKey = Objects.requireNonNull(authDTO).getCompany();
        return shipmentService.getShipmentPage(_CompKey, pageable);
    }

    @Override
    @GetMapping(path = "/loading-info", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<ShipmentLoadingInfoDTO> getShipmentLoadingInfo(@NotBlank String compkey,
                                                               @NotBlank String shpmtky) {

        return shipmentService.getShipmentLoadingInfo(compkey, shpmtky);
    }

    @Override
    @GetMapping(path = "/track-info", produces = MediaType.APPLICATION_JSON_VALUE)
    public ShipmentTrackInfoDTO getShipmentTrackInfo(@NotBlank String compkey,
                                                    @NotBlank String shpmtky) {

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        final String _CompKey = Objects.requireNonNull(authDTO).getCompany();
        return shipmentService.getShipment2TrackInfo(_CompKey, shpmtky);
    }

    // TEST
    @Hidden
    @PutMapping(path = "/empty-plan", produces = MediaType.APPLICATION_JSON_VALUE)
    public Integer emptyPlan(@RequestBody SimpleParams params) {
        final String shpmtky = params.getStr2();
        Integer upResult = shipmentService.updateShipmentPlanEmpty(shpmtky);
        log.info("Empty Plan, upResult : {} / {}", upResult, shpmtky);
        return upResult;
    }

    @Hidden
    @GetMapping(path = "/simple/info", produces = MediaType.APPLICATION_JSON_VALUE)
    public ShipmentCustomDTO.ShipmentSimpleInfoDTO findShipmentSimpleInfo(@RequestParam @NotNull String compkey,
                                                                          @RequestParam @NotNull String shpmtky) {

        return shipmentService.findShipmentSimpleInfo(compkey, shpmtky);
    }

}
