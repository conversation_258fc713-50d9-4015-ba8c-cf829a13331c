package com.logistics.tms.shipment.service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import com.logistics.tms.external.dto.OetmhdTmsInfoDTO;
import com.logistics.tms.external.service.OetmhdService;
import com.logistics.tms.shipment.dto.*;
import com.logistics.tms.shipment.mapper.ShipmentMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.locationtech.jts.geom.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import com.logistics.tms.common.enumeration.OrderStatusEnum;
import com.logistics.tms.common.enumeration.PackageTypeEnum;
import com.logistics.tms.common.enumeration.PartnerTypeEnum;
import com.logistics.tms.common.enumeration.ShipmentVehicleTonEnum;
import com.logistics.tms.common.enumeration.SimulationOptionEnum;
import com.logistics.tms.common.enumeration.TruckLoadTypeEnum;
import com.logistics.tms.common.enumeration.VehicleTypeEnum;
import com.logistics.tms.common.enumeration.VehicleWeightTypeEnum;
import com.logistics.tms.common.enumeration.VisitTypeEnum;
import com.logistics.tms.common.util.GeometryUtils;
import com.logistics.tms.common.util.NumberUtils;
import com.logistics.tms.configuration.error.ShipmentException;
import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.dispatch.dto.DispatchAddDTO;
import com.logistics.tms.dispatch.service.DispatchManualService;
import com.logistics.tms.dispatch.service.DispatchPlanService;
import com.logistics.tms.driver.dto.DriverInfoDTO;
import com.logistics.tms.driver.service.DriverInfoCustomService;
import com.logistics.tms.lbs.constant.LbsConstant;
import com.logistics.tms.lbs.domain.route.LbsRouteDestination;
import com.logistics.tms.lbs.domain.route.LbsRouteOnDemandRequest;
import com.logistics.tms.lbs.domain.route.LbsRouteOnDemandResponse;
import com.logistics.tms.lbs.service.LbsRouteService;
import com.logistics.tms.loadingoptimizationmanager.feign.LoadOptimizeFeignClient;
import com.logistics.tms.loadingoptimizationmanager.feign.type.Address;
import com.logistics.tms.loadingoptimizationmanager.feign.type.ClusterOption;
import com.logistics.tms.loadingoptimizationmanager.feign.type.Item;
import com.logistics.tms.loadingoptimizationmanager.feign.type.LoadedItem;
import com.logistics.tms.loadingoptimizationmanager.feign.type.MultiTruckLoadingResult;
import com.logistics.tms.loadingoptimizationmanager.feign.type.OptimizeMultipleTrucksRequestDto;
import com.logistics.tms.loadingoptimizationmanager.feign.type.Position;
import com.logistics.tms.loadingoptimizationmanager.feign.type.Truck;
import com.logistics.tms.loadingoptimizationmanager.feign.type.TruckResult;
import com.logistics.tms.loadingoptimizationmanager.feign.type.TruckType;
import com.logistics.tms.pickupdropoffarea.dto.PickUpDropOffAreaDTO;
import com.logistics.tms.pickupdropoffarea.service.PickUpDropOffAreaService;
import com.logistics.tms.shipment.constant.ShipmentConstant;
import com.logistics.tms.shipment.constant.ShipmentUtils;
import com.logistics.tms.shipment.dao.ShipmentTargetDAO;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.CarrierInfoDTO;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.OetmhdSimpleDTO;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.ShipmentAllTrackInfoDTO;
import com.logistics.tms.shipment.dto.ShipmentOmsItemCustomDTO.ShipmentOmsItemAddDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanSimpleInfo;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.AvailableDeletePlanDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.LoadOptimizerTruckCapacityDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.PickupDropoffDateAndTime;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentChangePlanOetmskyPairDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentChangedListDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentMapDest;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentMapDummyDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentMapResultDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentOetmsInfo;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentOetmskyChangeOldInfo;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentOetmskyPairDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlan2DateTimesDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanChangedDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanChangedDummyDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanChangedSimpleDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanChangedTargetDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanCheckMadeDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanDeletedInfoDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanFixDummyDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanFixPartnerDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanFixPrevDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanFixShipmentDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanIsChangedDAO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanLoadOptimizerResultDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanMakeDummyDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentResultCountDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentResultCountDummyDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentResultCountItemDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentResultDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentResultDummyDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentResultItemDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentResultListDummyDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentSectionOneDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentSectionTwoDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentTargetCountDummyDAO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentTargetResultCountDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentTestArgsDTO;
import com.logistics.tms.shipment.entity.Shipment;
import com.logistics.tms.shipment.entity.ShipmentOmsItem;
import com.logistics.tms.shipment.entity.ShipmentPlan;
import com.logistics.tms.shipment.entity.ShipmentSection;
import com.logistics.tms.shipment.mapper.ShipmentOmsItemMapper;
import com.logistics.tms.shipment.mapper.ShipmentPlanMapper;
import com.logistics.tms.shipment.repository.QShipmentOmsItemRepository;
import com.logistics.tms.shipment.repository.QShipmentPlanRepository;
import com.logistics.tms.shipment.repository.QShipmentRepository;
import com.logistics.tms.shipment.repository.QShipmentSectionRepository;
import com.logistics.tms.shipment.repository.ShipmentOmsItemRepository;
import com.logistics.tms.shipment.repository.ShipmentPlanRepository;
import com.logistics.tms.shipment.repository.ShipmentRepository;
import com.logistics.tms.shipment.repository.ShipmentSectionRepository;

import jakarta.annotation.Nullable;
import jakarta.persistence.EntityManager;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Validated
@RequiredArgsConstructor
@Transactional(readOnly = true)
@Service
public class ShipmentPlanService {

    private final ShipmentPlanRepository shipmentPlanRepository;
    private final QShipmentPlanRepository qShipmentPlanRepository;
    private final ShipmentRepository shipmentRepository;
    private final QShipmentRepository qShipmentRepository;
    private final ShipmentSectionRepository shipmentSectionRepository;
    private final QShipmentSectionRepository qShipmentSectionRepository;
    private final ShipmentOmsItemRepository shipmentOmsItemRepository;
    private final QShipmentOmsItemRepository qShipmentOmsItemRepository;
    private final ShipmentPlanMapper shipmentPlanMapper;
    private final ShipmentMapper shipmentMapper;
    private final ShipmentOmsItemMapper shipmentOmsItemMapper;
    private final ShipmentService shipmentService;
    private final ShipmentSectionService shipmentSectionService;
    private final DispatchPlanService dispatchPlanService;
    private final DispatchManualService dispatchManualService;
    private final PickUpDropOffAreaService pickUpDropOffAreaService;
    private final DriverInfoCustomService driverInfoCustomService;
    private final OetmhdService oetmhdService;

    @Autowired
    private LoadOptimizeFeignClient loadOptimizeFeignClient;

    @Autowired
    private LbsRouteService lbsRouteService;

    @Autowired
    private EntityManager entityManager;

    public List<ShipmentTargetDTO> getShipmentTargetOne(final AuthDTO authDTO,
            @NotBlank final String compkey,
            @NotNull final LocalDate uldrqdt,
            @NotNull final Integer roundno,
            @NotNull final VisitTypeEnum visityp,
            @NotBlank final String ptnrkey,
            @NotBlank final String destkey) {

        final String _authPtnrkey = Objects.requireNonNull(authDTO).getPartnerKey();

        Boolean allStatus = false;
        final ShipmentPlan shpl = shipmentPlanRepository.findFirstByCompkeyAndPtnrkeyAndShpmtdtAndRoundnoAndDeletatNull(
                compkey, _authPtnrkey, uldrqdt, roundno);
        if (shpl != null) {
            allStatus = true;
        }
        log.info("Shipment TargetOne, 시뮬레이션 여부.allStatus: {}", allStatus);

        List<ShipmentTargetDTO> targetList = qShipmentPlanRepository.findShipmentTarget(_authPtnrkey, compkey,
                uldrqdt,
                roundno,
                visityp,
                ptnrkey,
                destkey,
                allStatus);
        for (ShipmentTargetDTO one : targetList) {
            one.setPkavat1(ShipmentUtils.convertUnavat1Code(one.getPkavat1()));
            one.setDpavat1(ShipmentUtils.convertUnavat1Code(one.getDpavat1()));
        }
        return targetList;
    }

    public ShipmentPlanChangedTargetDTO getShipmentTargetCount(final AuthDTO authDTO,
            @NotBlank final String compkey,
            @NotNull final LocalDate uldrqdt,
            @NotNull final Integer roundno,
            @NotNull final VisitTypeEnum visityp,
            final Boolean changed,
            // final LocalDate changedshpmtdt,
            // final Integer changedroundno,
            final Boolean allStatus) {

        ShipmentPlanChangedTargetDTO result = new ShipmentPlanChangedTargetDTO();

        LocalDate today = LocalDate.now(); // 오늘 날짜
        LocalDate tomorrow = today.plusDays(1); // 내일 날짜

        {
            // if ( ! uldrqdt.isBefore(today) && ! uldrqdt.isAfter(tomorrow) ) { // D-0 ,
            // D-1
            if (!uldrqdt.isAfter(tomorrow)) { // D-1 이전
                result.setMsg("긴급은 시뮬레이션 하지 않음");
                // return result;
            }
        }

        final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();

        List<ShipmentTargetCountDummyDAO> targetDummyList = qShipmentPlanRepository.findShipmentTarget2Count(ptnrkey,
                compkey,
                uldrqdt,
                roundno,
                visityp,
                allStatus);

        if (targetDummyList != null) {
            // log.info("Shipment TargetCount, targetDummyList Count #1 : {}",
            // targetDummyList.size());
        }

        // 250226. 시뮬레이션 이후 target List 가져오는 경우에는
        // Shipment No 가 있는지 확인해서 , 해당 Shipment MANLSHP 가 true 인 것을 제외한다.
        if (allStatus && targetDummyList != null) {
            Iterator<ShipmentTargetCountDummyDAO> iterator = targetDummyList.iterator();
            while (iterator.hasNext()) {
                ShipmentTargetCountDummyDAO one = iterator.next();
                if (one.getTmshpno() != null && !one.getTmshpno().trim().isEmpty()) {
                    Shipment shmt = shipmentRepository.findFirstByShpmtkyAndCompkeyAndDeletatIsNull(one.getTmshpno(),
                            compkey);
                    if (shmt != null && shmt.getManlshp()) {
                        log.info("Shipment TargetCount, Found Manual Shipment. {}", one.getTmshpno());
                        iterator.remove(); // 리스트에서 안전하게 제거
                    }
                }
            }
        }
        if (targetDummyList != null) {
            // log.info("Shipment TargetCount, targetDummyList Count #2 : {}",
            // targetDummyList.size());
        }

        if (changed) {
            // 삭제는 변경확인 팝업에서 ...
            // final LocalDate today = LocalDate.now();
            // long daysDifference = ChronoUnit.DAYS.between(today, changedshpmtdt);
            // log.info("today : {} / changed Shpmtdt : {} / Diff : {}", today,
            // changedshpmtdt, daysDifference);
            // if ( daysDifference >= 2 ) {
            // // Delete Previous ShipmentPlan
            // this.deleteShipmentPlanAfter(compkey, today, changedshpmtdt, changedroundno);
            // }

            result.setShowTooltip(true);
        } else {
            result.setShowTooltip(false);
        }

        List<ShipmentTargetCountDTO> targetList = new ArrayList<>();

        List<List<ShipmentTargetCountDummyDAO>> groupCustPtnrkeyList = null;

        if (VisitTypeEnum.DROPOFF.equals(visityp)) {
            // 하차지 기준. custkey 단위로 group
            groupCustPtnrkeyList = targetDummyList.stream()
                    .filter(dto -> dto.getCustkey() != null) // null 키 제거
                    .collect(Collectors.groupingBy(ShipmentTargetCountDummyDAO::getCustkey))
                    .values().stream()
                    .collect(Collectors.toList());
        } else if (VisitTypeEnum.PICKUP.equals(visityp)) {
            // 상차지 기준. ptnrkey 단위로 group
            groupCustPtnrkeyList = targetDummyList.stream()
                    .filter(dto -> dto.getPtnrkey() != null) // null 키 제거
                    .collect(Collectors.groupingBy(ShipmentTargetCountDummyDAO::getPtnrkey))
                    .values().stream()
                    .collect(Collectors.toList());
        }

        if (groupCustPtnrkeyList == null || groupCustPtnrkeyList.size() < 1) {
            return result;
        }

        for (List<ShipmentTargetCountDummyDAO> custPtnrkeyList : groupCustPtnrkeyList) {
            // Destkey,Loadkey 단위로 Group
            List<List<ShipmentTargetCountDummyDAO>> groupDestLoadkeyList = null;
            if (VisitTypeEnum.DROPOFF.equals(visityp)) {
                groupDestLoadkeyList = custPtnrkeyList.stream()
                        .filter(dto -> dto.getDestkey() != null)
                        .collect(Collectors.groupingBy(ShipmentTargetCountDummyDAO::getDestkey))
                        .values().stream()
                        .collect(Collectors.toList());
            } else if (VisitTypeEnum.PICKUP.equals(visityp)) {
                groupDestLoadkeyList = custPtnrkeyList.stream()
                        .filter(dto -> dto.getLoadkey() != null)
                        .collect(Collectors.groupingBy(ShipmentTargetCountDummyDAO::getLoadkey))
                        .values().stream()
                        .collect(Collectors.toList());
            }

            if (groupDestLoadkeyList == null || groupDestLoadkeyList.size() < 1) {
                continue;
            }

            for (List<ShipmentTargetCountDummyDAO> destLoadkeyList : groupDestLoadkeyList) {
                // 단독 건수와 혼적 건수를 별도로 집계
                int ftlCount = (int) destLoadkeyList.stream()
                        .filter(s -> s.getCargoty() == TruckLoadTypeEnum.FTL)
                        .count();
                int ltlCount = (int) destLoadkeyList.stream()
                        .filter(s -> s.getCargoty() == TruckLoadTypeEnum.LTL)
                        .count();
                int totalCount = (ftlCount + ltlCount);

                if (destLoadkeyList.size() < 1) {
                    continue;
                }

                ShipmentTargetCountDummyDAO first = destLoadkeyList.get(0);
                ShipmentTargetCountDTO one = new ShipmentTargetCountDTO();
                one.setCompkey(first.getCompkey());
                one.setDenamlc(first.getDenamlc());
                one.setPtnrtyp(first.getPtnrtyp());
                one.setPtnamlc(first.getPtnamlc());
                one.setLodrqdt(first.getLodrqdt());
                one.setUldrqdt(first.getUldrqdt());
                one.setTotcont(totalCount);
                one.setFtlcont(ftlCount);
                one.setLtlcont(ltlCount);
                if (VisitTypeEnum.DROPOFF.equals(visityp)) {
                    one.setPtnrkey(first.getCustkey());
                    one.setDestkey(first.getDestkey());
                } else if (VisitTypeEnum.PICKUP.equals(visityp)) {
                    one.setPtnrkey(first.getPtnrkey());
                    one.setDestkey(first.getLoadkey());
                }

                targetList.add(one);
            }

        }

        result.setTargetList(targetList);

        return result;
    }

    public Boolean isFixedShipmentPlanByDateRound(final AuthDTO authDTO,
            @NotNull final LocalDate uldrqdt,
            @NotNull final Integer roundno) {
        final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
        final String compkey = Objects.requireNonNull(authDTO).getCompany();
        // Get Shipment Plan
        ShipmentPlan shPlan = shipmentPlanRepository.findFirstByCompkeyAndPtnrkeyAndShpmtdtAndRoundnoAndDeletatNull(
                compkey, ptnrkey, uldrqdt, roundno);
        if (shPlan == null)
            return false;
        if (shPlan.getFixedat() == null)
            return false;
        return true;
    }

    public List<String> getShipmentListByDateRound(final AuthDTO authDTO,
            @NotNull final LocalDate uldrqdt,
            @NotNull final Integer roundno) {

        final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
        final String compkey = Objects.requireNonNull(authDTO).getCompany();
        List<String> oldShpmtkyList = null;

        try {
            List<ShipmentPlanCheckMadeDTO> exsitPlan = qShipmentPlanRepository.findShipmentPlanByDateRound(ptnrkey,
                    compkey, uldrqdt, roundno);

            if (exsitPlan != null && exsitPlan.size() > 0) {
                final String oldShpplky = exsitPlan.get(0).getShpplky();
                oldShpmtkyList = this.getShipmentListByShpplky(oldShpplky);
                for (final String shpmtky : oldShpmtkyList) {
                    log.info("getShipmentListByDateRound : OLD shpmtky. {} / {}", shpmtky, oldShpplky);
                }
            }
        } catch (Exception ex) {
        }

        return oldShpmtkyList;
    }

    public List<String> getFixedShipmentList(final String compkey,
            final String shpplky) {

        final ShipmentPlan shipmentPlan = shipmentPlanRepository.findByCompkeyAndShpplky(compkey, shpplky);

        if (Objects.isNull(shipmentPlan)) {
            log.info("getShipmentList : 해당하는 운송계획이 존재하지 않습니다.");
            return null;
        }
        if (Objects.nonNull(shipmentPlan.getDeletat())) {
            log.info("getShipmentList : 해당 운송계획은 삭제된 상태입니다.");
            return null;
        }

        List<String> shipmentKeyList = new ArrayList<>();
        for (Shipment shipment : shipmentPlan.getShipmentList()) {
            if (shipment.getDeletat() == null && shipment.getFixedat() != null) {
                shipmentKeyList.add(shipment.getShpmtky());
            }
        }

        return shipmentKeyList;

    }

    public ShipmentPlanMakeDummyDTO makeShipmentPlan(final AuthDTO authDTO,
            @NotBlank final String compkey,
            final String useract,
            @NotNull final String partnerKey,
            @NotNull final LocalDate uldrqdt,
            @NotNull final Integer roundno,
            final SimulationOptionEnum simulop) {

        ShipmentPlanMakeDummyDTO response = new ShipmentPlanMakeDummyDTO();

        final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
        log.info("MakeShipmentPlan ptnrkey = {}", ptnrkey);
        List<ShipmentPlanCheckMadeDTO> exsitPlan = qShipmentPlanRepository.findShipmentPlanByDateRound(ptnrkey, compkey,
                uldrqdt, roundno);

        LocalDate today = LocalDate.now(); // 오늘 날짜
        LocalDate tomorrow = today.plusDays(1); // 내일 날짜

        {
            // if ( ! uldrqdt.isBefore(today) && ! uldrqdt.isAfter(tomorrow) ) { // D-0 ,
            // D-1
            if (!uldrqdt.isAfter(tomorrow)) { // D-1 이전
                // 기존 ShipmentPlan 이 존재하면 재 시뮬레이션 하지 않음
                if (exsitPlan == null || exsitPlan.size() < 1) {
                    // 기존 ShipmentPlan 이 없음
                } else {
                    response.setErrorMsg("긴급은 시뮬레이션 하지 않음");
                    // return response; // TODO.for TEST(주석)
                }
            }
        }

        List<String> oldShpmtkyList = null;
        Boolean isFixedPlan = false;
        if (exsitPlan != null && exsitPlan.size() > 0) {
            if (exsitPlan.get(0).getFixedat() != null)
                isFixedPlan = true;

            final String oldShpplky = exsitPlan.get(0).getShpplky();
            oldShpmtkyList = this.getShipmentListByShpplky(oldShpplky);

            // Plan 확정이면, 배차DB 취소가능여부 체크
            if (isFixedPlan && oldShpmtkyList != null && oldShpmtkyList.size() > 0) {
                log.info("MakeShipmentPlan, oldShpmtkyList. {}", oldShpmtkyList);
                // Boolean isCheck = true 이면 chekc만 하고, isCheck = false 면 실제 DB 에서 지우도록 하였습니다.
                /*
                 * Boolean isCheck = true;
                 * Boolean bRet = dispatchPlanService.cancelDispatch(oldShpmtkyList, isCheck);
                 * if ( bRet == false ) {
                 * response.setErrorMake("배차완료된 운송계획은 삭제할 수 없습니다.");
                 * return response;
                 * }
                 */
                if (!isAvailableDeletePlan(authDTO, oldShpmtkyList)) {
                    response.setErrorMsg("배차완료된 운송계획은 삭제할 수 없습니다.");
                    return response;
                }
            }

            response.setIsFixedPlan(isFixedPlan);
            response.setOldShpplky(oldShpplky);
            response.setShpmtkyList(oldShpmtkyList);
        }

        final LocalDateTime now = LocalDateTime.now();

        List<ShipmentTargetDAO> targetList = qShipmentPlanRepository.findShipmentTargetList(ptnrkey, compkey,
                uldrqdt,
                roundno);

        if (CollectionUtils.isEmpty(targetList)) {
            response.setErrorMsg("해당하는 운송요청정보가 존재하지 않습니다.");
            return response;
            // throw new ShipmentException(HttpStatus.NOT_FOUND, "해당하는 운송요청정보가 존재하지 않습니다.");
        }

        targetList = this.fillBlankShipmentData(targetList);

        // load,custkey 의 areaId 리스트
        List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaIdList = this.getAreaListByLoadCustkeyPartnerKey(ptnrkey,
                targetList);
        /*
         * delete log
         * for ( PickUpDropOffAreaDTO.AreaShipmentResponse a : areaIdList ) {
         * log.info("MakeShipmentPlan, AreaIdCode [{}:{}] => ({})", a.getDestkey(),
         * a.getCustomerCode(), a.getAreaId());
         * }
         */

        List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaDriverList = null;
        // if ( simulop.equals(SimulationOptionEnum.DESIGNATED) ) {
        // areaDriverList = this.getDestAreaForLoadOptimizer(targetList, ptnrkey,
        // uldrqdt);
        // } else {
        // areaDriverList = this.getDriverVehicleInfoByPartnerKeyAndHoliday(ptnrkey,
        // uldrqdt);
        // }
        // 권역무시하고 전체 차량 추출 (250103)
        areaDriverList = this.getDriverVehicleInfoByPartnerKeyAndHoliday(ptnrkey, uldrqdt);

        List<Truck> custTrucks = this.makeTruckForLoadOptimizer(compkey, areaDriverList, targetList);

        // LoadingOptimization Simulation
        ShipmentPlanLoadOptimizerResultDTO loadOptimizedObj = this.makeLoadOptimizer(compkey, simulop, targetList,
                areaDriverList, areaIdList, custTrucks);

        final MultiTruckLoadingResult optimizedList = loadOptimizedObj.getLoadResult();
        if (optimizedList == null) {
            log.info("MakeShipmentPlan, Empty !!!! Truck LoadingResult");
            response.setErrorMsg(loadOptimizedObj.getErrorMsg());
            response.setOldShpplky(null);
            response.setShpmtkyList(null);
            return response;
        }

        // 상하차 이동간 예상운임비 base
        final Integer moveDestFee = shipmentService.getDestFeeFromMcodem();
        log.info("MakeShipmentPlan, Mcodem DestFee = {}", moveDestFee);

        // custkey 만 추출
        List<String> uniqueCustomer = targetList.stream()
                .map(t -> t.getCustkey()) // custkey 필드만 추출
                .distinct() // 중복 제거
                .collect(Collectors.toList()); // List로 수집

        // plan 키는 1개
        // final String shpplky = shipmentPlanRepository.getNextSequenceKey();
        String shpplky = null;
        if (exsitPlan.isEmpty()) {
            shpplky = shipmentPlanRepository.getNextSequenceKey();
        } else {
            shpplky = exsitPlan.get(0).getShpplky();
        }

        final ShipmentPlan shipmentPlan = ShipmentPlan.builder()
                .compkey(compkey)
                .shpplky(shpplky)
                .useract(useract)
                .shpmtdt(uldrqdt)
                .roundno(roundno)
                .simulop(simulop)
                .ptnrkey(ptnrkey)
                .simulat(now)
                .build();

        for (String custkey : uniqueCustomer) {
            // FTL 생성
            final List<Shipment> ftlShipmentList = this.makeFtlShipmentList(compkey, custkey, targetList, custTrucks,
                    moveDestFee, response);
            shipmentPlan.addShipmentList(ftlShipmentList);
        }

        log.info("MakeShipmentPlan, TruckResult size: {}", optimizedList.getTruckResults().size());
        for (TruckResult truckInfo : optimizedList.getTruckResults()) {
            // LTL 생성
            final List<Shipment> ltlShipmentList = this.makeLtlShipmentList(compkey, targetList, areaDriverList,
                    custTrucks, truckInfo, moveDestFee, response);
            shipmentPlan.addShipmentList(ltlShipmentList);
        }

        if (optimizedList.getUnassignedItems().getLoadedItems().size() > 0) {
            // 남은 LTL 생성
            final List<Shipment> ltlShipmentList = this.makeRemainLtlShipmentList(compkey, targetList, areaDriverList,
                    custTrucks,
                    optimizedList.getUnassignedItems().getUnloadedItems(), moveDestFee, response);
            shipmentPlan.addShipmentList(ltlShipmentList);
        }

        response.setShipPlan(shipmentPlan);
        // log.info("Make ShipmentList().size = {}",
        // shipmentPlan.getShipmentList().size());
        return response;
    }

    public ShipmentPlanMakeDummyDTO makeSelectedShipmentPlan(final AuthDTO authDTO,
            @NotBlank final String compkey,
            final String useract,
            @NotNull final String partnerKey,
            @NotNull final LocalDate uldrqdt,
            @NotNull final Integer roundno,
            @NotNull final List<String> reOetmskyList,
            final SimulationOptionEnum simulop) {

        ShipmentPlanMakeDummyDTO response = new ShipmentPlanMakeDummyDTO();

        final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
        log.info("MakeShipmentPlan ptnrkey = {}", ptnrkey);
        List<ShipmentPlanCheckMadeDTO> exsitPlan = qShipmentPlanRepository.findShipmentPlanByDateRound(ptnrkey, compkey,
                uldrqdt, roundno);

        LocalDate today = LocalDate.now(); // 오늘 날짜
        LocalDate tomorrow = today.plusDays(1); // 내일 날짜

        {
            // if ( ! uldrqdt.isBefore(today) && ! uldrqdt.isAfter(tomorrow) ) { // D-0 ,
            // D-1
            if (!uldrqdt.isAfter(tomorrow)) { // D-1 이전
                // 기존 ShipmentPlan 이 존재하면 재 시뮬레이션 하지 않음
                if (exsitPlan == null || exsitPlan.size() < 1) {
                    // 기존 ShipmentPlan 이 없음
                } else {
                    response.setErrorMsg("긴급은 시뮬레이션 하지 않음");
                    // return response; // TODO.for TEST(주석)
                }
            }
        }

        List<String> oldShpmtkyList = null;
        Boolean isFixedPlan = false;
        if (exsitPlan != null && exsitPlan.size() > 0) {
            if (exsitPlan.get(0).getFixedat() != null)
                isFixedPlan = true;

            final String oldShpplky = exsitPlan.get(0).getShpplky();
            oldShpmtkyList = this.getShipmentListByShpplky(oldShpplky);

            // Plan 확정이면, 배차DB 취소가능여부 체크
            if (isFixedPlan && oldShpmtkyList != null && oldShpmtkyList.size() > 0) {
                log.info("MakeShipmentPlan, oldShpmtkyList. {}", oldShpmtkyList);
                // Boolean isCheck = true 이면 chekc만 하고, isCheck = false 면 실제 DB 에서 지우도록 하였습니다.

                // Boolean isCheck = true;
                // Boolean bRet = dispatchPlanService.cancelDispatch(oldShpmtkyList, isCheck);
                // if ( bRet == false ) {
                // response.setErrorMake("배차완료된 운송계획은 삭제할 수 없습니다.");
                // return response;
                // }

                if (!isAvailableDeletePlan(authDTO, oldShpmtkyList)) {
                    response.setErrorMsg("배차완료된 운송계획은 삭제할 수 없습니다.");
                    return response;
                }
            }

            response.setIsFixedPlan(isFixedPlan);
            response.setOldShpplky(oldShpplky);
            response.setShpmtkyList(oldShpmtkyList);
        }

        final LocalDateTime now = LocalDateTime.now();

        List<ShipmentTargetDAO> targetList = qShipmentPlanRepository.findShipmentTargetList(ptnrkey, compkey,
                uldrqdt,
                roundno);

        if (CollectionUtils.isEmpty(targetList)) {
            response.setErrorMsg("해당하는 운송요청정보가 존재하지 않습니다.");
            return response;
            // throw new ShipmentException(HttpStatus.NOT_FOUND, "해당하는 운송요청정보가 존재하지 않습니다.");
        }

        targetList = this.fillBlankShipmentData(targetList);

        if (!CollectionUtils.isEmpty(reOetmskyList)) {
            Set<String> allowed = new HashSet<>(reOetmskyList);
            targetList = targetList.stream()
                    .filter(t -> allowed.contains(t.getOetmsky()))
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(targetList)) {
            response.setErrorMsg("선택된 oetmsky 목록에 맞는 운송요청정보가 없습니다.");
            return response;
        }

        // load,custkey 의 areaId 리스트
        List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaIdList = this.getAreaListByLoadCustkeyPartnerKey(ptnrkey,
                targetList);
        for (PickUpDropOffAreaDTO.AreaShipmentResponse a : areaIdList) {
            log.info("MakeShipmentPlan, AreaIdCode [{}:{}] => ({})", a.getDestkey(), a.getCustomerCode(),
                    a.getAreaId());
        }

        List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaDriverList = null;
        // if ( simulop.equals(SimulationOptionEnum.DESIGNATED) ) {
        // areaDriverList = this.getDestAreaForLoadOptimizer(targetList, ptnrkey,
        // uldrqdt);
        // } else {
        // areaDriverList = this.getDriverVehicleInfoByPartnerKeyAndHoliday(ptnrkey,
        // uldrqdt);
        // }
        // 권역무시하고 전체 차량 추출 (250103)
        areaDriverList = this.getDriverVehicleInfoByPartnerKeyAndHoliday(ptnrkey, uldrqdt);

        List<Truck> custTrucks = this.makeTruckForLoadOptimizer(compkey, areaDriverList, targetList);

        // LoadingOptimization Simulation
        ShipmentPlanLoadOptimizerResultDTO loadOptimizedObj = this.makeLoadOptimizer(compkey, simulop, targetList,
                areaDriverList, areaIdList, custTrucks);

        final MultiTruckLoadingResult optimizedList = loadOptimizedObj.getLoadResult();
        if (optimizedList == null) {
            log.info("MakeShipmentPlan, Empty !!!! Truck LoadingResult");
            response.setErrorMsg(loadOptimizedObj.getErrorMsg());
            response.setOldShpplky(null);
            response.setShpmtkyList(null);
            return response;
        }

        // 상하차 이동간 예상운임비 base
        final Integer moveDestFee = shipmentService.getDestFeeFromMcodem();
        log.info("MakeShipmentPlan, Mcodem DestFee = {}", moveDestFee);

        // custkey 만 추출
        List<String> uniqueCustomer = targetList.stream()
                .map(t -> t.getCustkey()) // custkey 필드만 추출
                .distinct() // 중복 제거
                .collect(Collectors.toList()); // List로 수집

        // plan 키는 1개
        // final String shpplky = shipmentPlanRepository.getNextSequenceKey();
        String shpplky = null;
        if (exsitPlan.isEmpty()) {
            shpplky = shipmentPlanRepository.getNextSequenceKey();
        } else {
            shpplky = exsitPlan.get(0).getShpplky();
        }

        final ShipmentPlan shipmentPlan = ShipmentPlan.builder()
                .compkey(compkey)
                .shpplky(shpplky)
                .useract(useract)
                .shpmtdt(uldrqdt)
                .roundno(roundno)
                .simulop(simulop)
                .ptnrkey(ptnrkey)
                .simulat(now)
                .build();

        for (String custkey : uniqueCustomer) {
            // FTL 생성
            final List<Shipment> ftlShipmentList = this.makeFtlShipmentList(compkey, custkey, targetList, custTrucks,
                    moveDestFee, response);
            shipmentPlan.addShipmentList(ftlShipmentList);
        }

        log.info("MakeShipmentPlan, TruckResult size: {}", optimizedList.getTruckResults().size());
        for (TruckResult truckInfo : optimizedList.getTruckResults()) {
            // LTL 생성
            final List<Shipment> ltlShipmentList = this.makeLtlShipmentList(compkey, targetList, areaDriverList,
                    custTrucks, truckInfo, moveDestFee, response);
            shipmentPlan.addShipmentList(ltlShipmentList);
        }

        if (optimizedList.getUnassignedItems().getLoadedItems().size() > 0) {
            // 남은 LTL 생성
            final List<Shipment> ltlShipmentList = this.makeRemainLtlShipmentList(compkey, targetList, areaDriverList,
                    custTrucks,
                    optimizedList.getUnassignedItems().getUnloadedItems(), moveDestFee, response);
            shipmentPlan.addShipmentList(ltlShipmentList);
        }

        List<String> shipmentKeyList = new ArrayList<>();
        if (shipmentPlan.getShipmentList() != null) {
            for (Shipment shipment : shipmentPlan.getShipmentList()) {
                shipmentKeyList.add(shipment.getShpmtky());
            }
        }
        response.setShpmtkyList(shipmentKeyList);

        response.setShipPlan(shipmentPlan);
        // log.info("Make ShipmentList().size = {}",
        // shipmentPlan.getShipmentList().size());
        return response;
    }

    public List<String> makeReplaceShipmentPlan(final AuthDTO authDTO,
            @NotNull final String shpmtky,
            final ShipmentVehicleTonEnum _repVhctn) {

        final String compkey = Objects.requireNonNull(authDTO).getCompany();
        final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
        ShipmentVehicleTonEnum repVhctn = _repVhctn;
        if (_repVhctn == null)
            repVhctn = ShipmentVehicleTonEnum.T05;

        List<ShipmentTargetDAO> targetList = qShipmentRepository.findShipmentOne(compkey, shpmtky);

        if (CollectionUtils.isEmpty(targetList)) {
            throw new ShipmentException(HttpStatus.NOT_FOUND, "해당하는 운송요청정보가 존재하지 않습니다.");
        }

        targetList = this.fillReplaceShipmentData(targetList);
        final LocalDate uldrqdt = targetList.get(0).getLodrqdt();

        // load,custkey 의 areaId 리스트
        List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaIdList = this.getAreaListByLoadCustkeyPartnerKey(ptnrkey,
                targetList);

        List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaDriverList = null;
        areaDriverList = this.getDriverVehicleInfoByPartnerKeyAndHoliday(ptnrkey, uldrqdt);

        List<Truck> custTrucks = this.makeReplaceTruckForLoadOptimizer(targetList.get(0).getRepVhctn(), repVhctn);

        ShipmentPlanLoadOptimizerResultDTO loadOptimizedObj = this.makeLoadOptimizer(compkey,
                SimulationOptionEnum.DESIGNATED, targetList, areaDriverList, areaIdList, custTrucks);

        final MultiTruckLoadingResult optimizedList = loadOptimizedObj.getLoadResult();
        if (optimizedList == null) {
            return null;
            // return loadOptimizedObj.getError();
        }

        targetList = this.updateReplaceTruckTonAndLoadPosition(targetList, optimizedList);

        // List<String> shpmtkyList = new ArrayList<>();
        // for ( ShipmentTargetDAO targetOne : targetList ) {
        // shpmtkyList.add(targetOne.getShpmtky());
        // }
        List<String> shpmtkyList = targetList.stream()
                .map(ShipmentTargetDAO::getShpmtky)
                .collect(Collectors.toList());

        return shpmtkyList;
    }

    @Transactional
    public ShipmentPlanMakeDummyDTO saveShipmentPlan(final AuthDTO authDTO, final String compkey,
            ShipmentPlanMakeDummyDTO shipMake) {
        if (shipMake == null) {
            return shipMake;
        }

        ShipmentPlan shipmentPlan = shipMake.getShipPlan();
        if (shipmentPlan == null) {
            return shipMake;
        }

        // 배차완료된 Plan 정보 삭제
        /*
         * List<String> oldShpmtkyList = shipMake.getShpmtkyList();
         * Boolean isFixedPlan = shipMake.getIsFixedPlan();
         * if ( isFixedPlan != null && isFixedPlan && oldShpmtkyList != null &&
         * oldShpmtkyList.size() > 0 ) {
         * log.info("Save ShipmentPlan, oldShpmtkyList. {}", oldShpmtkyList);
         * // Boolean isCheck = true 이면 chekc만 하고, isCheck = false 면 실제 DB 에서 지우도록
         * 하였습니다.
         * Boolean isCheck = false;
         * Boolean bRet = dispatchPlanService.cancelDispatch(oldShpmtkyList, isCheck);
         * if ( bRet == false ) {
         * log.info("Save ShipmentPlan, 배차완료된 운송계획은 삭제할 수 없습니다.");
         * shipMake.setErrorMsg("배차완료된 운송계획은 삭제할 수 없습니다.");
         * 
         * // 저장된 New Plan 및 oetmsky list 정보들을 삭제
         * shipMake.setShipPlan(null);
         * shipMake.setOetmskyValues(null);
         * shipMake.setManualPairList(null);
         * shipMake.setShpmtkyList(null);
         * 
         * return shipMake;
         * }
         * }
         */

        // final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
        // log.info("Save ShipmentList size = {}",
        // shipmentPlan.getShipmentList().size());
        // 동일한 운송일/회차에 대한 지난 운송계획들에 대해 삭제일자 설정
        ShipmentPlanDeletedInfoDTO deletedInfo = this
                .deleteShipmentPlanByKeyExceptFixedShipmnet(shipMake.getOldShpplky());

        final ShipmentPlan savedEntity = shipmentPlanRepository.save(shipmentPlan);
        // log.info("Saved ShipmentList size = {}",
        // savedEntity.getShipmentList().size());

        // 수동 flag 된 주문건에 대해서 신규 plan 으로 변경
        List<ShipmentOetmskyPairDTO> manualPairList = this.updateManualShipmentPlan(compkey, shipmentPlan, deletedInfo);
        shipMake.setManualPairList(manualPairList);

        shipMake.setShpplky(savedEntity.getShpplky());
        // log.info("Saved getShpplky = {}", savedEntity.getShpplky());
        shipMake.setShpmtkyCount(savedEntity.getShipmentList().size());
        shipMake.setOetmskyValues(deletedInfo.getOetmskyValues());
        shipMake.setShpmtkyList(deletedInfo.getShpmtkyList());

        // log.info("Saved deletedOetmskyValues size = {}", oetmskyValues.size());
        return shipMake;
    }

    @Transactional
    public ShipmentPlanMakeDummyDTO saveShipmentOmsItem(final AuthDTO authDTO, final String compkey,
            ShipmentPlanMakeDummyDTO shipMake) {
        if (shipMake == null) {
            return shipMake;
        }
        if (shipMake.getShipItemLists() == null) {
            return shipMake;
        }

        try {
            // ShipmentOmsItem 리스트 DB 저장
            for (List<ShipmentOmsItemDTO> slist : shipMake.getShipItemLists()) {
                for (ShipmentOmsItemDTO s : slist) {
                    // log.info("ShipmentOmsItem Shpmtky:{},Oetmsky:{}", s.getShpmtky(),
                    // s.getOetmsky());
                    ShipmentOmsItem item = shipmentOmsItemMapper.toEntity(s);
                    ShipmentOmsItem savedItem = shipmentOmsItemRepository.save(item);
                }
            }
        } catch (Exception ex) {
            log.info("Exception : {}", ex.toString());
        }

        return shipMake;
    }

    @Transactional
    public ShipmentPlan fixShipmentPlan(final AuthDTO authDTO,
            @NotBlank final String compkey,
            @NotBlank final String shpplky) {

        final ShipmentPlan shipmentPlan = shipmentPlanRepository.findByCompkeyAndShpplky(compkey, shpplky);

        if (Objects.isNull(shipmentPlan)) {
            log.info("해당하는 운송계획이 존재하지 않습니다.");
            return null;
        }
        if (Objects.nonNull(shipmentPlan.getDeletat())) {
            log.info("해당 운송계획은 삭제된 상태입니다.");
            return null;
        }
        if (Objects.nonNull(shipmentPlan.getFixedat())) {
            log.info("해당 운송계획은 이미 확정된 상태입니다.");
            return null;
        }

        List<Shipment> shipmentList = shipmentPlan.getShipmentList();
        for (Shipment shipment : shipmentList) {
            List<OetmhdTmsInfoDTO> oetmhdTmsInfoDTOS = oetmhdService.findOetmskyByShipmentKey(shipment.getShpmtky());
            if (!oetmhdTmsInfoDTOS.isEmpty()) {
                for (OetmhdTmsInfoDTO oetmhdTmsInfo : oetmhdTmsInfoDTOS) {
                    if (OrderStatusEnum.CANCEL.equals(oetmhdTmsInfo.getTshitst())) {
                        log.info("해당하는 운송 정보는 취소된 상태입니다.");
                        return null;
                    }
                }
            }
        }

        LocalDateTime nowDateTime = LocalDateTime.now();
        // shipmentPlan.setFixedat(nowDateTime);
        for (Shipment shipment : shipmentList) {
            shipment.setFixedat(nowDateTime);
        }

        return shipmentPlanRepository.save(shipmentPlan);
    }

    @Transactional
    public ShipmentPlan fixShipmentPlanByShipment(@NotNull final String compkey,
            @NotNull final String shpplky,
            @NotNull final List<String> shipmentList) {
        log.info("fixShipmentPlanByShipment -> shpplky: {}", shpplky);
        log.info("fixShipmentPlanByShipment -> shipmentList: {}", shipmentList);

        if (shipmentList == null) {
            log.info("fixShipmentPlanByShipment : shipmentList is null!!");
            return null;
        }

        final ShipmentPlan shipmentPlan = shipmentPlanRepository.findByCompkeyAndShpplky(compkey, shpplky);

        if (Objects.isNull(shipmentPlan)) {
            log.info("fixShipmentPlanByShipment : 해당하는 운송계획이 존재하지 않습니다.");
            return null;
        }
        if (Objects.nonNull(shipmentPlan.getDeletat())) {
            log.info("fixShipmentPlanByShipment : 해당 운송계획은 삭제된 상태입니다.");
            return null;
        }

        List<Shipment> oldShipmentList = shipmentPlan.getShipmentList();
        for (Shipment shipment : oldShipmentList) {
            if (shipment.getDeletat() == null && shipmentList.contains(shipment.getShpmtky())) {
                List<OetmhdTmsInfoDTO> oetmhdTmsInfoDTOS = oetmhdService
                        .findOetmskyByShipmentKey(shipment.getShpmtky());
                if (!oetmhdTmsInfoDTOS.isEmpty()) {
                    for (OetmhdTmsInfoDTO oetmhdTmsInfo : oetmhdTmsInfoDTOS) {
                        if (OrderStatusEnum.CANCEL.equals(oetmhdTmsInfo.getTshitst())) {
                            log.info("fixShipmentPlanByShipment : 해당하는 운송 정보는 취소된 상태입니다.");
                            return null;
                        }
                    }
                }
            }
        }

        LocalDateTime nowDateTime = LocalDateTime.now();
        // shipmentPlan.setFixedat(nowDateTime);
        for (Shipment shipment : oldShipmentList) {
            if (shipmentList.contains(shipment.getShpmtky())) {
                shipment.setFixedat(nowDateTime);
            }
        }

        return shipmentPlanRepository.save(shipmentPlan);
    }

    @Transactional
    public List<String> findFixedOetmskyList(@NotNull final String compkey,
            @NotNull final String shpplky) {

        final ShipmentPlan shipmentPlan = shipmentPlanRepository.findByCompkeyAndShpplky(compkey, shpplky);

        if (Objects.isNull(shipmentPlan)) {
            log.info("findFixedOetmskyList : 해당하는 운송계획이 존재하지 않습니다.");
            return null;
        }
        if (Objects.nonNull(shipmentPlan.getDeletat())) {
            log.info("findFixedOetmskyList : 해당 운송계획은 삭제된 상태입니다.");
            return null;
        }

        List<Shipment> shipmentList = shipmentPlan.getShipmentList();
        List<String> fixedOetmskyList = new ArrayList<>();
        if (!shipmentList.isEmpty()) {
            for (Shipment shipment : shipmentList) {
                if (shipment.getFixedat() != null) {
                    for (ShipmentSection shipmentSection : shipment.getShipmentSectionList()) {
                        fixedOetmskyList.add(shipmentSection.getOetmsky());
                    }
                }
            }
        }
        fixedOetmskyList = ShipmentUtils.distinctStringList(fixedOetmskyList);

        return fixedOetmskyList;
    }

    @Transactional
    public Integer updateOetmhdTshitstPlanFix(List<String> oetmskyValues) {
        Integer result1 = qShipmentPlanRepository.updateOetmhdTshitstPlanFix(oetmskyValues);
        Integer result2 = qShipmentPlanRepository.updateOetmitTshitstPlanFix(oetmskyValues);
        return result1;
    }

    public List<String> getOetmskyByShipmentPlan(final ShipmentPlan fixShipmentPlan) {

        if (fixShipmentPlan == null)
            return null;
        try {
            List<String> oetmskyValues = new ArrayList<>();
            final List<ShipmentPlan> oldShipmentPlanList = List.of(fixShipmentPlan);

            oldShipmentPlanList.forEach(oldShipmentPlan -> {
                oldShipmentPlan.getShipmentList().forEach(oldShipment -> {
                    oldShipment.getShipmentSectionList().forEach(oldShipmentSection -> {
                        oetmskyValues.add(oldShipmentSection.getOetmsky());
                    });
                });
            });

            // Oetmsky 중복제거
            final List<String> distinctOetmskyValues = oetmskyValues.stream()
                    .distinct()
                    .collect(Collectors.toList());

            return distinctOetmskyValues;
        } catch (Exception ex) {
        }

        return null;
    }

    public List<String> getOetmskyByShipmentPlanByShipment(final ShipmentPlan fixShipmentPlan,
            final List<String> shipmentList) {

        if (fixShipmentPlan == null)
            return null;
        try {
            List<String> oetmskyValues = new ArrayList<>();
            final List<ShipmentPlan> oldShipmentPlanList = List.of(fixShipmentPlan);

            oldShipmentPlanList.forEach(oldShipmentPlan -> {
                oldShipmentPlan.getShipmentList().forEach(oldShipment -> {
                    if (shipmentList.contains(oldShipment.getShpmtky())) {
                        oldShipment.getShipmentSectionList().forEach(oldShipmentSection -> {
                            oetmskyValues.add(oldShipmentSection.getOetmsky());
                        });
                    }
                });
            });

            // Oetmsky 중복제거
            final List<String> distinctOetmskyValues = oetmskyValues.stream()
                    .distinct()
                    .collect(Collectors.toList());

            return distinctOetmskyValues;
        } catch (Exception ex) {
        }

        return null;
    }

    public String getCarrierShipmentPlan(@NotBlank final String compkey,
            @NotBlank final String ptnrkey,
            final LocalDate shpmtdt,
            final Integer roundno) {

        String shpplky = null;
        try {
            ShipmentPlan shipmentPlan = shipmentPlanRepository
                    .findFirstByCompkeyAndPtnrkeyAndShpmtdtAndRoundnoAndDeletatNull(compkey, ptnrkey, shpmtdt, roundno);

            if (Objects.isNull(shipmentPlan)) {
                log.info("해당하는 운송계획이 존재하지 않습니다.");
                return null;
            }
            if (Objects.nonNull(shipmentPlan.getDeletat())) {
                log.info("해당 운송계획은 삭제된 상태입니다.");
                return null;
            }
            if (Objects.nonNull(shipmentPlan.getFixedat())) {
                log.info("해당 운송계획은 이미 확정된 상태입니다.");
                return null;
            }

            shpplky = shipmentPlan.getShpplky();

        } catch (Exception ex) {
        }
        return shpplky;
    }

    public ShipmentPlanFixPrevDTO getFixPrevPopupShipmentPlan(final AuthDTO authDTO,
            @NotBlank final String compkey,
            @NotBlank final String shpplky) {

        final List<ShipmentPlanFixDummyDTO> dummyList = qShipmentPlanRepository.findShipmentFixPrevList(compkey,
                shpplky);

        ShipmentPlanFixPrevDTO fixPrevList = new ShipmentPlanFixPrevDTO();
        List<ShipmentPlanFixPartnerDTO> partnerList = new ArrayList<>();

        // Ptnrkey 리스트 (중복제거)
        final List<String> ptnrKeys = dummyList.stream()
                .map(ShipmentPlanFixDummyDTO::getPtnrkey)
                .distinct()
                .collect(Collectors.toList());

        // ptnrKeys.forEach(ptnrKey -> log.info("ptnrKey: {}", ptnrKey));

        for (String ptnrkey : ptnrKeys) {
            // log.info("ptnrkey: {}", ptnrkey);
            Integer vehforftl = 0;
            Integer vehforltl = 0;

            ShipmentPlanFixPartnerDTO partnerOne = new ShipmentPlanFixPartnerDTO();
            List<ShipmentPlanFixShipmentDTO> shipmentList = new ArrayList<>();
            // Ptnrkey filtered 리스트
            final List<ShipmentPlanFixDummyDTO> partnerFilterdList = dummyList.stream()
                    .filter(t -> t.getPtnrkey().equals(ptnrkey))
                    .toList();

            // Shipment 리스트 (중복제거)
            final List<String> shmtKeys = partnerFilterdList.stream()
                    .map(ShipmentPlanFixDummyDTO::getShpmtky)
                    .distinct()
                    .collect(Collectors.toList());

            for (String shmtkey : shmtKeys) {
                // Shmtkey filtered 리스트
                final List<ShipmentPlanFixDummyDTO> shmtFilterdList = partnerFilterdList.stream()
                        .filter(t -> t.getShpmtky().equals(shmtkey))
                        .toList();

                ShipmentPlanFixShipmentDTO oneShip = new ShipmentPlanFixShipmentDTO();
                final ShipmentPlanFixDummyDTO item = shmtFilterdList.get(0);
                oneShip.setShpmtky(item.getShpmtky());
                oneShip.setCargoty(item.getCargoty());
                oneShip.setVhctncd(item.getVhctncd());
                oneShip.setLoadrat(item.getLoadrat());

                final Integer countofpickup = shmtFilterdList.size();
                final Integer countofdropoff = shmtFilterdList.size();
                oneShip.setCountofpickup(countofpickup);
                oneShip.setCountofdropoff(countofdropoff);

                oneShip.setEstimatedfee(item.getEstimatedfee());

                shipmentList.add(oneShip);

                if (item.getCargoty().equals(TruckLoadTypeEnum.FTL))
                    vehforftl += 1;
                if (item.getCargoty().equals(TruckLoadTypeEnum.LTL))
                    vehforltl += 1;
            }
            partnerOne.setShipmentList(shipmentList);

            final ShipmentPlanFixDummyDTO tmpPtn0 = partnerFilterdList.get(0);
            partnerOne.setPtnamlc(tmpPtn0.getPtnamlc());
            partnerOne.setPtnrkey(tmpPtn0.getPtnrkey());
            partnerOne.setPtnrtyp(tmpPtn0.getPtnrtyp());
            partnerOne.setVehforftl(vehforftl);
            partnerOne.setVehforltl(vehforltl);
            partnerOne.setTotalveh(vehforftl + vehforltl);

            partnerList.add(partnerOne);
        }
        fixPrevList.setPartnerList(partnerList);

        final ShipmentPlanFixDummyDTO tmpDummy0 = dummyList.get(0);
        fixPrevList.setRoundno(tmpDummy0.getRoundno());
        fixPrevList.setShpmtdt(tmpDummy0.getShpmtdt());
        fixPrevList.setShpplky(tmpDummy0.getShpplky());

        return fixPrevList;
    }

    @Transactional
    public void updateDispatchShipmentPlan(final ShipmentPlan shipmentPlan) {

        if (Objects.isNull(shipmentPlan)) {
            log.info("해당하는 운송계획이 존재하지 않습니다.");
            return;
        }

        log.info("update DispatchShipmentPlan, shpplky: {}", shipmentPlan.getShpplky());

        List<DispatchAddDTO.ShipmentDTO> dispatchAddList = new ArrayList<>();
        for (Shipment shipment : shipmentPlan.getShipmentList()) {
            DispatchAddDTO.ShipmentDTO addDto = new DispatchAddDTO.ShipmentDTO();
            addDto.setCompkey(shipmentPlan.getCompkey());
            addDto.setRequestDate(shipmentPlan.getShpmtdt()); // 요청 일자
            addDto.setTransportRound(shipmentPlan.getRoundno()); // 운송 회차
            addDto.setShipmentKey(shipment.getShpmtky()); // Shipment No.
            addDto.setCargoty(shipment.getCargoty()); // 단독/혼적
            addDto.setDropOffCompanyName(shipment.getPtnamlc()); // 하차 상호명

            addDto.setDriverId(shipment.getAutoDrvId());
            addDto.setVehicleId(shipment.getAutoVhcId());
            addDto.setPartnerKey(shipmentPlan.getPtnrkey());

            log.info("update DispatchShipmentPlan, Shpmtky: {}", shipment.getShpmtky());

            dispatchAddList.add(addDto);
        }

        dispatchPlanService.saveByShipment(dispatchAddList);
    }

    @Transactional
    public void updateDispatchShipmentPlanByShipmnet(final ShipmentPlan shipmentPlan,
            final List<String> shipmentList) {

        if (Objects.isNull(shipmentPlan)) {
            log.info("updateDispatchShipmentPlanByShipmnet : 해당하는 운송계획이 존재하지 않습니다.");
            return;
        }

        if (shipmentList == null) {
            log.info("updateDispatchShipmentPlanByShipmnet : shipmentList is null!!");
            return;
        }

        log.info("update DispatchShipmentPlan, shpplky: {}", shipmentPlan.getShpplky());

        List<DispatchAddDTO.ShipmentDTO> dispatchAddList = new ArrayList<>();
        for (Shipment shipment : shipmentPlan.getShipmentList()) {
            if (shipmentList.contains(shipment.getShpmtky())) {
                DispatchAddDTO.ShipmentDTO addDto = new DispatchAddDTO.ShipmentDTO();
                addDto.setCompkey(shipmentPlan.getCompkey());
                addDto.setRequestDate(shipmentPlan.getShpmtdt()); // 요청 일자
                addDto.setTransportRound(shipmentPlan.getRoundno()); // 운송 회차
                addDto.setShipmentKey(shipment.getShpmtky()); // Shipment No.
                addDto.setCargoty(shipment.getCargoty()); // 단독/혼적
                addDto.setDropOffCompanyName(shipment.getPtnamlc()); // 하차 상호명

                addDto.setDriverId(shipment.getAutoDrvId());
                addDto.setVehicleId(shipment.getAutoVhcId());
                addDto.setPartnerKey(shipmentPlan.getPtnrkey());

                log.info("update DispatchShipmentPlan, Shpmtky: {}", shipment.getShpmtky());

                dispatchAddList.add(addDto);
            }
        }

        dispatchPlanService.saveByShipment(dispatchAddList);
    }

    public List<String> getShipmentListByShpplky(final String shpplky) {

        List<String> shpmtkyList = new ArrayList<>();

        if (shpplky == null || shpplky.isBlank())
            return shpmtkyList;

        try {
            final List<ShipmentPlan> shipmentPlanList = List.of(shipmentPlanRepository.findByShpplky(shpplky));

            shipmentPlanList.forEach(shipmentPlan -> {
                shipmentPlan.getShipmentList().forEach(shipment -> {
                    if (shipment.getDeletat() == null && shipment.getFixedat() != null) {
                        shpmtkyList.add(shipment.getShpmtky());
                    }
                });
            });
        } catch (Exception ex) {
        }

        return shpmtkyList;
    }

    public List<String> getShipmentListByShpplkyExceptFixedShipment(final String shpplky) {

        List<String> shpmtkyList = new ArrayList<>();

        if (shpplky == null || shpplky.isBlank())
            return shpmtkyList;

        try {
            final List<ShipmentPlan> shipmentPlanList = List.of(shipmentPlanRepository.findByShpplky(shpplky));

            shipmentPlanList.forEach(shipmentPlan -> {
                shipmentPlan.getShipmentList().forEach(shipment -> {
                    if (shipment.getDeletat() == null && shipment.getFixedat() == null) {
                        shpmtkyList.add(shipment.getShpmtky());
                    }
                });
            });
        } catch (Exception ex) {
        }

        return shpmtkyList;
    }

    @Transactional
    public List<String> deleteShipmentPlanOne(@NotBlank final String compkey,
            @NotNull final String shpplky) {

        List<String> oetmskyValues = new ArrayList<>();
        final LocalDateTime now = LocalDateTime.now();
        final ShipmentPlan oneShipmentPlan = shipmentPlanRepository.findByCompkeyAndShpplky(compkey, shpplky);

        if (oneShipmentPlan != null) {
            final List<ShipmentPlan> oldShipmentPlanList = List.of(oneShipmentPlan);

            if (oldShipmentPlanList != null && oldShipmentPlanList.size() > 0) {
                oldShipmentPlanList.forEach(oldShipmentPlan -> {
                    oldShipmentPlan.setDeletat(now);
                    oldShipmentPlan.getShipmentList().forEach(oldShipment -> {
                        oldShipment.setDeletat(now);
                        oldShipment.getShipmentSectionList().forEach(oldShipmentSection -> {
                            oldShipmentSection.setDeletat(now);
                            oetmskyValues.add(oldShipmentSection.getOetmsky());
                        });
                    });

                    shipmentPlanRepository.save(oldShipmentPlan);
                });
            }
        }

        return oetmskyValues;
    }

    @Transactional
    public ShipmentPlanDeletedInfoDTO deleteShipmentPlanByKey(@NotBlank final String oldShpplky) {

        ShipmentPlanDeletedInfoDTO deletedInfo = new ShipmentPlanDeletedInfoDTO();

        if (oldShpplky == null || oldShpplky.isBlank())
            return deletedInfo;

        try {
            List<ShipmentSectionOneDTO> manualList = new ArrayList<>();
            List<String> shpmtkyList = new ArrayList<>();
            List<String> oetmskyValues = new ArrayList<>();

            final LocalDateTime now = LocalDateTime.now();
            final List<ShipmentPlan> oldShipmentPlanList = List.of(shipmentPlanRepository.findByShpplky(oldShpplky));

            oldShipmentPlanList.forEach(oldShipmentPlan -> {
                log.info("delete plan : {}", oldShipmentPlan.getShpplky()); // forDEBUG
                oldShipmentPlan.setDeletat(now);
                oldShipmentPlan.getShipmentList().forEach(oldShipment -> {
                    // delete log log.info("delete shmt : {}", oldShipment.getShpmtky()); //forDEBUG
                    shpmtkyList.add(oldShipment.getShpmtky());
                    oldShipment.setDeletat(now);
                    oldShipment.getShipmentSectionList().forEach(oldShipmentSection -> {
                        // delete log log.info("delete shsc : {}", oldShipmentSection.getShpscky());
                        // //forDEBUG
                        oldShipmentSection.setDeletat(now);
                        oetmskyValues.add(oldShipmentSection.getOetmsky());
                        // delete log log.info("delete add Oetm : {}", oldShipmentSection.getOetmsky());
                        // //forDEBUG
                        if (oldShipmentSection.getManlshp()) {
                            ShipmentSectionOneDTO manualEntry = new ShipmentSectionOneDTO();
                            manualEntry.setShpl(oldShipmentPlan.getShpplky());
                            manualEntry.setShmt(oldShipment.getShpmtky());
                            manualEntry.setShsc(oldShipmentSection.getShpscky());
                            manualEntry.setOetmsky(oldShipmentSection.getOetmsky());
                            manualList.add(manualEntry);
                            log.info("delete                 manAdd : {}/{}", manualEntry.getShsc(),
                                    manualEntry.getOetmsky()); // forDEBUG
                        }
                    });
                });

                shipmentPlanRepository.save(oldShipmentPlan);
            });

            // Oetmsky 중복제거
            final List<String> deletedOetmskyValues = oetmskyValues.stream()
                    .distinct()
                    .collect(Collectors.toList());

            deletedInfo.setOetmskyValues(deletedOetmskyValues);
            deletedInfo.setOetmskyManualList(manualList);
            deletedInfo.setShpmtkyList(shpmtkyList);

            shipmentPlanRepository.flush();

        } catch (Exception ex) {
        }

        return deletedInfo;
    }

    @Transactional
    public ShipmentPlanDeletedInfoDTO deleteShipmentPlanByKeyExceptFixedShipmnet(@NotBlank final String oldShpplky) {

        ShipmentPlanDeletedInfoDTO deletedInfo = new ShipmentPlanDeletedInfoDTO();

        if (oldShpplky == null || oldShpplky.isBlank())
            return deletedInfo;

        try {
            List<ShipmentSectionOneDTO> manualList = new ArrayList<>();
            List<String> shpmtkyList = new ArrayList<>();
            List<String> oetmskyValues = new ArrayList<>();

            final LocalDateTime now = LocalDateTime.now();
            final List<ShipmentPlan> oldShipmentPlanList = List.of(shipmentPlanRepository.findByShpplky(oldShpplky));

            AtomicBoolean isFixedShipment = new AtomicBoolean(false);
            oldShipmentPlanList.forEach(oldShipmentPlan -> {
                log.info("delete plan : {}", oldShipmentPlan.getShpplky()); // forDEBUG
                oldShipmentPlan.setDeletat(now);
                isFixedShipment.set(false);
                oldShipmentPlan.getShipmentList().forEach(oldShipment -> {
                    log.info("delete shmt : {}", oldShipment.getShpmtky()); // forDEBUG
                    if (oldShipment.getFixedat() != null) {
                        isFixedShipment.set(true);
                    } else {
                        shpmtkyList.add(oldShipment.getShpmtky());
                        oldShipment.setDeletat(now);
                        oldShipment.getShipmentSectionList().forEach(oldShipmentSection -> {
                            log.info("delete shsc : {}", oldShipmentSection.getShpscky()); // forDEBUG
                            oldShipmentSection.setDeletat(now);
                            oetmskyValues.add(oldShipmentSection.getOetmsky());
                            log.info("delete add Oetm : {}", oldShipmentSection.getOetmsky()); // forDEBUG
                            if (oldShipmentSection.getManlshp()) {
                                ShipmentSectionOneDTO manualEntry = new ShipmentSectionOneDTO();
                                manualEntry.setShpl(oldShipmentPlan.getShpplky());
                                manualEntry.setShmt(oldShipment.getShpmtky());
                                manualEntry.setShsc(oldShipmentSection.getShpscky());
                                manualEntry.setOetmsky(oldShipmentSection.getOetmsky());
                                manualList.add(manualEntry);
                                log.info("delete manAdd : {}/{}", manualEntry.getShsc(), manualEntry.getOetmsky()); // forDEBUG
                            }
                        });
                    }
                });

                if (isFixedShipment.get()) {
                    oldShipmentPlan.setDeletat(null);
                }

                shipmentPlanRepository.save(oldShipmentPlan);
            });

            // Oetmsky 중복제거
            final List<String> deletedOetmskyValues = oetmskyValues.stream()
                    .distinct()
                    .collect(Collectors.toList());

            deletedInfo.setOetmskyValues(deletedOetmskyValues);
            deletedInfo.setOetmskyManualList(manualList);
            deletedInfo.setShpmtkyList(shpmtkyList);

            shipmentPlanRepository.flush();

        } catch (Exception ex) {
        }

        return deletedInfo;
    }

    @Transactional
    public Long deleteShipmentOmsItems(final List<String> shpmtkyList) {

        Long result = 0L;

        if (shpmtkyList == null || shpmtkyList.size() < 1)
            return result;

        try {
            for (String shpmtky : shpmtkyList) {
                log.info("Delete ShipmentOmsItems, shpmtky : {}", shpmtky);
                // result += qShipmentOmsItemRepository.deleteShipmentOmsItems(shpmtky); //
                // LOCALTEST
            }
        } catch (Exception ex) {
        }

        return result;
    }

    @Transactional
    public ShipmentPlanDeletedInfoDTO deleteShipmentPlans(final AuthDTO authDTO,
            @NotBlank final String compkey,
            @NotNull final LocalDate uldrqdt,
            @NotNull final Integer roundno,
            // final Boolean isFixedPlan,
            List<String> oldShpmtkyList) {

        ShipmentPlanDeletedInfoDTO deletedInfo = new ShipmentPlanDeletedInfoDTO();

        final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();

        List<ShipmentSectionOneDTO> manualList = new ArrayList<>();
        List<String> shpmtkyList = new ArrayList<>();
        List<String> oetmskyValues = new ArrayList<>();
        final LocalDateTime now = LocalDateTime.now();

        log.info("Delete ShipmentPlans, ptnrkey:{}, uldrqdt:{}, roundno:{}", ptnrkey, uldrqdt, roundno);
        final List<ShipmentPlan> oldShipmentPlanList = shipmentPlanRepository
                .findByPtnrkeyAndCompkeyAndShpmtdtAndRoundnoAndDeletatNull(ptnrkey, compkey, uldrqdt, roundno);

        try {
            /*
             * if ( isFixedPlan && ( oldShpmtkyList != null && oldShpmtkyList.size() > 0 ) )
             * {
             * log.info("Delete ShipmentPlans, oldShpmtkyList. {}", oldShpmtkyList);
             * // Boolean isCheck = true 이면 chekc만 하고, isCheck = false 면 실제 DB 에서 지우도록
             * 하였습니다.
             * Boolean isCheck = false;
             * Boolean bRet = dispatchPlanService.cancelDispatch(oldShpmtkyList, isCheck);
             * if ( bRet == false ) {
             * log.info("Delete ShipmentPlans, 배차완료된 운송계획은 삭제할 수 없습니다.");
             * // 배차확정이 아니더라도 배차에 리스트가 없으면 false 결과
             * return deletedInfo;
             * } else {
             * log.info("DeletePlan, clear Dispatch. {}", oldShpmtkyList);
             * }
             * }
             */

            log.info("Delete ShipmentPlans, oldShipmentPlanList:{}", oldShipmentPlanList);
            oldShipmentPlanList.forEach(oldShipmentPlan -> {
                log.info("Delete ShipmentPlans, shpplky:{}", oldShipmentPlan.getShpplky());
                deletedInfo.setShpplky(oldShipmentPlan.getShpplky());
                oldShipmentPlan.setDeletat(now);
                oldShipmentPlan.getShipmentList().forEach(oldShipment -> {
                    // delete log log.info("Delete ShipmentPlans, shpmtky:{}",
                    // oldShipment.getShpmtky());
                    shpmtkyList.add(oldShipment.getShpmtky());
                    oldShipment.setDeletat(now);
                    oldShipment.getShipmentSectionList().forEach(oldShipmentSection -> {
                        // delete log log.info("Delete ShipmentPlans, shpscky:{},oetmsky:{}",
                        // oldShipmentSection.getShpscky(), oldShipmentSection.getOetmsky());
                        oldShipmentSection.setDeletat(now);
                        oetmskyValues.add(oldShipmentSection.getOetmsky());
                        if (oldShipmentSection.getManlshp()) {
                            ShipmentSectionOneDTO manualEntry = new ShipmentSectionOneDTO();
                            manualEntry.setShpl(oldShipmentPlan.getShpplky());
                            manualEntry.setShmt(oldShipment.getShpmtky());
                            manualEntry.setShsc(oldShipmentSection.getShpscky());
                            manualEntry.setOetmsky(oldShipmentSection.getOetmsky());
                            manualList.add(manualEntry);
                        }
                    });
                });

                shipmentPlanRepository.save(oldShipmentPlan);
            });

            // Oetmsky 중복제거
            final List<String> deletedOetmskyValues = oetmskyValues.stream()
                    .distinct()
                    .collect(Collectors.toList());

            deletedInfo.setOetmskyValues(deletedOetmskyValues);
            deletedInfo.setOetmskyManualList(manualList);
            deletedInfo.setShpmtkyList(shpmtkyList);

        } catch (Exception ex) {
        }

        return deletedInfo;
    }

    @Transactional
    public void clearDeletedShipmentPlans() {
        log.info("clearDeletedShipmentPlans");
        List<ShipmentPlan> shipmentPlanList = shipmentPlanRepository.findAllByDeletatIsNotNull();

        List<ShipmentPlanDTO> shipmentPlanDTOList = shipmentPlanMapper.toDto(shipmentPlanList);
        if (shipmentPlanDTOList != null && !shipmentPlanDTOList.isEmpty()) {
            for (ShipmentPlanDTO shipmentPlanDTO : shipmentPlanDTOList) {
                ShipmentPlan shipmentPlan = shipmentPlanMapper.toEntity(shipmentPlanDTO);
                shipmentPlanRepository.delete(shipmentPlan);
            }
        }
    }

    @Transactional
    public void clearDeletedShipments() {
        log.info("clearDeletedShipments");
        List<Shipment> shipmentList = shipmentRepository.findAllByDeletatIsNotNull();

        List<ShipmentDTO> shipmentDTOList = shipmentMapper.toDto(shipmentList);
        if (shipmentDTOList != null && !shipmentDTOList.isEmpty()) {
            for (ShipmentDTO shipmentDTO : shipmentDTOList) {
                Shipment shipment = shipmentMapper.toEntity(shipmentDTO);
                shipmentRepository.delete(shipment);
            }
        }
    }

    @Transactional
    public ShipmentPlanDeletedInfoDTO deleteSelectedShipmentPlans(final AuthDTO authDTO,
            @NotBlank final String compkey,
            @NotNull final LocalDate uldrqdt,
            @NotNull final Integer roundno,
            @NotNull final List<String> cancelOetmskyList) {

        ShipmentPlanDeletedInfoDTO deletedInfo = new ShipmentPlanDeletedInfoDTO();

        final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();

        List<ShipmentSectionOneDTO> manualList = new ArrayList<>();
        List<String> shpmtkyList = new ArrayList<>();
        List<String> oetmskyValues = new ArrayList<>();
        final LocalDateTime now = LocalDateTime.now();

        log.info("Delete ShipmentPlans, ptnrkey:{}, uldrqdt:{}, roundno:{}", ptnrkey, uldrqdt, roundno);
        final List<ShipmentPlan> oldShipmentPlanList = shipmentPlanRepository
                .findByPtnrkeyAndCompkeyAndShpmtdtAndRoundnoAndDeletatNull(ptnrkey, compkey, uldrqdt, roundno);

        try {
            log.info("Delete ShipmentPlans, oldShipmentPlanList:{}", oldShipmentPlanList);
            oldShipmentPlanList.forEach(oldShipmentPlan -> {
                log.info("Delete ShipmentPlans, shpplky:{}", oldShipmentPlan.getShpplky());
                deletedInfo.setShpplky(oldShipmentPlan.getShpplky());
                oldShipmentPlan.setDeletat(now);
                oldShipmentPlan.getShipmentList().forEach(oldShipment -> {
                    log.info("Delete ShipmentPlans,   shpmtky:{}", oldShipment.getShpmtky());
                    shpmtkyList.add(oldShipment.getShpmtky());
                    oldShipment.setDeletat(now);
                    oldShipment.setSimulat(null);
                    oldShipment.setFixedat(null);
                    oldShipment.getShipmentSectionList().forEach(oldShipmentSection -> {
                        log.info("Delete ShipmentPlans,     shpscky:{},oetmsky:{}", oldShipmentSection.getShpscky(),
                                oldShipmentSection.getOetmsky());
                        oldShipmentSection.setDeletat(now);
                        oetmskyValues.add(oldShipmentSection.getOetmsky());
                        if (oldShipmentSection.getManlshp()) {
                            ShipmentSectionOneDTO manualEntry = new ShipmentSectionOneDTO();
                            manualEntry.setShpl(oldShipmentPlan.getShpplky());
                            manualEntry.setShmt(oldShipment.getShpmtky());
                            manualEntry.setShsc(oldShipmentSection.getShpscky());
                            manualEntry.setOetmsky(oldShipmentSection.getOetmsky());
                            manualList.add(manualEntry);
                        }
                    });
                });

                shipmentPlanRepository.save(oldShipmentPlan);
            });

            // Oetmsky 중복제거
            final List<String> deletedOetmskyValues = oetmskyValues.stream()
                    .distinct()
                    .collect(Collectors.toList());

            List<String> reOetmskyList = new ArrayList<>();
            for (String oetmsky : deletedOetmskyValues) {
                if (!cancelOetmskyList.contains(oetmsky)) {
                    reOetmskyList.add(oetmsky);
                }
            }
            log.info("deleteSelectedShipmentPlans, reOetmskyList -> {}", reOetmskyList);

            if (reOetmskyList != null && !reOetmskyList.isEmpty()) {
                log.info("Delete ShipmentPlans, oldShipmentPlanList:{}", oldShipmentPlanList);
                oldShipmentPlanList.forEach(oldShipmentPlan -> {
                    log.info("Delete ShipmentPlans, shpplky:{}", oldShipmentPlan.getShpplky());
                    deletedInfo.setShpplky(oldShipmentPlan.getShpplky());
                    oldShipmentPlan.setDeletat(null);
                    oldShipmentPlan.setFixedat(null);

                    shipmentPlanRepository.save(oldShipmentPlan);
                });
            }

            deletedInfo.setOetmskyValues(deletedOetmskyValues);
            deletedInfo.setOetmskyManualList(manualList);
            deletedInfo.setShpmtkyList(shpmtkyList);

        } catch (Exception ex) {
        }

        return deletedInfo;
    }

    // public void deleteTriggerShipmentPlan(@NotBlank final String compkey,
    // @NotNull final LocalDate uldrqdt,
    // @NotNull final Integer roundno) {

    // ShipmentPlanDeletedInfoDTO deletedInfo = this.deleteShipmentPlans(compkey,
    // uldrqdt, roundno);
    // this.clearOetmhdTmshpno(deletedInfo.getOetmskyValues());
    // }

    public Optional<ShipmentPlanDTO> getShipmentPlan(final AuthDTO authDTO,
            @NotBlank final String compkey,
            @NotBlank final String shpplky) {

        final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
        return Optional.ofNullable(qShipmentPlanRepository.findShipmentPlan(ptnrkey, compkey, shpplky))
                .map(shipmentPlanMapper::toDto);
    }

    public Page<ShipmentPlanDTO> getShipmentPlanPage(@NotBlank final String compkey,
            @Nullable final Pageable pageable) {

        return qShipmentPlanRepository.findShipmentPlanPage(compkey, pageable)
                .map(shipmentPlanMapper::toDto);
    }

    @Deprecated
    public List<ShipmentPlanDTO> getAll() {

        return shipmentPlanRepository.findAll().stream()
                .map(shipmentPlanMapper::toDto)
                .collect(Collectors.toList());
    }

    @Deprecated
    @Transactional
    public ShipmentPlanDTO save(final ShipmentPlanDTO shipmentPlanDTO) {

        final ShipmentPlan entity = shipmentPlanMapper.toEntity(shipmentPlanDTO);
        final ShipmentPlan savedEntity = shipmentPlanRepository.save(entity);

        return shipmentPlanMapper.toDto(savedEntity);
    }

    // load,custkey 의 areaId 리스트
    List<PickUpDropOffAreaDTO.AreaShipmentResponse> getAreaListByLoadCustkeyPartnerKey(final String ptnrkey,
            List<ShipmentTargetDAO> targetList) {
        // Set 을 사용하여 (LoadKey, CustKey Pair) 중복 제거
        Set<String> uniqueKeys = new HashSet<>();
        List<ShipmentTargetDAO> distinctList = targetList.stream()
                .filter(item -> uniqueKeys.add(item.getLoadkey() + ":" + item.getCustkey()))
                .collect(Collectors.toList());

        List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaList = new ArrayList<>();
        for (ShipmentTargetDAO one : distinctList) {
            List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaInfo = pickUpDropOffAreaService
                    .findDestination2PickDropAreaByLoadKeyAndCustKey(
                            one.getLoadkey(), one.getCustkey(), ptnrkey);
            if (areaInfo != null && areaInfo.size() > 0) {
                for (PickUpDropOffAreaDTO.AreaShipmentResponse area : areaInfo) {
                    areaList.add(area);
                }
            } else {
                // delete log log.info("areaInfo size is zero. {}/{}", one.getLoadkey(),
                // one.getCustkey());
            }
        }
        return areaList;
    }

    List<PickUpDropOffAreaDTO.AreaShipmentResponse> getDriverVehicleInfoByPartnerKeyAndHoliday(final String partnerKey,
            final LocalDate requestDate) {

        List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaList = new ArrayList<>();
        List<DriverInfoDTO.DriverVehicleInfo> drvlist = null;
        try {
            drvlist = driverInfoCustomService.findDriverVehicleInfoByPartnerKeyAndHoliday(partnerKey, requestDate);
            for (DriverInfoDTO.DriverVehicleInfo drv : drvlist) {
                PickUpDropOffAreaDTO.AreaShipmentResponse area = new PickUpDropOffAreaDTO.AreaShipmentResponse();
                area.setVehicleWidth(drv.getVehicleWidth());
                area.setVehicleLength(drv.getVehicleLength());
                area.setVehicleHeight(drv.getVehicleHeight());
                area.setMaxPayload(drv.getMaxPayload());
                area.setVehicleWeight(drv.getVehicleWeight());
                area.setAreaName(drv.getAreaName());
                area.setAreaId(drv.getAreaId());
                area.setIsHoliday(drv.getIsHoliday());
                area.setVehicleType(drv.getVehicleType());
                area.setLoadingRate(drv.getLoadingRate());
                area.setVehicleId(drv.getVehicleId());
                area.setVehicleType(drv.getVehicleType());
                area.setLoadingPriority(drv.getLoadingPriority());
                area.setUnloadingCondition(drv.getUnloadingCondition());
                area.setUserasq(drv.getUserasq());

                areaList.add(area);
            }
        } catch (Exception ex) {
        }
        return areaList;
    }

    List<PickUpDropOffAreaDTO.AreaShipmentResponse> getDestAreaForLoadOptimizer(
            List<ShipmentTargetDAO> targetList,
            final String partnerKey,
            final LocalDate reqDate) {

        // Set 을 사용하여 (LoadKey, CustKey Pair) 중복 제거
        Set<String> uniqueKeys = new HashSet<>();
        List<ShipmentTargetDAO> distinctList = targetList.stream()
                .filter(item -> uniqueKeys.add(item.getLoadkey() + ":" + item.getCustkey()))
                .collect(Collectors.toList());

        List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaList = new ArrayList<>();
        for (ShipmentTargetDAO one : distinctList) {
            List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaInfo = pickUpDropOffAreaService
                    .findDestinationPickDropAreaByLoadKeyAndCustKey(
                            one.getLoadkey(), one.getCustkey(), partnerKey, reqDate);
            if (areaInfo.size() > 0) {
                if (areaInfo.get(0).getIsHoliday() == false) {
                    for (PickUpDropOffAreaDTO.AreaShipmentResponse area : areaInfo) {
                        areaList.add(area);
                    }
                } else {
                    log.info("Holiday DriverId : {}/{}", areaInfo.get(0).getAreaDriverId(),
                            areaInfo.get(0).getVehicleType());
                }
            } else {
                log.info("areaInfo size is zero");
            }
        }

        return areaList;
    }

    String getAreaIdOne(final String loadKey, final String custKey,
            final List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaIdList) {

        try {
            for (PickUpDropOffAreaDTO.AreaShipmentResponse area : areaIdList) {
                if (area.getDestkey().equals(loadKey) && area.getCustomerCode().equals(custKey)) {
                    log.info("AreaID One : {}/{}/{}", loadKey, custKey, area.getAreaId());
                    return String.valueOf(area.getAreaId());
                } else {
                    // log.info("AreaID NotFound : {}/{}", loadKey, custKey);
                }
            }
            // delete log log.info("AreaID NotFound : {}/{}", loadKey, custKey);
        } catch (Exception ex) {
        }

        return null;
    }

    List<Truck> makeTruckForLoadOptimizer(@NotBlank final String compkey,
            final List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaDriverList,
            final List<ShipmentTargetDAO> targetList) {

        List<Truck> trucks = new ArrayList<>();

        try {
            for (PickUpDropOffAreaDTO.AreaShipmentResponse one : areaDriverList) {
                if (one.getIsHoliday()) {
                    log.info("MakeTruck ForLoadOptimizer, IsHoliday: {}/{}/{}", one.getUserasq(), one.getVehicleId(),
                            one.getAreaId());
                    continue;
                }
                if (one.getVehicleType() == null)
                    continue;
                if (one.getVehicleId() == null)
                    continue;
                // log.info("MakeTruck ForLoadOptimizer, Userasq: {}/{}/{}/{}",
                // one.getUserasq(), one.getVehicleType(), one.getLoadingPriority(),
                // one.getUnloadingCondition());
                trucks.add(Truck.builder()
                        .id(one.getVehicleId().toString())
                        .width(one.getVehicleWidth())
                        .depth(one.getVehicleLength())
                        .height(one.getVehicleHeight())
                        .maxWeight((int) (one.getMaxPayload() * 1000))
                        .vehicleWeight(one.getVehicleWeight())
                        .groupId(String.valueOf(one.getAreaId())) // 트럭 권역 구분자 (고유 ID)
                        .type(TruckType.fromVehicleTypeEnum(one.getVehicleType())) // 트럭 유형
                        .volumeUtilization(one.getLoadingRate()) // 최대 적재 용량
                        // TODO. 시간이 많이 걸려서 임시로 주석처리 (250122)
                        // .loadingPriority(one.getLoadingPriority()) // 적재우선순위
                        // .unloadingCondition(one.getUnloadingCondition()) // 우선하차기준정보
                        // .position() // 트럭 현재 위치
                        .build());
            }
        } catch (Exception ex) {
            log.error("Truck exception: {}", ex);
        }

        // 생성된 truck 이 없으면 default 로 1개 생성 (250103 삭제)
        // if ( trucks.size() < 1 ) {
        // log.info("Empty Truck : size {}",trucks.size());
        // trucks.add(Truck.builder()
        // .id("DEFAULT")
        // .width(2200)
        // .depth(8800)
        // .height(2900)
        // .maxWeight(11*1000)
        // .vehicleWeight(VehicleWeightTypeEnum.T11)
        // .groupId(targetList.get(0).getCustkey()) // 트럭 권역 구분자
        // .type(TruckType.CARGO) // 트럭 유형
        // .volumeUtilization(80f) // 최대 적재 용량
        // // .position() // 트럭 현재 위치
        // .build());
        // }

        // DEBUGLOG
        for (Truck t : trucks) {
            log.info("Maked Truck {}/{}/{}/{}", t.getId(), t.getVehicleWeight(), t.getVolumeUtilization(),
                    t.getGroupId());
        }

        return trucks;
    }

    List<Truck> makeReplaceTruckForLoadOptimizer(final ShipmentVehicleTonEnum repVhctnDB,
            final ShipmentVehicleTonEnum repVhctnIn) {

        List<Truck> trucks = new ArrayList<>();

        trucks.add(Truck.builder()
                .id("REPLACE")
                .width(null)
                .depth(null)
                .height(null)
                .maxWeight(repVhctnIn.getWeightKg())
                .vehicleWeight(ShipmentVehicleTonEnum.toVehicleWeightTypeEnum(repVhctnIn))
                .groupId(null) // 트럭 권역 구분자
                .type(null) // 트럭 유형
                .volumeUtilization(null) // 최대 적재 용량
                // .position() // 트럭 현재 위치
                .build());

        return trucks;
    }

    ShipmentPlanLoadOptimizerResultDTO makeLoadOptimizer(@NotBlank final String compkey,
            final SimulationOptionEnum simulop,
            final List<ShipmentTargetDAO> targetList,
            final List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaDriverList,
            final List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaIdList,
            final List<Truck> custTrucks) {

        ShipmentPlanLoadOptimizerResultDTO resultLoadOpt = new ShipmentPlanLoadOptimizerResultDTO();
        try {

            List<Item> items = new ArrayList<Item>();

            targetList.stream()
                    .filter(dao -> TruckLoadTypeEnum.LTL.equals(dao.getCargoty()))
                    .forEach(t -> {
                        Address pickupAddress = Address.builder()
                                .longitude(t.getPklongi().doubleValue())
                                .latitude(t.getPklatit().doubleValue())
                                .build();
                        Address dropAddress = Address.builder()
                                .longitude(t.getDplongi().doubleValue())
                                .latitude(t.getDplatit().doubleValue())
                                .build();

                        String groupId = this.getAreaIdOne(t.getLoadkey(), t.getCustkey(), areaIdList);
                        log.info("Make LoadOptimizer, Oetmsky:{} / GID:{} / TURG:{}", t.getOetmsky(), groupId,
                                t.getTurgtyn());
                        items.add(Item.builder()
                                .id(t.getOetmsky())
                                .width(t.getPakwidh())
                                .depth(t.getPakdept())
                                .height(t.getPakheig())
                                .weight(t.getPkrweig().intValue())
                                .shipyardGroupKey(t.getCustkey())
                                .groupId(groupId)
                                .pickupAddress(pickupAddress)
                                .dropAddress(dropAddress)
                                .pickupTime(LocalDateTime.of(t.getLodrqdt(), t.getLodrqtm()))
                                .dropTime(LocalDateTime.of(t.getUldrqdt(), t.getUldrqtm()))
                                .pickupPartnerType(t.getPuptnrty().toEnglish()) // 상차 파트너 타입
                                .dropoffPartnerType(t.getPtnrtyp().toEnglish()) // 하차 파트너 타입
                                .packagingOneSideType(t.getOnsidty()) // 포장ONE-SIDE 타입
                                .palletBoxQuantity(t.getOpakqty()) // 팔레트 박스 수량
                                .packagingForm(t.getOpkcate().toEnglish()) // 포장형태
                                .packagingType(t.getOpktype().toEnglish()) // 포장타입
                                .bottomStackable(t.getBotskyn()) // 하단 적재 가능 여부
                                .topStackable(t.getTopskyn()) // 상단 적재 가능 여부
                                // -- 상차지 적재 불가능한 상한
                                .pickupCannotLoadOverTonnage(VehicleWeightTypeEnum.unavatToEnum(t.getPkavat1()))
                                // -- 하차지 적재 불가능한 상한
                                .dropoffCannotLoadOverTonnage(VehicleWeightTypeEnum.unavatToEnum(t.getDpavat1()))
                                // -- 상차지 적재 불가능한 하한
                                // .pickupCannotLoadUnderTonnage()
                                // -- 하차지 적재 불가능한 하한
                                // .dropoffCannotLoadUnderTonnage()
                                .build());
                    });

            ClusterOption cOpt = ClusterOption.GROUP_BASED;
            if (simulop.equals(SimulationOptionEnum.EQUALLY)) {
                cOpt = ClusterOption.EQUAL_DISTRIBUTION;
            }

            /*
             * delete log
             * for (Item t : items) {
             * log.info("Make LoadOptimizer, item:{}/{}/{} /{}/{} /{}/{}"
             * , t.getId(), t.getGroupId(), t.getShipyardGroupKey()
             * , t.getPickupTime(), t.getDropTime()
             * , t.getPickupCannotLoadOverTonnage(), t.getDropoffCannotLoadOverTonnage());
             * }
             */

            // Call optimizeMultipleTrucksRequestDto
            OptimizeMultipleTrucksRequestDto request = OptimizeMultipleTrucksRequestDto.builder()
                    .clusterOption(cOpt)
                    .trucks(custTrucks)
                    .items(items)
                    .build();

            // Call optimizeMultipleTrucks
            MultiTruckLoadingResult loadResult = loadOptimizeFeignClient.optimizeMultipleTrucks(request);
            resultLoadOpt.setLoadResult(loadResult);
        } catch (Exception e) {
            log.info("Error :: {}", e.toString());
            resultLoadOpt.setErrorMsg(e.toString());
        }

        return resultLoadOpt;
    }

    private Integer callLBSforFTL(BigDecimal puX, BigDecimal puY, BigDecimal dfX, BigDecimal dfY) {
        Integer estime = 0;
        try {
            log.info("call LBSforFTL. ... ");
            Double startX = NumberUtils.BigDecimalToDouble(puX);
            Double startY = NumberUtils.BigDecimalToDouble(puY);
            log.info("call LBSforFTL. Start {},{}", startX, startY);
            Double destX = NumberUtils.BigDecimalToDouble(dfX);
            Double destY = NumberUtils.BigDecimalToDouble(dfY);

            List<LbsRouteDestination> destinations = new ArrayList<>();
            LbsRouteDestination destination1 = LbsRouteDestination.builder()
                    .nId((long) 1)
                    .coordinate(Objects.requireNonNull(GeometryUtils.createPoint(destX, destY)))
                    .build();
            destinations.add(destination1);

            log.info("call LBSforFTL. Make Request Body ... {}", destinations.size());
            final LbsRouteOnDemandRequest requestBody = LbsRouteOnDemandRequest.builder()
                    .requestId(Long.valueOf(NumberUtils.generateRandomNumeric(9)))
                    .start(Objects.requireNonNull(GeometryUtils.createPoint(startX, startY)))
                    .routeOption(LbsConstant.ROUTE_OPTION_DEFAULT)
                    .destinations(destinations)
                    .build();

            log.info("call LBSforFTL. routeOnDemand ...");
            LbsRouteOnDemandResponse routeResult = lbsRouteService.routeOnDemand(requestBody);
            List<LbsRouteOnDemandResponse.LbsRouteOrderResult> routeResList = routeResult.getVisitList();
            if (routeResList == null || routeResList.size() < 1) {
                log.info("call LBSforFTL. routeResList is null or size 0");
                return estime;
            }
            log.info("call LBSforFTL. routeResList size : {}", routeResList.size());
            for (LbsRouteOnDemandResponse.LbsRouteOrderResult rt : routeResList) {
                estime = rt.getEstimatedSecs();
                log.info("call LBSforFTL. routeResList estime : {}", estime);
            }
        } catch (Exception ex) {
        }

        return estime;
    }

    private List<Shipment> makeFtlShipmentList(@NotBlank final String compkey,
            @NotBlank final String custkey,
            final List<ShipmentTargetDAO> targetList,
            final List<Truck> custTrucks,
            final Integer moveDestFee,
            ShipmentPlanMakeDummyDTO response) {

        LoadOptimizerTruckCapacityDTO truckCap = this.calcDefaultTruck5TCapacity();
        VehicleTypeEnum vehicleType = VehicleTypeEnum.카고; // default

        final List<ShipmentTargetDAO> ftlTargetList = targetList.stream()
                .filter(dao -> TruckLoadTypeEnum.FTL.equals(dao.getCargoty()))
                .toList();

        // AtomicString shpmtky = new AtomicString();

        final List<Shipment> ftlShipmentList = ftlTargetList.stream()
                .filter(t -> t.getCustkey().equals(custkey))
                .map(t -> {

                    // Call LBS for ESTime
                    final Integer estime = this.callLBSforFTL(t.getPklongi(), t.getPklatit(), t.getDplongi(),
                            t.getDplatit());
                    log.info("make FTL ship Sc. estime : {}", estime);

                    Integer loadCap = this.calcLoadPaletteCapacity(t, truckCap);
                    loadCap = 100; // FTL 강제 100 (241226 고병진)
                    final ShipmentVehicleTonEnum vhctnDef = t.getOpvhton();

                    ShipmentTargetDAO _oneDest = t;
                    String shmtCustkey = ShipmentConstant.SHIPMENT_DEFAULT_OWNER_CUSTKEY;
                    String shmtCunamlc = ShipmentConstant.SHIPMENT_DEFAULT_OWNER_CUNAMLC;
                    PartnerTypeEnum shmtPtnrtyp = PartnerTypeEnum.OWNER;
                    if (_oneDest != null) {
                        if (PartnerTypeEnum.CUSTOMER.equals(_oneDest.getPtnrtyp())) {
                            shmtPtnrtyp = _oneDest.getPtnrtyp();
                            shmtCustkey = _oneDest.getCustkey();
                            shmtCunamlc = _oneDest.getCunamlc();
                        } else {
                            shmtPtnrtyp = PartnerTypeEnum.OWNER;
                            shmtCustkey = ShipmentConstant.SHIPMENT_DEFAULT_OWNER_CUSTKEY;
                            shmtCunamlc = ShipmentConstant.SHIPMENT_DEFAULT_OWNER_CUNAMLC;
                        }
                    }

                    final String shpmtky = shipmentRepository.getNextSequenceKey();
                    final Shipment shipment = Shipment.builder()
                            .compkey(compkey)
                            .shpmtky(shpmtky)
                            .vhctncd(vhctnDef)
                            .vehicleType(vehicleType)
                            .cargoty(TruckLoadTypeEnum.FTL)
                            .ptnrkey(shmtCustkey)
                            .ptnamlc(shmtCunamlc)
                            .ptnrtyp(shmtPtnrtyp)
                            .simulat(LocalDateTime.now())
                            .build();

                    final AtomicInteger visitOrder = new AtomicInteger(1);

                    final ShipmentSection pickupShipmentSection = ShipmentSection.builder()
                            .compkey(compkey)
                            .shpscky(shipmentSectionRepository.getNextSequenceKey())
                            .oetmsky(t.getOetmsky())
                            .tshitst(t.getTshitst())
                            .ptnrkey(t.getPtnrkey())
                            .destkey(t.getLoadkey())
                            .denamlc(t.getPknamlc())
                            .vhctncd(vhctnDef)
                            .loadrat(loadCap)
                            .isreturn(false) // for IMSI
                            .loadyplt(0)
                            .loadxplt(0)
                            .loadyunit(0)
                            .loadxunit(0)
                            .loadUnitDepth(1)
                            .loadUnitWidth(1)
                            .estsecs(0)
                            .deaddr1(t.getPkaddr1())
                            .deaddr2(t.getPkaddr2())
                            .deaddr3(t.getPkaddr3())
                            .ptaddr(t.getPkptaddr1() != null && t.getPkptaddr1().length() > 2 ? t.getPkptaddr1()
                                    : t.getPkptaddr2())
                            .ludrqtm(t.getLodrqtm())
                            .ludrqdt(t.getLodrqdt())
                            .pakwidh(t.getPakwidh())
                            .pakheig(t.getPakheig())
                            .pakdept(t.getPakdept())
                            .pkrweig(t.getPkrweig())
                            .onsidty(t.getOnsidty())
                            .opakqty(t.getOpakqty())
                            .opkcate(t.getOpkcate())
                            .opktype(t.getOpktype())
                            .ptnamlc(t.getPtnamlc())
                            .skudesc(t.getSkudesc())
                            .trctname(t.getTrctname())
                            .trctnum(t.getTrctnum())
                            .rgitqty(t.getRgitqty())
                            .ptnrtyp(t.getPuptnrty())
                            .ludptim(t.getLodptim())
                            .unavat1(ShipmentUtils.trimUnavatCode(t.getPkavat1()))
                            .unavat2(t.getPkavat2())
                            .unavat3(t.getPkavat1()) // unavat1 만 사용하니까 unavat1 내용 copy
                            .decoord(NumberUtils.getPoint6ByBigdecimal(t.getPklongi(), t.getPklatit()))
                            .vstordr(visitOrder.getAndIncrement())
                            .vsttype(VisitTypeEnum.PICKUP)
                            .build();

                    final ShipmentSection dropoffShipmentSection = ShipmentSection.builder()
                            .compkey(compkey)
                            .shpscky(shipmentSectionRepository.getNextSequenceKey())
                            .oetmsky(t.getOetmsky())
                            .tshitst(t.getTshitst())
                            .ptnrkey(t.getCustkey())
                            .destkey(t.getDestkey())
                            .denamlc(t.getDpnamlc())
                            .vhctncd(vhctnDef)
                            .isreturn(false) // for IMSI
                            .loadrat(loadCap)
                            .loadyplt(0)
                            .loadxplt(0)
                            .loadyunit(0)
                            .loadxunit(0)
                            .loadUnitDepth(1)
                            .loadUnitWidth(1)
                            .estsecs(estime)
                            .deaddr1(t.getDpaddr1())
                            .deaddr2(t.getDpaddr2())
                            .deaddr3(t.getDpaddr3())
                            .ptaddr(t.getDpptaddr1() != null && t.getDpptaddr1().length() > 2 ? t.getDpptaddr1()
                                    : t.getDpptaddr2())
                            .ludrqtm(t.getUldrqtm())
                            .ludrqdt(t.getUldrqdt())
                            .pakwidh(t.getPakwidh())
                            .pakheig(t.getPakheig())
                            .pakdept(t.getPakdept())
                            .pkrweig(t.getPkrweig())
                            .onsidty(t.getOnsidty())
                            .opakqty(t.getOpakqty())
                            .opkcate(t.getOpkcate())
                            .opktype(t.getOpktype())
                            .ptnamlc(t.getCunamlc())
                            .skudesc(t.getSkudesc())
                            .trctname(t.getTrctname())
                            .trctnum(t.getTrctnum())
                            .rgitqty(t.getRgitqty())
                            .ptnrtyp(t.getPtnrtyp())
                            .ludptim(t.getUldptim())
                            .unavat1(ShipmentUtils.trimUnavatCode(t.getDpavat1()))
                            .unavat2(t.getDpavat2())
                            .unavat3(t.getDpavat1()) // unavat1 만 사용하니까 unavat1 내용 copy
                            .decoord(NumberUtils.getPoint6ByBigdecimal(t.getDplongi(), t.getDplatit()))
                            .vstordr(visitOrder.getAndIncrement())
                            .vsttype(VisitTypeEnum.DROPOFF)
                            .build();

                    shipment.setLoadrat(loadCap);
                    shipment.addShipmentSection(pickupShipmentSection, dropoffShipmentSection);
                    Integer estimatedfee = shipmentService.calcEstimatedFee(moveDestFee, shipment);
                    shipment.setEstimatedfee(estimatedfee);

                    return shipment;
                })
                .toList();

        // Make ShipmentOmsItem
        try {
            do { // while false 로 1회 실행
                log.info("[Make FTL] Make Positions ...");
                List<ShipmentOmsItemDTO> shipItemList = new ArrayList<>();
                if (ftlShipmentList == null || ftlShipmentList.size() < 1)
                    break;
                List<ShipmentSection> scList = ftlShipmentList.get(0).getShipmentSectionList();
                if (scList == null || scList.size() < 2)
                    break;

                String shpmtky = ftlShipmentList.get(0).getShpmtky();
                for (ShipmentSection t : scList) {
                    // PICKUP,DROPOFF 한개만 DB에 기록
                    if (VisitTypeEnum.PICKUP.equals(t.getVsttype()))
                        continue;
                    List<Position> posList = this.getPositionsOfTruckResult(null, t.getOetmsky());
                    for (Position pos : posList) {
                        ShipmentOmsItemAddDTO addDto = new ShipmentOmsItemAddDTO();
                        addDto.setCompkey(compkey);
                        addDto.setShpmtky(shpmtky);
                        addDto.setOetmsky(t.getOetmsky());
                        log.info("[Make FTL] MakeList Positions : {}/{}", shpmtky, t.getOetmsky());
                        addDto.setLoadyplt(pos.getY());
                        addDto.setLoadxplt(pos.getX());
                        addDto.setLoadyunit(pos.getUnitY());
                        addDto.setLoadxunit(pos.getUnitX());
                        addDto.setLoadUnitDepth(pos.getUnitDepth());
                        addDto.setLoadUnitWidth(pos.getUnitWidth());
                        addDto.setLevel(Optional.ofNullable(pos.getLevel()).orElse(0)); // 기본값 0으로 안전 처리

                        ShipmentOmsItemDTO item = ShipmentOmsItemCustomDTO.newShipmentOmsItemDTO(addDto);
                        shipItemList.add(item);
                    }
                }
                List<List<ShipmentOmsItemDTO>> prevItemList = response.getShipItemLists();
                if (prevItemList == null) {
                    prevItemList = new ArrayList<>();
                }
                prevItemList.add(shipItemList);
                response.setShipItemLists(prevItemList);
            } while (false);
        } catch (Exception ex) {
        }

        return ftlShipmentList;
    }

    private List<Shipment> makeLtlShipmentList(@NotBlank final String compkey,
            final List<ShipmentTargetDAO> targetList,
            final List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaDriverList,
            final List<Truck> custTrucks,
            final TruckResult truckInfo,
            final Integer moveDestFee,
            ShipmentPlanMakeDummyDTO response) {

        // 2025.01.20
        // CUSTKEY 가 기자재,사외제작소 등 여러개가 한개의 Truck 으로 묶일수 있기 때문에
        // targetList 를 Grouping, Sorting 하지 않음

        // customer Key
        // final String custkey =
        // truckInfo.getResult().getLoadedItems().get(0).getShipyardGroupKey();
        // log.info("[LoadOpt] , Truck ({}) Item size: {}", custkey,
        // truckInfo.getResult().getLoadedItems().size());

        AtomicInteger truckCap = new AtomicInteger(64);
        Integer truckVolumeUtilization = 0;
        VehicleTypeEnum vehicleType = VehicleTypeEnum.카고;
        try {
            // truckVolumeUtilization = truckInfo.getResult().getVolumeUtilization();
            // truckCap.set(this.calcDriverTruckCapacity(areaList, truckInfo));
            if (truckInfo.getTruckWeightStr() != null) {
                ShipmentVehicleTonEnum tTen = ShipmentVehicleTonEnum
                        .fromVehicleStringTon(truckInfo.getTruckWeightStr());
                if (tTen != null)
                    truckCap.set(tTen.get550palCount());
                log.info("[LoadOpt] truckCap : {} / {}", truckCap, tTen);
            }

            // LoadOptimizer 에서 제공된 용적률
            truckVolumeUtilization = truckInfo.getResult().getVolumeUtilization();
            log.info("[LoadOpt] truckVolumeUtilization : {} ", truckVolumeUtilization);
            if (truckVolumeUtilization == null)
                truckVolumeUtilization = 68;
            vehicleType = this.getDriverVehicleType(areaDriverList, truckInfo);
            log.info("[LoadOpt] vehicleType : {} ", vehicleType);
        } catch (Exception ex) {
        }

        // item id 리스트 생성. OETMSKY
        List<String> idList = new ArrayList<>();
        for (LoadedItem item : truckInfo.getResult().getLoadedItems()) {
            idList.add(item.getId());
            /*
             * delete log
             * log.info("[LoadOpt] Result Item: {}/[{},{}]/VS:[{},{}]/ETA:[{},{}]"
             * , item.getId(), item.getShipyardGroupKey(), item.getGroupId()
             * , item.getPickupVisitOrder(), item.getDropoffVisitOrder(),
             * item.getPickupEta(), item.getDropoffEta()
             * );
             */
        }
        // item id Set 생성
        // Set<String> idSet = new HashSet<>(idList); // idList를 Set으로 변환
        // // idList를 Map으로 변환하여 인덱스 저장
        // Map<String, Integer> idIndexMap = new HashMap<>();
        // for (int i = 0; i < idList.size(); i++) {
        // idIndexMap.put(idList.get(i), i);
        // }

        // matching : All
        // final List<ShipmentTargetDAO> oetmskyTargetList = targetList.stream()
        // .filter(dao -> TruckLoadTypeEnum.LTL.equals(dao.getCargoty()))
        // .filter(dao -> dao.getCustkey().equals(custkey))
        // .filter(dao -> idSet.contains(dao.getOetmsky())) // Set으로 검색
        // .toList();

        // sorting
        // final List<ShipmentTargetDAO> sortedTargetList = oetmskyTargetList.stream()
        // .sorted(Comparator.comparingInt(dao ->
        // idIndexMap.getOrDefault(dao.getOetmsky(), Integer.MAX_VALUE)))
        // .toList();
        final List<ShipmentTargetDAO> sortedTargetList = targetList.stream()
                .filter(t -> idList.contains(t.getOetmsky()))
                .toList();

        // 첫번째 한개만 추출
        // final ShipmentTargetDAO first = sortedTargetList.stream()
        // .findFirst()
        // .orElse(null); // or provide a default value if necessary

        // 전체 targetList 에서 해당 Truck 에 포함된 운송주문건 정보 1개를 추출. Shipment 기본정보에 사용
        // 우선순위 (1) 조선소가 포함된 운송주문
        // 기타 파트너 타입
        Boolean foundCustomerPtnrType = false;
        ShipmentTargetDAO oneDest = null;
        for (LoadedItem item : truckInfo.getResult().getLoadedItems()) {
            if (foundCustomerPtnrType)
                break;
            for (ShipmentTargetDAO t : targetList) {
                if (foundCustomerPtnrType)
                    break;
                if (!item.getId().equals(t.getOetmsky()))
                    continue;
                oneDest = t;
                if (PartnerTypeEnum.CUSTOMER.equals(t.getPtnrtyp())) {
                    log.info("[LoadOpt] Result Truck Found CUSTOMER: {} / {}", t.getPtnrtyp(), t.getOetmsky());
                    // 조선소가 포함되어 있으면 break
                    foundCustomerPtnrType = true;
                    break;
                }
            }
        }
        String shmtCustkey = ShipmentConstant.SHIPMENT_DEFAULT_OWNER_CUSTKEY;
        String shmtCunamlc = ShipmentConstant.SHIPMENT_DEFAULT_OWNER_CUNAMLC;
        PartnerTypeEnum shmtPtnrtyp = PartnerTypeEnum.OWNER;
        if (oneDest != null) {
            log.info("[LoadOpt] Result Truck GroupID: {}/{}/{}/{}", truckInfo.getGroupId(), oneDest.getPtnrtyp(),
                    oneDest.getCustkey(), oneDest.getCunamlc());
            if (PartnerTypeEnum.CUSTOMER.equals(oneDest.getPtnrtyp())) {
                shmtPtnrtyp = oneDest.getPtnrtyp();
                shmtCustkey = oneDest.getCustkey();
                shmtCunamlc = oneDest.getCunamlc();
            } else {
                shmtPtnrtyp = PartnerTypeEnum.OWNER;
                shmtCustkey = ShipmentConstant.SHIPMENT_DEFAULT_OWNER_CUSTKEY;
                shmtCunamlc = ShipmentConstant.SHIPMENT_DEFAULT_OWNER_CUNAMLC;
            }
        } else {
            log.info("[LoadOpt] Result Truck NULL");
        }

        log.info("[LoadOpt] Result Truck area: {} / item area : {}", truckInfo.getGroupId(), truckInfo.getResult()
                .getLoadedItems().stream().map(LoadedItem::getGroupId).collect(Collectors.joining(",")));
        ShipmentVehicleTonEnum getVhctnDef = ShipmentVehicleTonEnum.T11; // for LTL "11톤

        // for DEBUG
        /*
         * delete log
         * for ( LoadedItem item : truckInfo.getResult().getLoadedItems() ) {
         * log.info("[LoadOpt] Result Truck item: {} Qty : {}", item.getId(),
         * item.getPalletBoxQuantity());
         * for ( Position pos : item.getPositions() ) {
         * log.info("        Pos = {},{} / {},{} / {},{}"
         * , pos.getUnitX(), pos.getUnitY(), pos.getUnitWidth(), pos.getUnitDepth(),
         * pos.getX(), pos.getY());
         * }
         * }
         * 
         * log.info("[LoadOpt] Result Truck ID: {} = W: {} / RW: {} / Ad: {}",
         * truckInfo.getTruckId(), truckInfo.getTruckWeightStr(),
         * truckInfo.getReplaceableTruckWeightStr(), truckInfo.getAddedTruck());
         */
        // ~ for DEBUG

        if (truckInfo.getTruckWeightStr() != null) {
            getVhctnDef = ShipmentVehicleTonEnum.fromVehicleStringTon(truckInfo.getTruckWeightStr());
            log.info("[LoadOpt] getVhctnDef : {}", getVhctnDef);
        }
        final ShipmentVehicleTonEnum vhctnDef = getVhctnDef;

        // 대체가능차량톤수
        ShipmentVehicleTonEnum repVhctn = null;
        if (truckInfo.getReplaceableTruckWeightStr() != null) {
            repVhctn = ShipmentVehicleTonEnum.fromVehicleStringTon(truckInfo.getReplaceableTruckWeightStr());
            log.info("[LoadOpt] repVhctn : {}", repVhctn);
        }
        if (truckInfo.getAddedTruck() == false) {
            // addedTruck == false 이면 null
            // repVhctn = null; // 대체가능차량을 addedTruck 값으로 가부 처리하지 않음 (250103)
            log.info("[LoadOpt] getAddedTruck : {}", truckInfo.getAddedTruck());
        }
        if (vhctnDef != null && repVhctn != null && (vhctnDef != repVhctn)) {
            // 응답차량과 대체차량이 다르면 저장 (250103)
            log.info("[LoadOpt] vhctnDef/repVhctn : {} / {}", vhctnDef, repVhctn);
        } else {
            repVhctn = null;
            log.info("[LoadOpt] repVhctn Set Null");
        }

        final AtomicLong shmtLoadRat = new AtomicLong(0);
        Long autoDrvId = null;
        Long autoVhcId = null;
        try {
            if (!truckInfo.getAddedTruck()) {
                autoVhcId = Long.valueOf(truckInfo.getTruckId()); // TruckId = VehicleId 임
                autoDrvId = this.getAreaDriverIdByVehicleId(areaDriverList, autoVhcId);
                log.info("[LoadOpt] autoVhcId/autoDrvId : {}/{}", autoVhcId, autoDrvId);
                if (autoDrvId == null) {
                    autoVhcId = null;
                }
            }
        } catch (NumberFormatException e) {
            System.out.println("유효하지 않은 숫자 형식입니다.");
        }

        final String shpmtky = shipmentRepository.getNextSequenceKey();
        final Shipment ltlShipment = Shipment.builder()
                .compkey(compkey)
                .shpmtky(shpmtky)
                .vhctncd(vhctnDef)
                .vehicleType(vehicleType)
                .cargoty(TruckLoadTypeEnum.LTL)
                .repVhctn(repVhctn)
                .autoDrvId(autoDrvId)
                .autoVhcId(autoVhcId)
                .ptnrkey(shmtCustkey)
                .ptnamlc(shmtCunamlc)
                .ptnrtyp(shmtPtnrtyp)
                .simulat(LocalDateTime.now())
                .build();

        final AtomicInteger visitAOrder = new AtomicInteger(1);

        List<ShipmentSection> ltlShipmentSectionList = sortedTargetList.stream()
                .map(t -> {

                    Position pos = this.getPositionOfTruckResult(truckInfo, t.getOetmsky());
                    List<Integer> visitOrderSet = this.getLoadingVisitOrder(truckInfo, t.getOetmsky());
                    List<Integer> estime = this.getLoadOptimizerEstime(truckInfo, t.getOetmsky());
                    // log.info("[LoadOpt] estime : {}/{}", estime, t.getOetmsky());
                    // Integer loadCap = this.calcLoadingCapacity(t);
                    Integer loadCap = pos.getUnitDepth() * pos.getUnitWidth();
                    shmtLoadRat.addAndGet(loadCap);
                    log.info("[LoadOpt] vsord loadCap estime: {}/{}/{}/{}", t.getOetmsky(), visitOrderSet, loadCap,
                            estime);

                    final String scky1 = shipmentSectionRepository.getNextSequenceKey();
                    final ShipmentSection pickupShipmentSection = ShipmentSection.builder()
                            .compkey(compkey)
                            .shpscky(scky1)
                            .oetmsky(t.getOetmsky())
                            .tshitst(t.getTshitst())
                            .ptnrkey(t.getPtnrkey())
                            .destkey(t.getLoadkey())
                            .denamlc(t.getPknamlc())
                            .vhctncd(vhctnDef)
                            .loadrat(NumberUtils.getPercentByLong(loadCap, truckCap.get()))
                            .isreturn(false) // for IMSI
                            .loadyplt(pos.getY())
                            .loadxplt(pos.getX())
                            .loadyunit(pos.getUnitY())
                            .loadxunit(pos.getUnitX())
                            .loadUnitDepth(pos.getUnitDepth())
                            .loadUnitWidth(pos.getUnitWidth())
                            .estsecs(estime.get(0))
                            .deaddr1(t.getPkaddr1())
                            .deaddr2(t.getPkaddr2())
                            .deaddr3(t.getPkaddr3())
                            .ptaddr(t.getPkptaddr1() != null && t.getPkptaddr1().length() > 2 ? t.getPkptaddr1()
                                    : t.getPkptaddr2())
                            .ludrqtm(t.getLodrqtm())
                            .ludrqdt(t.getLodrqdt())
                            .pakwidh(t.getPakwidh())
                            .pakheig(t.getPakheig())
                            .pakdept(t.getPakdept())
                            .pkrweig(t.getPkrweig())
                            .onsidty(t.getOnsidty())
                            .opakqty(t.getOpakqty())
                            .opkcate(t.getOpkcate())
                            .opktype(t.getOpktype())
                            .ptnamlc(t.getPtnamlc())
                            .skudesc(t.getSkudesc())
                            .trctname(t.getTrctname())
                            .trctnum(t.getTrctnum())
                            .rgitqty(t.getRgitqty())
                            .ptnrtyp(t.getPuptnrty())
                            .ludptim(t.getLodptim())
                            .unavat1(ShipmentUtils.trimUnavatCode(t.getPkavat1()))
                            .unavat2(t.getPkavat2())
                            .unavat3(t.getPkavat1()) // unavat1 만 사용하니까 unavat1 내용 copy
                            .decoord(NumberUtils.getPoint6ByBigdecimal(t.getPklongi(), t.getPklatit()))
                            // .vstordr(visitAOrder.getAndIncrement())
                            .vstordr(visitOrderSet.get(0))
                            .vsttype(VisitTypeEnum.PICKUP)
                            .build();

                    final String scky2 = shipmentSectionRepository.getNextSequenceKey();
                    final ShipmentSection dropoffShipmentSection = ShipmentSection.builder()
                            .compkey(compkey)
                            .shpscky(scky2)
                            .oetmsky(t.getOetmsky())
                            .tshitst(t.getTshitst())
                            .ptnrkey(t.getCustkey())
                            .destkey(t.getDestkey())
                            .denamlc(t.getDpnamlc())
                            .vhctncd(vhctnDef)
                            .loadrat(NumberUtils.getPercentByLong(loadCap, truckCap.get()))
                            .isreturn(false) // for IMSI
                            .loadyplt(pos.getY())
                            .loadxplt(pos.getX())
                            .loadyunit(pos.getUnitY())
                            .loadxunit(pos.getUnitX())
                            .loadUnitDepth(pos.getUnitDepth())
                            .loadUnitWidth(pos.getUnitWidth())
                            .estsecs(estime.get(1))
                            .deaddr1(t.getDpaddr1())
                            .deaddr2(t.getDpaddr2())
                            .deaddr3(t.getDpaddr3())
                            .ptaddr(t.getDpptaddr1() != null && t.getDpptaddr1().length() > 2 ? t.getDpptaddr1()
                                    : t.getDpptaddr2())
                            .ludrqtm(t.getUldrqtm())
                            .ludrqdt(t.getUldrqdt())
                            .pakwidh(t.getPakwidh())
                            .pakheig(t.getPakheig())
                            .pakdept(t.getPakdept())
                            .pkrweig(t.getPkrweig())
                            .onsidty(t.getOnsidty())
                            .opakqty(t.getOpakqty())
                            .opkcate(t.getOpkcate())
                            .opktype(t.getOpktype())
                            .ptnamlc(t.getCunamlc())
                            .skudesc(t.getSkudesc())
                            .trctname(t.getTrctname())
                            .trctnum(t.getTrctnum())
                            .rgitqty(t.getRgitqty())
                            .ptnrtyp(t.getPtnrtyp())
                            .ludptim(t.getUldptim())
                            .unavat1(ShipmentUtils.trimUnavatCode(t.getDpavat1()))
                            .unavat2(t.getDpavat2())
                            .unavat3(t.getDpavat1()) // unavat1 만 사용하니까 unavat1 내용 copy
                            .decoord(NumberUtils.getPoint6ByBigdecimal(t.getDplongi(), t.getDplatit()))
                            // .vstordr(visitAOrder.getAndIncrement())
                            .vstordr(visitOrderSet.get(1))
                            .vsttype(VisitTypeEnum.DROPOFF)
                            .build();

                    return List.of(pickupShipmentSection, dropoffShipmentSection);
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());

        // Make ShipmentOmsItem
        List<ShipmentOmsItemDTO> shipItemList = new ArrayList<>();
        for (ShipmentTargetDAO t : sortedTargetList) {
            List<Position> posList = this.getPositionsOfTruckResult(truckInfo, t.getOetmsky());
            for (Position pos : posList) {
                ShipmentOmsItemAddDTO addDto = new ShipmentOmsItemAddDTO();
                addDto.setCompkey(compkey);
                addDto.setShpmtky(ltlShipment.getShpmtky());
                addDto.setOetmsky(t.getOetmsky());
                log.info("[LoadOpt] MakeList Positions : {}/{}", ltlShipment.getShpmtky(), t.getOetmsky());
                addDto.setLoadyplt(pos.getY());
                addDto.setLoadxplt(pos.getX());
                addDto.setLoadyunit(pos.getUnitY());
                addDto.setLoadxunit(pos.getUnitX());
                addDto.setLoadUnitDepth(pos.getUnitDepth());
                addDto.setLoadUnitWidth(pos.getUnitWidth());

                ShipmentOmsItemDTO item = ShipmentOmsItemCustomDTO.newShipmentOmsItemDTO(addDto);
                shipItemList.add(item);
            }
        }
        List<List<ShipmentOmsItemDTO>> prevItemList = response.getShipItemLists();
        if (prevItemList == null) {
            prevItemList = new ArrayList<>();
        }
        prevItemList.add(shipItemList);
        response.setShipItemLists(prevItemList);

        // ltlShipment.setLoadrat(NumberUtils.getPercentByLong(shmtLoadRat.intValue(),
        // truckCap.get()));
        ltlShipment.setLoadrat(truckVolumeUtilization);
        ltlShipmentSectionList = this.reTouchVisitOrder(ltlShipmentSectionList);
        ltlShipment.addShipmentSectionList(ltlShipmentSectionList);
        Integer estimatedfee = shipmentService.calcEstimatedFee(moveDestFee, ltlShipmentSectionList);
        ltlShipment.setEstimatedfee(estimatedfee);

        return List.of(ltlShipment);
    }

    private List<Shipment> makeRemainLtlShipmentList(@NotBlank final String compkey,
            final List<ShipmentTargetDAO> targetList,
            final List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaDriverList,
            final List<Truck> custTrucks,
            final List<LoadedItem> remainInfo,
            final Integer moveDestFee,
            ShipmentPlanMakeDummyDTO response) {

        // customer Key
        // final String custkey = remainInfo.get(0).getShipyardGroupKey();
        VehicleTypeEnum vehicleType = VehicleTypeEnum.카고; // default

        // item id 리스트 생성. OETMSKY
        List<String> idList = new ArrayList<>();
        for (Item item : remainInfo) {
            idList.add(item.getId());
        }
        // item id Set 생성
        // Set<String> idSet = new HashSet<>(idList); // idList를 Set으로 변환
        // // idList를 Map으로 변환하여 인덱스 저장
        // Map<String, Integer> idIndexMap = new HashMap<>();
        // for (int i = 0; i < idList.size(); i++) {
        // idIndexMap.put(idList.get(i), i);
        // }

        // matching : All
        // final List<ShipmentTargetDAO> oetmskyTargetList = targetList.stream()
        // .filter(dao -> TruckLoadTypeEnum.LTL.equals(dao.getCargoty()))
        // .filter(dao -> dao.getCustkey().equals(custkey))
        // .filter(dao -> idSet.contains(dao.getOetmsky())) // Set으로 검색
        // .toList();

        // sorting
        // final List<ShipmentTargetDAO> sortedTargetList = oetmskyTargetList.stream()
        // .sorted(Comparator.comparingInt(dao ->
        // idIndexMap.getOrDefault(dao.getOetmsky(), Integer.MAX_VALUE)))
        // .toList();

        final List<ShipmentTargetDAO> sortedTargetList = targetList.stream()
                .filter(t -> idList.contains(t.getOetmsky()))
                .toList();

        // 첫번째 한개만 추출
        // final ShipmentTargetDAO first = sortedTargetList.stream()
        // .findFirst()
        // .orElse(null); // or provide a default value if necessary

        // 전체 targetList 에서 해당 Truck 에 포함된 운송주문건 정보 1개를 추출. Shipment 기본정보에 사용
        // 우선순위 (1) 조선소가 포함된 운송주문
        // 기타 파트너 타입
        Boolean foundCustomerPtnrType = false;
        ShipmentTargetDAO oneDest = null;
        for (LoadedItem item : remainInfo) {
            if (foundCustomerPtnrType)
                break;
            for (ShipmentTargetDAO t : targetList) {
                if (foundCustomerPtnrType)
                    break;
                if (!item.getId().equals(t.getOetmsky()))
                    continue;
                oneDest = t;
                if (PartnerTypeEnum.CUSTOMER.equals(t.getPtnrtyp())) {
                    // 조선소가 포함되어 있으면 break
                    foundCustomerPtnrType = true;
                    break;
                }
            }
        }
        String shmtCustkey = ShipmentConstant.SHIPMENT_DEFAULT_OWNER_CUSTKEY;
        String shmtCunamlc = ShipmentConstant.SHIPMENT_DEFAULT_OWNER_CUNAMLC;
        PartnerTypeEnum shmtPtnrtyp = PartnerTypeEnum.OWNER;
        if (oneDest != null) {
            if (PartnerTypeEnum.CUSTOMER.equals(oneDest.getPtnrtyp())) {
                shmtPtnrtyp = oneDest.getPtnrtyp();
                shmtCustkey = oneDest.getCustkey();
                shmtCunamlc = oneDest.getCunamlc();
            } else {
                shmtPtnrtyp = PartnerTypeEnum.OWNER;
                shmtCustkey = ShipmentConstant.SHIPMENT_DEFAULT_OWNER_CUSTKEY;
                shmtCunamlc = ShipmentConstant.SHIPMENT_DEFAULT_OWNER_CUNAMLC;
            }
        }

        // 첫번째 한개만 추출
        // final ShipmentTargetDAO first = sortedTargetList.stream()
        // .findFirst()
        // .orElse(null); // or provide a default value if necessary

        final ShipmentVehicleTonEnum vhctnDef = ShipmentVehicleTonEnum.T11; // for LTL "11톤
        // 대체가능차량톤수
        ShipmentVehicleTonEnum repVhctn = null;
        Integer loadrat = 35;

        final String shpmtky = shipmentRepository.getNextSequenceKey();
        final Shipment ltlShipment = Shipment.builder()
                .compkey(compkey)
                .shpmtky(shpmtky)
                .vhctncd(vhctnDef)
                .vehicleType(vehicleType)
                .cargoty(TruckLoadTypeEnum.LTL)
                .repVhctn(repVhctn)
                .ptnrkey(shmtCustkey)
                .ptnamlc(shmtCunamlc)
                .ptnrtyp(shmtPtnrtyp)
                .build();

        final AtomicInteger visitAOrder = new AtomicInteger(1);

        final List<ShipmentSection> ltlShipmentSectionList = sortedTargetList.stream()
                .map(t -> {

                    Position pos = this.getPositionOfItemResult(remainInfo, t.getOetmsky());
                    List<Integer> estime = this.getLoadOptimizerEstime(remainInfo, t.getOetmsky());
                    List<Integer> visitOrderSet = this.getLoadingVisitOrder(remainInfo, t.getOetmsky());

                    final String scky1 = shipmentSectionRepository.getNextSequenceKey();
                    final ShipmentSection pickupShipmentSection = ShipmentSection.builder()
                            .compkey(compkey)
                            .shpscky(scky1)
                            .oetmsky(t.getOetmsky())
                            .tshitst(t.getTshitst())
                            .ptnrkey(t.getPtnrkey())
                            .destkey(t.getLoadkey())
                            .denamlc(t.getPknamlc())
                            .vhctncd(vhctnDef)
                            .loadrat(34) // for IMSI
                            .isreturn(false) // for IMSI
                            .loadyplt(pos.getY())
                            .loadxplt(pos.getX())
                            .loadyunit(pos.getUnitY())
                            .loadxunit(pos.getUnitX())
                            .loadUnitDepth(pos.getUnitDepth())
                            .loadUnitWidth(pos.getUnitWidth())
                            .estsecs(estime.get(0))
                            .deaddr1(t.getPkaddr1())
                            .deaddr2(t.getPkaddr2())
                            .deaddr3(t.getPkaddr3())
                            .ptaddr(t.getPkptaddr1() != null && t.getPkptaddr1().length() > 2 ? t.getPkptaddr1()
                                    : t.getPkptaddr2())
                            .ludrqtm(t.getLodrqtm())
                            .ludrqdt(t.getLodrqdt())
                            .pakwidh(t.getPakwidh())
                            .pakheig(t.getPakheig())
                            .pakdept(t.getPakdept())
                            .pkrweig(t.getPkrweig())
                            .onsidty(t.getOnsidty())
                            .opakqty(t.getOpakqty())
                            .opkcate(t.getOpkcate())
                            .opktype(t.getOpktype())
                            .ptnamlc(t.getPtnamlc())
                            .skudesc(t.getSkudesc())
                            .trctname(t.getTrctname())
                            .trctnum(t.getTrctnum())
                            .rgitqty(t.getRgitqty())
                            .ptnrtyp(t.getPuptnrty())
                            .ludptim(t.getLodptim())
                            .unavat1(ShipmentUtils.trimUnavatCode(t.getPkavat1()))
                            .unavat2(t.getPkavat2())
                            .unavat3(t.getPkavat1()) // unavat1 만 사용하니까 unavat1 내용 copy
                            .decoord(NumberUtils.getPoint6ByBigdecimal(t.getPklongi(), t.getPklatit()))
                            // .vstordr(visitAOrder.getAndIncrement())
                            .vstordr(visitOrderSet.get(0))
                            .vsttype(VisitTypeEnum.PICKUP)
                            .build();

                    final String scky2 = shipmentSectionRepository.getNextSequenceKey();
                    final ShipmentSection dropoffShipmentSection = ShipmentSection.builder()
                            .compkey(compkey)
                            .shpscky(scky2)
                            .oetmsky(t.getOetmsky())
                            .tshitst(t.getTshitst())
                            .ptnrkey(t.getCustkey())
                            .destkey(t.getDestkey())
                            .denamlc(t.getDpnamlc())
                            .vhctncd(vhctnDef)
                            .loadrat(34) // for IMSI
                            .isreturn(false) // for IMSI
                            .loadyplt(pos.getY())
                            .loadxplt(pos.getX())
                            .loadyunit(pos.getUnitY())
                            .loadxunit(pos.getUnitX())
                            .loadUnitDepth(pos.getUnitDepth())
                            .loadUnitWidth(pos.getUnitWidth())
                            .estsecs(estime.get(1))
                            .deaddr1(t.getDpaddr1())
                            .deaddr2(t.getDpaddr2())
                            .deaddr3(t.getDpaddr3())
                            .ptaddr(t.getDpptaddr1() != null && t.getDpptaddr1().length() > 2 ? t.getDpptaddr1()
                                    : t.getDpptaddr2())
                            .ludrqtm(t.getUldrqtm())
                            .ludrqdt(t.getUldrqdt())
                            .pakwidh(t.getPakwidh())
                            .pakheig(t.getPakheig())
                            .pakdept(t.getPakdept())
                            .pkrweig(t.getPkrweig())
                            .onsidty(t.getOnsidty())
                            .opakqty(t.getOpakqty())
                            .opkcate(t.getOpkcate())
                            .opktype(t.getOpktype())
                            .ptnamlc(t.getCunamlc())
                            .skudesc(t.getSkudesc())
                            .trctname(t.getTrctname())
                            .trctnum(t.getTrctnum())
                            .rgitqty(t.getRgitqty())
                            .ptnrtyp(t.getPtnrtyp())
                            .ludptim(t.getUldptim())
                            .unavat1(ShipmentUtils.trimUnavatCode(t.getDpavat1()))
                            .unavat2(t.getDpavat2())
                            .unavat3(t.getDpavat1()) // unavat1 만 사용하니까 unavat1 내용 copy
                            .decoord(NumberUtils.getPoint6ByBigdecimal(t.getDplongi(), t.getDplatit()))
                            // .vstordr(visitAOrder.getAndIncrement())
                            .vstordr(visitOrderSet.get(1))
                            .vsttype(VisitTypeEnum.DROPOFF)
                            .build();

                    return List.of(pickupShipmentSection, dropoffShipmentSection);
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());

        ltlShipment.setLoadrat(loadrat);
        ltlShipment.addShipmentSectionList(ltlShipmentSectionList);
        Integer estimatedfee = shipmentService.calcEstimatedFee(moveDestFee, ltlShipmentSectionList);
        ltlShipment.setEstimatedfee(estimatedfee);

        return List.of(ltlShipment);
    }

    private List<ShipmentSection> makeNewShipmentSectionList(final String compkey, final String oetmsky,
            final List<ShipmentTargetDAO> targetList) {

        List<ShipmentSection> resultList = new ArrayList<>();
        if (targetList == null) {
            log.info("Make NewShipmentSectionList, targetList is NULL");
            return resultList;
        }
        if (targetList.size() < 1) {
            log.info("Make NewShipmentSectionList, targetList size: {}", targetList.size());
            return resultList;
        }

        AtomicInteger truckCap = new AtomicInteger(64);
        VehicleTypeEnum vehicleType = VehicleTypeEnum.카고;
        final ShipmentVehicleTonEnum vhctnDef = ShipmentVehicleTonEnum.T11; // for LTL "11톤
        // 대체가능차량톤수
        // addedTruck 이면 null
        ShipmentVehicleTonEnum repVhctn = null;

        final AtomicLong shmtLoadRat = new AtomicLong(0);
        Long autoDrvId = null;
        Long autoVhcId = null;

        final AtomicInteger visitOrder = new AtomicInteger(1);

        try {
            final List<ShipmentSection> shipmentSectionList = targetList.stream()
                    .map(t -> {

                        Integer estime = 120;
                        Integer unitDepth = (t.getPakdept() / 550);
                        if ((t.getPakdept() % 550) > 0)
                            unitDepth += 1;
                        Integer unitWidth = (t.getPakwidh() / 550);
                        if ((t.getPakwidh() % 550) > 0)
                            unitWidth += 1;
                        Integer loadCap = unitDepth * unitWidth;
                        shmtLoadRat.addAndGet(loadCap);

                        final String shscky1 = shipmentSectionRepository.getNextSequenceKey();
                        final ShipmentSection pickupShipmentSection = ShipmentSection.builder()
                                .compkey(compkey)
                                .shpscky(shscky1)
                                .oetmsky(t.getOetmsky())
                                .tshitst(t.getTshitst())
                                .ptnrkey(t.getPtnrkey())
                                .destkey(t.getLoadkey())
                                .denamlc(t.getPknamlc())
                                .vhctncd(vhctnDef)
                                .loadrat(NumberUtils.getPercentByLong(loadCap, truckCap.get()))
                                .isreturn(false) // for IMSI
                                .loadyplt(0)
                                .loadxplt(0)
                                .loadyunit(0)
                                .loadxunit(0)
                                .loadUnitDepth(unitDepth)
                                .loadUnitWidth(unitWidth)
                                .estsecs(estime)
                                .deaddr1(t.getPkaddr1())
                                .deaddr2(t.getPkaddr2())
                                .deaddr3(t.getPkaddr3())
                                .ptaddr(t.getPkptaddr1() != null && t.getPkptaddr1().length() > 2 ? t.getPkptaddr1()
                                        : t.getPkptaddr2())
                                .ludrqtm(t.getLodrqtm())
                                .ludrqdt(t.getLodrqdt())
                                .pakwidh(t.getPakwidh())
                                .pakheig(t.getPakheig())
                                .pakdept(t.getPakdept())
                                .pkrweig(t.getPkrweig())
                                .onsidty(t.getOnsidty())
                                .opakqty(t.getOpakqty())
                                .opkcate(t.getOpkcate())
                                .opktype(t.getOpktype())
                                .ptnamlc(t.getPtnamlc())
                                .skudesc(t.getSkudesc())
                                .trctname(t.getTrctname())
                                .trctnum(t.getTrctnum())
                                .rgitqty(t.getRgitqty())
                                .ptnrtyp(t.getPuptnrty())
                                .ludptim(t.getLodptim())
                                .unavat1(ShipmentUtils.trimUnavatCode(t.getPkavat1()))
                                .unavat2(t.getPkavat2())
                                .unavat3(t.getPkavat1()) // unavat1 만 사용하니까 unavat1 내용 copy
                                .decoord(NumberUtils.getPoint6ByBigdecimal(t.getPklongi(), t.getPklatit()))
                                .vstordr(visitOrder.getAndIncrement())
                                .vsttype(VisitTypeEnum.PICKUP)
                                .build();

                        final String shscky2 = shipmentSectionRepository.getNextSequenceKey();
                        final ShipmentSection dropoffShipmentSection = ShipmentSection.builder()
                                .compkey(compkey)
                                .shpscky(shscky2)
                                .oetmsky(t.getOetmsky())
                                .tshitst(t.getTshitst())
                                .ptnrkey(t.getCustkey())
                                .destkey(t.getDestkey())
                                .denamlc(t.getDpnamlc())
                                .vhctncd(vhctnDef)
                                .loadrat(NumberUtils.getPercentByLong(loadCap, truckCap.get()))
                                .isreturn(false) // for IMSI
                                .loadyplt(0)
                                .loadxplt(0)
                                .loadyunit(0)
                                .loadxunit(0)
                                .loadUnitDepth(unitDepth)
                                .loadUnitWidth(unitWidth)
                                .estsecs(estime)
                                .deaddr1(t.getDpaddr1())
                                .deaddr2(t.getDpaddr2())
                                .deaddr3(t.getDpaddr3())
                                .ptaddr(t.getDpptaddr1() != null && t.getDpptaddr1().length() > 2 ? t.getDpptaddr1()
                                        : t.getDpptaddr2())
                                .ludrqtm(t.getUldrqtm())
                                .ludrqdt(t.getUldrqdt())
                                .pakwidh(t.getPakwidh())
                                .pakheig(t.getPakheig())
                                .pakdept(t.getPakdept())
                                .pkrweig(t.getPkrweig())
                                .onsidty(t.getOnsidty())
                                .opakqty(t.getOpakqty())
                                .opkcate(t.getOpkcate())
                                .opktype(t.getOpktype())
                                .ptnamlc(t.getCunamlc())
                                .skudesc(t.getSkudesc())
                                .trctname(t.getTrctname())
                                .trctnum(t.getTrctnum())
                                .rgitqty(t.getRgitqty())
                                .ptnrtyp(t.getPtnrtyp())
                                .ludptim(t.getUldptim())
                                .unavat1(ShipmentUtils.trimUnavatCode(t.getDpavat1()))
                                .unavat2(t.getDpavat2())
                                .unavat3(t.getDpavat1()) // unavat1 만 사용하니까 unavat1 내용 copy
                                .decoord(NumberUtils.getPoint6ByBigdecimal(t.getDplongi(), t.getDplatit()))
                                .vstordr(visitOrder.getAndIncrement())
                                .vsttype(VisitTypeEnum.DROPOFF)
                                .build();

                        return List.of(pickupShipmentSection, dropoffShipmentSection);
                    })
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            return shipmentSectionList;
        } catch (Exception ex) {
        }

        return resultList;
    }

    private List<Integer> getLoadingVisitOrder(final TruckResult truckInfo,
            final String oetmsky) {
        Integer visitOrderPickup = 1;
        Integer visitOrderDropoff = 2;
        try {
            visitOrderPickup = truckInfo.getResult().getLoadedItems().stream()
                    .filter(item -> item.getId().equals(oetmsky))
                    .map(LoadedItem::getPickupVisitOrder)
                    .findFirst()
                    .orElse(1);

            visitOrderDropoff = truckInfo.getResult().getLoadedItems().stream()
                    .filter(item -> item.getId().equals(oetmsky))
                    .map(LoadedItem::getDropoffVisitOrder)
                    .findFirst()
                    .orElse(2);

            log.info("oetmsky = {} / VisitOrder = {},{}", oetmsky, visitOrderPickup, visitOrderDropoff);
        } catch (Exception ex) {
        }

        if (visitOrderPickup == 0)
            visitOrderPickup = 1;
        if (visitOrderDropoff == 0 || visitOrderDropoff == 1)
            visitOrderDropoff = 2;

        return Arrays.asList(visitOrderPickup, visitOrderDropoff);
    }

    private List<Integer> getLoadingVisitOrder(final List<LoadedItem> remainInfo,
            final String oetmsky) {
        Integer visitOrderPickup = 1;
        Integer visitOrderDropoff = 2;
        try {
            for (LoadedItem item : remainInfo) {
                if (item.getId().equals(oetmsky)) {
                    if (item.getPickupVisitOrder() == 0)
                        visitOrderPickup = 1;
                    else
                        visitOrderPickup = item.getPickupVisitOrder();

                    if (item.getPickupVisitOrder() == 0)
                        visitOrderDropoff = 2;
                    else
                        visitOrderDropoff = item.getDropoffVisitOrder();

                    log.info("oetmsky = {} / VisitOrder = {},{}", oetmsky, visitOrderPickup, visitOrderDropoff);
                    return Arrays.asList(visitOrderPickup, visitOrderDropoff);
                }
            }
        } catch (Exception ex) {
        }

        if (visitOrderPickup == 0)
            visitOrderPickup = 1;
        if (visitOrderDropoff == 0 || visitOrderDropoff == 1)
            visitOrderDropoff = 2;

        return Arrays.asList(visitOrderPickup, visitOrderDropoff);
    }

    private List<Integer> getLoadOptimizerEstime(final TruckResult truckInfo,
            final String oetmsky) {
        Integer pickupEta = 0;
        Integer dropoffEta = 0;
        try {
            pickupEta = truckInfo.getResult().getLoadedItems().stream()
                    .filter(item -> item.getId().equals(oetmsky))
                    .map(LoadedItem::getPickupEta)
                    .findFirst()
                    .orElse(0);

            dropoffEta = truckInfo.getResult().getLoadedItems().stream()
                    .filter(item -> item.getId().equals(oetmsky))
                    .map(LoadedItem::getDropoffEta)
                    .findFirst()
                    .orElse(0);
        } catch (Exception ex) {
        }
        return Arrays.asList(pickupEta, dropoffEta);
    }

    private List<Integer> getLoadOptimizerEstime(final List<LoadedItem> remainInfo,
            final String oetmsky) {
        try {
            for (LoadedItem item : remainInfo) {
                if (item.getId().equals(oetmsky)) {
                    return Arrays.asList(item.getPickupEta(), item.getDropoffEta());
                }
            }
        } catch (Exception ex) {
        }
        return Arrays.asList(0, 0);
    }

    private List<Position> getPositionsOfTruckResult(final TruckResult truckInfo,
            final String oetmsky) {
        try {
            if (truckInfo == null) {
                return List.of(Position.builder().x(0).y(0).unitX(0).unitY(0).unitWidth(0).unitDepth(0).build());
            }
            // get Position
            return truckInfo.getResult().getLoadedItems().stream()
                    .filter(item -> item.getId().equals(oetmsky))
                    .map(LoadedItem::getPositions)
                    .findFirst()
                    .orElse(List.of(Position.builder().x(0).y(0).unitX(0).unitY(0).unitWidth(0).unitDepth(0).build()));
        } catch (Exception ex) {
        }
        return List.of(Position.builder().x(0).y(0).unitX(0).unitY(0).unitWidth(0).unitDepth(0).build());
    }

    private Position getPositionOfTruckResult(final TruckResult truckInfo,
            final String oetmsky) {
        try {
            // get Position
            return truckInfo.getResult().getLoadedItems().stream()
                    .filter(item -> item.getId().equals(oetmsky))
                    .map(LoadedItem::getPosition)
                    .findFirst()
                    .orElse(Position.builder().x(0).y(0).unitX(0).unitY(0).build());
        } catch (Exception ex) {
        }

        return Position.builder().x(0).y(0).unitX(0).unitY(0).build();
    }

    private Position getPositionOfItemResult(final List<LoadedItem> remainInfo,
            final String oetmsky) {
        try {
            // get Position
            for (LoadedItem item : remainInfo) {
                if (item.getId().equals(oetmsky)) {
                    return item.getPosition();
                }
            }
        } catch (Exception ex) {
        }

        return Position.builder().x(0).y(0).unitX(0).unitY(0).build();
    }

    private Integer calcLoadingCapacity(final ShipmentTargetDAO sc) {
        Integer loadCap = 1;

        try {
            if (NumberUtils.isValidNonZero(sc.getPakwidh()) && NumberUtils.isValidNonZero(sc.getPakdept())) {
                Integer capW = sc.getPakwidh() / 550;
                Integer capD = sc.getPakdept() / 550;

                if ((sc.getPakwidh() % 550) > 0)
                    capW += 1;
                if ((sc.getPakdept() % 550) > 0)
                    capD += 1;

                loadCap = capW * capD;
                // log.info("Loading Capacity,ID:{}/W:{}/L:{}/CA:{}",
                // sc.getOetmit(), sc.getPakwidh(), sc.getPakdept(), loadCap);
            }
        } catch (Exception ex) {
        }

        return loadCap;
    }

    private Integer calcTruckCapacity(final List<Truck> custTrucks, final TruckResult truckInfo) {
        int loadCap = 1;
        try {
            for (Truck sc : custTrucks) {
                if (sc.getId().equals(truckInfo.getTruckId())) {
                    if (NumberUtils.isValidNonZero(sc.getWidth()) && NumberUtils.isValidNonZero(sc.getDepth())) {
                        loadCap = (sc.getWidth() / 550) * (sc.getDepth() / 550);
                    }
                    break;
                }
            }
        } catch (Exception ex) {
        }

        return loadCap;
    }

    private Long getAreaDriverIdByVehicleId(
            final List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaList,
            final Long autoVhcId) {
        try {
            {
                for (PickUpDropOffAreaDTO.AreaShipmentResponse one : areaList) {
                    if (one.getVehicleId() == autoVhcId) {
                        return one.getUserasq();
                    }
                }
            }
        } catch (Exception ex) {
        }
        return null;
    }

    private VehicleTypeEnum getDriverVehicleType(
            final List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaList,
            final TruckResult truckInfo) {

        try {
            {
                for (PickUpDropOffAreaDTO.AreaShipmentResponse one : areaList) {
                    if (one.getVehicleId().toString().equals(truckInfo.getTruckId())) {
                        return one.getVehicleType();
                    }
                }
            }
        } catch (Exception ex) {
        }
        return VehicleTypeEnum.카고;
    }

    private Integer calcDriverTruckCapacity(
            final List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaList,
            final TruckResult truckInfo) {
        int loadCap = 1;
        Boolean isFound = false;
        int addedTruckLoadCap = 40; // 추가된 경우 40 unit으로 산정
        try {
            if (truckInfo.getAddedTruck()) {
                return addedTruckLoadCap;
            }

            {
                for (PickUpDropOffAreaDTO.AreaShipmentResponse one : areaList) {
                    if (isFound)
                        break;
                    if (one.getVehicleId().toString().equals(truckInfo.getTruckId())) {
                        if (NumberUtils.isValidNonZero(one.getVehicleWidth()) &&
                                NumberUtils.isValidNonZero(one.getVehicleLength())) {
                            loadCap = (one.getVehicleWidth() / 550) * (one.getVehicleLength() / 550);
                        }
                        log.info("DriverTruck Capacity,ID:{}/W:{}/L:{}/CA:{}/OC:{}",
                                truckInfo.getTruckId(), one.getVehicleWidth(), one.getVehicleLength(),
                                loadCap, one.getPalletCount().intValue());

                        isFound = true;
                        break;
                    }
                }
            }
            if (!isFound) {
                loadCap = addedTruckLoadCap;
            }
        } catch (Exception ex) {
        }

        return loadCap;
    }

    private Integer calcLoadPaletteCapacity(final ShipmentTargetDAO sc, LoadOptimizerTruckCapacityDTO truckCap) {
        try {
            if (sc.getOpktype().equals(PackageTypeEnum.BULK)) {
                return 60;
            }
        } catch (Exception ex) {
        }
        return 50;
    }

    private LoadOptimizerTruckCapacityDTO calcDefaultTruck5TCapacity() {
        LoadOptimizerTruckCapacityDTO truckCapInfo = new LoadOptimizerTruckCapacityDTO();
        truckCapInfo.setCapStandard(10);
        truckCapInfo.setCapNonStandard(5500);
        truckCapInfo.setCapOneSide(5500);
        truckCapInfo.setTruckTon("5T");
        truckCapInfo.setTruckStyle("카고");
        return truckCapInfo;
    }

    @Transactional
    public void clearOetmhdTmshpno(final List<String> oetmskyValues) {
        // Oetmhd 테이블에 shipmentPlan No NULL
        qShipmentPlanRepository.clearOetmhdTmshpno(oetmskyValues);
        // Oetmit 테이블에 shipmentPlan No NULL
        qShipmentPlanRepository.clearOetmitTmshpno(oetmskyValues);
    }

    @Transactional
    public void updateOetmhdTmshpno(final ShipmentPlan shipmentPlan,
            final List<String> oetmskyValues) {

        // log.info("update Oetmhd Tmshpno shipmentPlan is null ? {}", ( shipmentPlan ==
        // null ) ? true : false);
        if (shipmentPlan == null) {
            return;
        }
        // log.info("clear Oetmhd Tmshpno size = {}", oetmskyValues.size());
        this.clearOetmhdTmshpno(oetmskyValues);

        // Oetmhd 테이블에 shipmentPlan No. update
        qShipmentPlanRepository.updateOetmhdTmshpno(shipmentPlan);
    }

    @Transactional
    public List<ShipmentOetmskyPairDTO> updateManualShipmentPlan(final String compkey,
            ShipmentPlan shipmentPlan,
            ShipmentPlanDeletedInfoDTO deletedInfo) {
        //
        List<ShipmentOetmskyPairDTO> pairList = new ArrayList<>();

        List<ShipmentSectionOneDTO> manualList = deletedInfo.getOetmskyManualList();
        if (manualList == null || manualList.size() < 1)
            return pairList;

        List<ShipmentSectionTwoDTO> groupOetmsky = manualList.stream()
                // 1. 그룹화: `shpl`, `shmt`, `oetmsky`를 키로 설정
                .collect(Collectors.groupingBy(dto -> Arrays.asList(dto.getShpl(), dto.getShmt(), dto.getOetmsky())))
                .entrySet()
                .stream()
                // 2. `ShipmentSectionTwoDTO`로 변환
                .map(entry -> {
                    List<ShipmentSectionOneDTO> groupedList = entry.getValue();
                    ShipmentSectionOneDTO first = groupedList.get(0); // 키에서 공통값 추출
                    List<String> shscList = groupedList.stream()
                            .map(ShipmentSectionOneDTO::getShsc)
                            .collect(Collectors.toList());
                    return new ShipmentSectionTwoDTO(
                            first.getShpl(),
                            first.getShmt(),
                            first.getOetmsky(),
                            shscList);
                })
                .collect(Collectors.toList());

        for (ShipmentSectionTwoDTO oetmskyOne : groupOetmsky) {
            Shipment oldShmt = shipmentRepository.findByCompkeyAndShpmtky(compkey, oetmskyOne.getShmt());
            Shipment newShmt = Shipment.builder()
                    .compkey(compkey)
                    .shipmentPlan(shipmentPlan)
                    .shpmtky(shipmentRepository.getNextSequenceKey())
                    .vhctncd(oldShmt.getVhctncd())
                    .vehicleType(oldShmt.getVehicleType())
                    .cargoty(oldShmt.getCargoty())
                    .ptnrkey(oldShmt.getPtnrkey())
                    .ptnamlc(oldShmt.getPtnamlc())
                    .ptnrtyp(oldShmt.getPtnrtyp())
                    .manlshp(true)
                    .build();

            Shipment savedShmt = shipmentRepository.save(newShmt);

            for (String shscky : oetmskyOne.getShscList()) {
                ShipmentSection getShsc = shipmentSectionRepository.findByCompkeyAndShpscky(compkey, shscky);
                qShipmentSectionRepository.updateManualShipmentSection(compkey, newShmt, getShsc);
            }

            ShipmentOetmskyPairDTO pair = new ShipmentOetmskyPairDTO();
            pair.setShpmtky(newShmt.getShpmtky());
            pair.setOetmsky(oetmskyOne.getOetmsky());
            pairList.add(pair);
        }
        return pairList;
    }

    @Transactional
    public ShipmentChangePlanOetmskyPairDTO saveManualShipment(final AuthDTO authDTO,
            final String oetmsky,
            final LocalDate uldrqdt,
            final LocalTime uldrqtm) {
        final String _Compkey = authDTO.getCompany();
        final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
        log.info("Save ManualShipment : oetmsky. {}/{}/{}", oetmsky, uldrqdt, uldrqtm);

        ShipmentChangePlanOetmskyPairDTO changePlanPairList = new ShipmentChangePlanOetmskyPairDTO();

        final ShipmentOetmsInfo oetmsShipInfo = this.getShpplkyFromOetmsky(authDTO, oetmsky);
        String orgShpplky = null;
        if (oetmsShipInfo != null) {
            orgShpplky = oetmsShipInfo.getOneShpplky();
            log.info("Save ManualShipment : Shpplkys:{}/Shpmtkys:{}/Shpsckys:{}", oetmsShipInfo.getShpplkys(),
                    oetmsShipInfo.getShpmtkys(), oetmsShipInfo.getShpsckys());
        }
        log.info("Save ManualShipment : oetmsky. {}/orgShpplky:{}", oetmsky, orgShpplky);

        changePlanPairList.setOetmsky(oetmsky);
        changePlanPairList.setOrgShpplky(orgShpplky);
        changePlanPairList.setOetmsShipInfo(oetmsShipInfo);

        List<ShipmentOetmskyPairDTO> pairList = new ArrayList<>();

        // 기존 shipmentSection 찾기
        List<ShipmentSection> foundAllShscList = qShipmentSectionRepository.findManualShipmentSectionByOetmsky(_Compkey,
                oetmsky);
        List<ShipmentSection> oldShscList = new ArrayList<>();

        try {
            String oldShmtky = null;
            String oldShmtPtnrkey = null;
            String oldShmtPtnamlc = null;
            PartnerTypeEnum oldShmtPtnrtyp = null;
            TruckLoadTypeEnum oldShmtCargoty = TruckLoadTypeEnum.LTL;
            ShipmentVehicleTonEnum oldShmtVhctncd = ShipmentVehicleTonEnum.T11;
            VehicleTypeEnum oldShmtVehicleType = VehicleTypeEnum.카고;

            Boolean foundOldShmtInfo = false;
            if (foundAllShscList != null && foundAllShscList.size() > 0) {
                log.info("Save ManualShipment : Old SC check");
                for (ShipmentSection sc : foundAllShscList) {
                    if (sc.getShipment() != null && !sc.getShipment().getShpmtky().isBlank()) {
                        if (sc.getShipment().getDeletat() == null && !foundOldShmtInfo) {
                            Shipment shmt0 = sc.getShipment();
                            oldShmtPtnrkey = shmt0.getPtnrkey();
                            oldShmtPtnamlc = shmt0.getPtnamlc();
                            oldShmtPtnrtyp = shmt0.getPtnrtyp();
                            oldShmtCargoty = shmt0.getCargoty();
                            oldShmtVhctncd = shmt0.getVhctncd();
                            oldShmtVehicleType = shmt0.getVehicleType();
                            oldShmtky = shmt0.getShpmtky();
                            foundOldShmtInfo = true;
                            log.info("Save ManualShipment : foundOldShmtInfo");
                        }
                    }
                    sc.setDeletat(LocalDateTime.now());
                }
            }
            qShipmentSectionRepository.updateScDeleteByOetmsky(oetmsky);
            changePlanPairList.setOrgShpmtky(oldShmtky);
            foundAllShscList = null;

            // if ( foundAllShscList == null || foundAllShscList.size() < 1 )
            {
                // 기존 shipmentSection 이 없으면 신규 생성
                log.info("Save ManualShipment : 기존 shipmentSection 없음");
                List<ShipmentTargetDAO> targetList = qShipmentPlanRepository.findShipmentTargetOne(_Compkey, oetmsky,
                        ptnrkey);
                oldShscList = makeNewShipmentSectionList(_Compkey, oetmsky, targetList);
                log.info("Save ManualShipment : New ShSc size. {}", oldShscList.size());

                List<ShipmentSection> dfScList = oldShscList.stream()
                        .filter(t -> VisitTypeEnum.DROPOFF.equals(t.getVsttype()))
                        .collect(Collectors.toList());
                if (dfScList != null && dfScList.size() > 0) {
                    ShipmentSection oneSc = dfScList.get(0);
                    if (!foundOldShmtInfo) {
                        oldShmtPtnrkey = oneSc.getPtnrkey();
                        oldShmtPtnamlc = oneSc.getPtnamlc();
                        oldShmtPtnrtyp = oneSc.getPtnrtyp();
                        oldShmtVhctncd = oneSc.getVhctncd();
                        if (targetList != null && targetList.size() > 0) {
                            oldShmtCargoty = targetList.get(0).getCargoty();
                        }
                    }
                    if (PartnerTypeEnum.CUSTOMER.equals(oneSc.getPtnrtyp())) {
                        // DropOff 가 조선소인 경우
                        oldShmtPtnrkey = oneSc.getPtnrkey();
                        oldShmtPtnamlc = oneSc.getPtnamlc();
                        oldShmtPtnrtyp = oneSc.getPtnrtyp();
                    } else {
                        // DropOff 가 조선소가 아닌 경우
                        oldShmtPtnrkey = ShipmentConstant.SHIPMENT_DEFAULT_OWNER_CUSTKEY;
                        oldShmtPtnamlc = ShipmentConstant.SHIPMENT_DEFAULT_OWNER_CUNAMLC;
                        oldShmtPtnrtyp = PartnerTypeEnum.OWNER;
                    }
                    log.info("Save ManualShipment : New ShSc ptnr. {}/{}/{}/{}/{}", oldShmtPtnrkey, oldShmtPtnamlc,
                            oldShmtPtnrtyp, oldShmtVhctncd, oldShmtCargoty);
                } else {
                    log.info("Save ManualShipment : Not found ShSc ptnr.");
                }
            }

            Integer loadrat = ShipmentConstant.SHIPMENT_MANUAL_DISPATCH_LOADRAT;
            {
                ShipmentPlan oneShpl = null;
                {
                    // Make New Shipment Plan
                    final String shpplky = shipmentPlanRepository.getNextSequenceKey();
                    final ShipmentPlan shipmentPlan = ShipmentPlan.builder()
                            .compkey(_Compkey)
                            .shpplky(shpplky)
                            .useract(authDTO.getId())
                            // .shpmtdt(dfShsc.getLudrqdt())
                            .shpmtdt(uldrqdt)
                            .roundno(0) // 수동배차내보내기는 0
                            // 수동배차내보내기는 manual flag 설정
                            .manlshp(true)
                            .ptnrkey(ptnrkey)
                            .simulop(SimulationOptionEnum.DESIGNATED)
                            .simulat(LocalDateTime.now())
                            .build();

                    shipmentPlanRepository.save(shipmentPlan);
                    oneShpl = shipmentPlan;
                    log.info("Save ManualShipment : New SHPL. {}", shpplky);
                }

                final String shpmtky = shipmentRepository.getNextSequenceKey();
                final Shipment newShmt = Shipment.builder()
                        .compkey(_Compkey)
                        .shipmentPlan(oneShpl)
                        .shpmtky(shpmtky)
                        .vhctncd(oldShmtVhctncd)
                        .vehicleType(oldShmtVehicleType)
                        .cargoty(oldShmtCargoty)
                        .loadrat(loadrat)
                        .ptnrkey(oldShmtPtnrkey)
                        .ptnamlc(oldShmtPtnamlc)
                        .ptnrtyp(oldShmtPtnrtyp)
                        .manlshp(true)
                        .build();

                shipmentRepository.save(newShmt);
                log.info("Save ManualShipment : New SHMT. {}", shpmtky);

                for (ShipmentSection sc : oldShscList) {
                    if (VisitTypeEnum.PICKUP.equals(sc.getVsttype())) {
                        sc.setVstordr(1);
                    } else if (VisitTypeEnum.DROPOFF.equals(sc.getVsttype())) {
                        sc.setVstordr(2);
                    }
                    sc.setShipment(newShmt);
                    sc.setManlshp(true);
                    sc.setDeletat(null);
                    log.info("Save ManualShipment : SHSC. {}", sc.getShpscky());
                }
                shipmentSectionRepository.saveAll(oldShscList);

                ShipmentOetmskyPairDTO pair = new ShipmentOetmskyPairDTO();
                pair.setShpmtky(shpmtky);
                pair.setOetmsky(oetmsky);
                log.info("Save ManualShipment : Add Pair. {}/{}", oetmsky, shpmtky);
                pairList.add(pair);
            }
            changePlanPairList.setPairList(pairList);

        } catch (Exception ex) {
        }

        return changePlanPairList;
    }

    @Transactional
    public Boolean cleanRemainShipmentPlanBySection(final AuthDTO authDTO,
            final ShipmentOetmskyChangeOldInfo changedInfo) {
        final String compkey = authDTO.getCompany();
        final String shipmentKey = changedInfo.getShipmentKey();
        final List<String> shpsckys = changedInfo.getShpsckys();

        log.info("Clean ShipmentPlanBySection, shipmentKey:{}", shipmentKey);

        try {
            if (shipmentKey == null || shipmentKey.isBlank()) {
                log.info("Clean ShipmentPlanBySection, Empty shipmentKey");
                return false;
            }

            Integer remainScCount = 0;
            String shpplky = null;
            Shipment shmt = shipmentRepository.findByCompkeyAndShpmtky(compkey, shipmentKey);
            if (shmt == null)
                return false;
            shpplky = shmt.getShipmentPlan().getShpplky();
            for (ShipmentSection sc : shmt.getShipmentSectionList()) {
                if (shpsckys.contains(sc.getShpscky()))
                    continue;
                if (sc.getDeletat() == null) {
                    log.info("Clean ShipmentPlanBySection, remainSc = {}", sc.getShpscky());
                    remainScCount += 1;
                    break;
                }
            }
            log.info("Clean ShipmentPlanBySection, remainScCount={} / shpplky={}", remainScCount, shpplky);
            if (remainScCount == 0) {
                qShipmentRepository.deleteShpmtky(shipmentKey);
                Boolean bRetCancelDispatch = dispatchPlanService.cancelDispatch(List.of(shipmentKey), false);
                log.info("Clean ShipmentPlanBySection, CancelDispatch Ret: {}", bRetCancelDispatch);
            } else {
                return false;
            }

            if (shpplky == null) {
                return false;
            }
            Integer remainMtCount = 0;
            ShipmentPlan shpl = shipmentPlanRepository.findByCompkeyAndShpplky(compkey, shpplky);
            if (shpl == null)
                return false;
            for (Shipment mt : shpl.getShipmentList()) {
                if (shipmentKey == mt.getShpmtky())
                    continue;
                if (mt.getDeletat() == null) {
                    remainMtCount += 1;
                    break;
                }
            }
            log.info("Clean ShipmentPlanBySection, remainMtCount={}", remainMtCount);
            if (remainMtCount != 0) {
                return false;
            }

            {
                // Plan delete
                log.info("Clean ShipmentPlanBySection, Delete shpplky:{}", shpplky);
                ShipmentPlanDeletedInfoDTO deletedInfo = this.deleteShipmentPlanByKey(shpplky);
                log.info("Clean ShipmentPlanBySection, OetmskyValues:{}", deletedInfo.getOetmskyValues());
                this.clearOetmhdTmshpno(deletedInfo.getOetmskyValues());
            }

            return true;
        } catch (Exception ex) {
            log.info("Exception : {}", ex.toString());
        }

        return false;
    }

    @Transactional
    public Boolean cleanRemainShipmentPlanByManual(final AuthDTO authDTO,
            final ShipmentChangePlanOetmskyPairDTO changePlanPairInfo) {
        final String compkey = Objects.requireNonNull(authDTO).getCompany();
        final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
        final String orgShpplky = changePlanPairInfo.getOrgShpplky();
        final String orgShpmtky = changePlanPairInfo.getOrgShpmtky();
        final String oetmsky = changePlanPairInfo.getOetmsky();

        log.info("Clean RemainShipmentPlan, orgShpplky:{}/orgShpmtky:{}/ptnrkey:{}", orgShpplky, orgShpmtky, ptnrkey);

        try {

            if (orgShpmtky != null && !orgShpmtky.isBlank()) {
                Shipment shmt = shipmentRepository.findFirstByShpmtkyAndCompkeyAndDeletatIsNull(orgShpmtky, compkey);
                if (shmt != null) {
                    log.info("Clean RemainShipmentPlan, MT: {} / {}", shmt.getShpmtky(),
                            shmt.getShipmentSectionList().size());
                    List<ShipmentSection> shscList = shmt.getShipmentSectionList();
                    // for (ShipmentSection shsc : shscList) { // for DEBUG
                    // log.info("Clean RemainShipmentPlan, (before) vstordr: {} / {} / {}",
                    // shsc.getShpscky(), shsc.getOetmsky(), shsc.getVstordr());
                    // }
                    // vstordr 순으로 정렬
                    // shscList.sort((s1, s2) -> Integer.compare(s1.getVstordr(), s2.getVstordr()));
                    shscList.sort(Comparator.nullsLast(Comparator.comparing(ShipmentSection::getVstordr))); // null 값을
                                                                                                            // 뒤에 배치
                    // for (ShipmentSection shsc : shscList) { // for DEBUG
                    // if ( shsc.getOetmsky().equals(oetmsky) ) continue;
                    // log.info("Clean RemainShipmentPlan, (sort) vstordr: {} / {} / {}",
                    // shsc.getShpscky(), shsc.getOetmsky(), shsc.getVstordr());
                    // }
                    Integer newOrderNo = 1;
                    for (ShipmentSection shsc : shscList) {
                        if (shsc.getOetmsky().equals(oetmsky))
                            continue;
                        shsc.setVstordr(newOrderNo);
                        newOrderNo += 1;
                    }
                    for (ShipmentSection shsc : shscList) { // for DEBUG
                        if (shsc.getOetmsky().equals(oetmsky))
                            continue;
                        log.info("Clean RemainShipmentPlan, (after) vstordr: {} / {} / {}", shsc.getShpscky(),
                                shsc.getOetmsky(), shsc.getVstordr());
                    }
                    shipmentSectionRepository.saveAll(shscList);
                }
            }

            if (orgShpplky == null || orgShpplky.isBlank()) {
                log.info("Clean RemainShipmentPlan, Empty orgShpplky");
                return false;
            }

            Boolean forceDeletePlan = false;
            // shipment plan 찾기
            ShipmentPlan orgShPlan = shipmentPlanRepository.findByShpplky(orgShpplky);
            if (orgShPlan == null) {
                log.info("Clean RemainShipmentPlan, Empty orgShPlan");
                return false;
            }

            final String foundShpplky = orgShPlan.getShpplky();
            if (foundShpplky == null || foundShpplky.isBlank()) {
                log.info("Clean RemainShipmentPlan, Empty foundShpplky");
                return false;
            }

            Boolean cancelDispatchShmt = false;
            List<String> oldShmtList = new ArrayList<>();

            for (Shipment shmt : orgShPlan.getShipmentList()) {
                log.info("Clean RemainShipmentPlan, MT: {} / {}", shmt.getShpmtky(), shmt.getDeletat());
                Integer remainScCount = 0;
                for (ShipmentSection shsc : shmt.getShipmentSectionList()) {
                    // log.info("Clean RemainShipmentPlan, SC: {} / {}", shsc.getShpscky(),
                    // shsc.getDeletat());
                    if (shsc.getDeletat() == null) {
                        remainScCount += 1;
                    }
                }
                log.info("Clean RemainShipmentPlan, reamin SC count: {}", remainScCount);

                if (remainScCount == 0) {
                    shmt.setDeletat(LocalDateTime.now());
                    shipmentRepository.save(shmt);
                    // String.isBlank() 메서드는 null 입력에 대해 안전하게 false를 반환합니다.
                    if (!orgShpmtky.isBlank() && orgShpmtky.equals(shmt.getShpmtky()))
                        cancelDispatchShmt = true;
                } else {
                    oldShmtList.add(shmt.getShpmtky());
                }
            }

            if (oldShmtList.size() == 0) {
                forceDeletePlan = true;
            }

            // Dispatch Table 정리
            // if ( cancelDispatchShmt )
            // 수동배차 테이블에서 삭제는 해당 Shipment 에 다른 oetmsky 가 있어도 호출
            {
                log.info("Clean RemainShipmentPlan, call CancelManualDispatch : oetmsky({}) - {}", oetmsky, orgShpmtky);
                Boolean bRetCancelDispatch = dispatchManualService.cancelManualDispatch(orgShpmtky, oetmsky, false);
                log.info("Clean RemainShipmentPlan, CancelManualDispatch Ret: {}", bRetCancelDispatch);
            }

            if (forceDeletePlan) {
                log.info("Clean RemainShipmentPlan, check forceDeletePlan: {}", foundShpplky);
            }

            if (forceDeletePlan) {
                // Plan delete
                log.info("Clean RemainShipmentPlan, Delete shpplky:{}", foundShpplky);
                ShipmentPlanDeletedInfoDTO deletedInfo = this.deleteShipmentPlanByKey(foundShpplky);
                log.info("Clean RemainShipmentPlan, OetmskyValues:{}", deletedInfo.getOetmskyValues());
                this.clearOetmhdTmshpno(deletedInfo.getOetmskyValues());
            }

            return true;
        } catch (Exception ex) {
            log.info("Exception : {}", ex.toString());
        }

        return false;
    }

    @Transactional
    public void updateOetmhdManualDispatchOnlyDateChange(final String oetmsky) {
        try {
            log.info("Clear Oetmhd tmshpno. {}", oetmsky);
            qShipmentPlanRepository.clearOetmhdTmshpno(List.of(oetmsky));
        } catch (Exception ex) {
        }
    }

    @Transactional
    public void updateOetmskyShipmentKey(List<ShipmentOetmskyPairDTO> pairList) {
        if (pairList != null && pairList.size() > 0) {
            for (ShipmentOetmskyPairDTO pair : pairList) {
                if (pair != null) {
                    log.info("Update Oetmsky ShipmentKey : {}/{}", pair.getShpmtky(), pair.getOetmsky());
                    qShipmentPlanRepository.updateOetmhdTmshpno(pair.getShpmtky(), pair.getOetmsky());
                }
            }
        }
        log.info("Update Oetmsky ShipmentKey Done");
    }

    public ShipmentPlanCheckMadeDTO getShipmentPlanCheckMade(final AuthDTO authDTO,
            @NotBlank final String compkey,
            @NotNull final LocalDate uldrqdt,
            @NotNull final Integer roundno) {

        final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
        List<ShipmentPlanCheckMadeDTO> results = qShipmentPlanRepository.findShipmentPlanByDateRound(ptnrkey, compkey,
                uldrqdt, roundno);

        if (results == null || results.size() < 1) {
            ShipmentPlanCheckMadeDTO emptyDto = new ShipmentPlanCheckMadeDTO();
            emptyDto.setCompkey(compkey);
            emptyDto.setIsmade(false);
            emptyDto.setIschanged(false);
            emptyDto.setShowTooltip(false);
            emptyDto.setShpmtdt(uldrqdt);
            emptyDto.setRoundno(roundno);
            emptyDto.setChangedshpmtdt(null);
            emptyDto.setChangedroundno(0);
            return emptyDto;
        }

        log.info("Shipment Plan CheckMade: {} / {} / {}", results.size(), results.get(0).getShpplky(),
                results.get(0).getSimulat());
        ShipmentPlanCheckMadeDTO oneResult = results.get(0);
        ShipmentPlanIsChangedDAO chagnedObj = IsChangedShipmentPlan(ptnrkey, compkey, uldrqdt, roundno);
        oneResult.setIschanged(chagnedObj.getIsChanged());
        if (chagnedObj.getIsChanged()) {
            oneResult.setIsmade(false);
            oneResult.setChangedshpmtdt(chagnedObj.getShpmtdt());
            oneResult.setChangedroundno(chagnedObj.getRoundno());
            oneResult.setShowTooltip(true);
        }

        return oneResult;
    }

    public ShipmentPlanCheckMadeDTO getShipmentPlan3CheckMade(final AuthDTO authDTO,
            @NotBlank final String compkey,
            @NotNull final LocalDate uldrqdt,
            @NotNull final Integer roundno) {

        final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
        List<ShipmentPlanCheckMadeDTO> results = qShipmentPlanRepository.findShipmentPlanByDateRound(ptnrkey, compkey,
                uldrqdt, roundno);

        if (results == null || results.isEmpty()) {
            ShipmentPlanCheckMadeDTO emptyDto = new ShipmentPlanCheckMadeDTO();
            emptyDto.setCompkey(compkey);
            emptyDto.setIsmade(false);
            emptyDto.setIschanged(false);
            emptyDto.setShowTooltip(false);
            emptyDto.setShpmtdt(uldrqdt);
            emptyDto.setRoundno(roundno);
            emptyDto.setChangedshpmtdt(null);
            emptyDto.setChangedroundno(0);
            return emptyDto;
        }

        log.info("Shipment Plan CheckMade: {} / {} / {}", results.size(), results.get(0).getShpplky(),
                results.get(0).getSimulat());
        ShipmentPlanCheckMadeDTO oneResult = results.get(0);
        ShipmentPlanIsChangedDAO chagnedObj = IsChanged3ShipmentPlan(ptnrkey, compkey, oneResult);
        oneResult.setIschanged(chagnedObj.getIsChanged());
        if (chagnedObj.getIsChanged()) {
            oneResult.setIsmade(false);
            oneResult.setChangedshpmtdt(chagnedObj.getShpmtdt());
            oneResult.setChangedroundno(chagnedObj.getRoundno());
            oneResult.setShowTooltip(true);
        }
        oneResult.setIsmade(false);
        ShipmentPlan shipmentPlan = shipmentPlanRepository.findByShpplkyAndDeletatIsNull(oneResult.getShpplky());
        for (Shipment shipment : shipmentPlan.getShipmentList()) {
            if (shipment.getDeletat() == null && shipment.getFixedat() == null) {
                oneResult.setIsmade(true);
                break;
            }
        }

        return oneResult;
    }

    public ShipmentResultCountDTO getShipmentResultCount(final AuthDTO authDTO,
            @NotBlank final String compkey,
            @NotNull final LocalDate uldrqdt,
            @NotNull final Integer roundno) {

        ShipmentResultCountDTO retResultCount = new ShipmentResultCountDTO();
        List<ShipmentResultCountItemDTO> resultList = new ArrayList<>();

        final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
        List<ShipmentResultCountDummyDTO> dummyResultCountList = qShipmentPlanRepository.findShipmentResultCount(
                ptnrkey,
                compkey,
                uldrqdt,
                roundno);

        if (dummyResultCountList.size() < 1) {
            return retResultCount;
        }

        List<List<ShipmentResultCountDummyDTO>> groupPtnrkeyList = dummyResultCountList.stream()
                .filter(dto -> dto.getPtnrkey() != null) // null 키 제거
                .collect(Collectors.groupingBy(ShipmentResultCountDummyDTO::getPtnrkey))
                .values().stream()
                .collect(Collectors.toList());

        for (List<ShipmentResultCountDummyDTO> ptnrkeyList : groupPtnrkeyList) {
            // 단독 건수와 혼적 건수를 별도로 집계
            int ftlCount = (int) ptnrkeyList.stream()
                    .filter(s -> s.getCargoty() == TruckLoadTypeEnum.FTL)
                    // .mapToInt(ShipmentResultCountDummyDTO::getVhctncd)
                    .count();
            int ltlCount = (int) ptnrkeyList.stream()
                    .filter(s -> s.getCargoty() == TruckLoadTypeEnum.LTL)
                    // .mapToInt(ShipmentResultCountDummyDTO::getVhctncd_count)
                    .count();
            int totalCount = (ftlCount + ltlCount);

            if (ptnrkeyList.size() > 0) {
                ShipmentResultCountDummyDTO first = ptnrkeyList.get(0);
                ShipmentResultCountItemDTO ptnrItem = new ShipmentResultCountItemDTO(
                        first.getCompkey(),
                        first.getPtnrkey(),
                        first.getPtnamlc(),
                        first.getPtnrtyp(),
                        totalCount,
                        ftlCount,
                        ltlCount);
                // 차량중량별 수 (groupPtnrkey)
                Map<ShipmentVehicleTonEnum, Integer> vhctnCount = ptnrkeyList.stream()
                        .collect(Collectors.groupingBy(ShipmentResultCountDummyDTO::getVhctncd,
                                Collectors.summingInt(e -> 1)));

                //// for 이용한 count
                // Map<ShipmentVehicleTonEnum, Integer> vhctnCount = new HashMap<>();
                // for (ShipmentResultCountDummyDTO dto : ptnrkeyList) {
                // ShipmentVehicleTonEnum tonEnum = dto.getVhctncd();
                // vhctnCount.put(tonEnum, vhctnCount.getOrDefault(tonEnum, 0) + 1);
                // }

                ptnrItem.setVhctnCount(vhctnCount);

                resultList.add(ptnrItem);
            }
        }

        // 차량중량별 수 (전체)
        Map<ShipmentVehicleTonEnum, Integer> vhctnCountTotal = dummyResultCountList.stream()
                .collect(Collectors.groupingBy(ShipmentResultCountDummyDTO::getVhctncd, Collectors.summingInt(e -> 1)));
        retResultCount.setVhctnCount(vhctnCountTotal);

        String shpplky = null;
        // Boolean isFixed = false;
        if (resultList.size() > 0) {
            shpplky = dummyResultCountList.get(0).getShpplky();
            // if ( dummyResultCountList.get(0).getFixedat() != null ) isFixed = true;
            // retResultCount.setFixedat(dummyResultCountList.get(0).getFixedat());

            List<String> shpmtkyList = this.getShipmentListByShpplkyExceptFixedShipment(shpplky);
            // Boolean isCheck = true 이면 chekc만 하고, isCheck = false 면 실제 DB 에서 지우도록 하였습니다.
            Boolean bRet = dispatchPlanService.cancelDispatch(shpmtkyList, true);
            retResultCount.setIsDispatch(bRet);
        }
        retResultCount.setShpplky(shpplky);
        retResultCount.setCountItemList(resultList);
        retResultCount.setIschanged(false);
        // retResultCount.setIsFixed(isFixed);

        return retResultCount;
    }

    public ShipmentTargetResultCountDTO getShipmentTargetResultCount(final AuthDTO authDTO,
            @NotBlank final String compkey,
            @NotNull final LocalDate uldrqdt,
            @NotNull final Integer roundno) {

        try {
            ShipmentTargetResultCountDTO result = new ShipmentTargetResultCountDTO();

            ShipmentResultCountDTO resultCount = this.getShipmentResultCount(authDTO, compkey, uldrqdt, roundno);
            /*
             * ShipmentPlanChangedTargetDTO targetCount =
             * this.getShipmentTargetCount(authDTO, compkey, uldrqdt, roundno,
             * VisitTypeEnum.DROPOFF, false, uldrqdt, roundno, false); // allStatus : true
             * -> false
             */
            ShipmentPlanChangedTargetDTO targetCount = this.getShipmentTargetCount(authDTO, compkey, uldrqdt, roundno,
                    VisitTypeEnum.DROPOFF, false, false); // allStatus : true -> false

            result.setResultCount(resultCount);
            result.setTargetCount(targetCount);

            return result;
        } catch (Exception ex) {
            log.info("Exception: {}", ex.toString());
        }
        return null;
    }

    @Deprecated
    public ShipmentResultDTO getShipmentResultList(final AuthDTO authDTO,
            @NotBlank final String compkey,
            @NotNull final LocalDate uldrqdt,
            @NotNull final Integer roundno,
            @NotBlank final String ptnrkey) {

        ShipmentResultDTO results = new ShipmentResultDTO();
        List<ShipmentResultItemDTO> itemList = new ArrayList<>();

        final String _authPtnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
        List<Object[]> rawResults = shipmentPlanRepository.findShipmentResultRaw(_authPtnrkey,
                compkey, uldrqdt, roundno, ptnrkey);
        List<ShipmentResultDummyDTO> dummyList = rawResults.stream()
                .map(row -> new ShipmentResultDummyDTO(
                        (String) row[0], // compkey
                        (String) row[1], // shpmtky
                        TruckLoadTypeEnum.valueOf((String) row[2]), // cargoty
                        ShipmentVehicleTonEnum.valueOf((String) row[3]), // vhctncd
                        (String) row[4], // piaddr
                        (String) row[5], // deaddr
                        (Integer) row[6], // loadrat
                        (String) row[7], // ptnrkey
                        (String) row[8], // destkey
                        (LocalDate) ((java.sql.Date) row[9]).toLocalDate(), // shpmtdt
                        (String) row[10], // oetmsky
                        (String) row[11], // ptnamlc
                        PartnerTypeEnum.valueOf((String) row[12]), // ptnrtyp
                        (String) row[13], // shpplky
                        ((Long) row[14]).intValue(), // countofpickup
                        ((Long) row[15]).intValue() // countofdropoff
                ))
                .collect(Collectors.toList());

        Integer totalcount = 0;
        Integer ftlcount = 0;
        Integer ltlcount = 0;

        results.setCompkey(compkey);
        results.setShpmtdt(uldrqdt);
        results.setRoundno(roundno);
        if (dummyList.size() > 0) {
            results.setShpplky(dummyList.get(0).getShpplky());
            results.setPtnrkey(dummyList.get(0).getPtnrkey());
            results.setPtnamlc(dummyList.get(0).getPtnamlc());
            results.setPtnrtyp(dummyList.get(0).getPtnrtyp());
        }

        for (ShipmentResultDummyDTO shipItem : dummyList) {
            ShipmentResultItemDTO one = new ShipmentResultItemDTO();
            one.setShpmtky(shipItem.getShpmtky());
            one.setCargoty(shipItem.getCargoty());
            one.setVhctncd(shipItem.getVhctncd());
            one.setPiaddr(shipItem.getPiaddr());
            one.setDeaddr(shipItem.getDeaddr());
            one.setLoadrat(shipItem.getLoadrat());
            one.setDestkey(shipItem.getDestkey());
            one.setOetmsky(shipItem.getOetmsky());
            one.setCountofpickup(shipItem.getCountofpickup());
            one.setCountofdropoff(shipItem.getCountofdropoff());
            Integer vhc[] = ShipmentUtils.getTruckCountByVehicle(shipItem.getCargoty(), shipItem.getVhctncd());
            ftlcount += vhc[0];
            ltlcount += vhc[1];
            totalcount += 1;

            Integer estimatedfee = (shipItem.getCountofpickup() + shipItem.getCountofdropoff()) * 5000;
            one.setEstimatedfee(estimatedfee);

            // shipment 단위 제약사항
            List<ShipmentSectionDTO> shscList = shipmentSectionService.getShipmentSectionListByShipment(
                    shipItem.getCompkey(), shipItem.getShpmtky());
            String concatenatedString = shscList.stream()
                    .map(ShipmentSectionDTO::getUnavat1)
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(", "));
            if (!concatenatedString.isEmpty()) {
                one.setRestriction(concatenatedString);
            }

            itemList.add(one);
        }

        results.setTotalcount(totalcount);
        results.setFtlcount(ftlcount);
        results.setLtlcount(ltlcount);
        results.setShipmentItems(itemList);

        return results;
    }

    public List<ShipmentMapResultDTO> getShipmentMapResultList(final AuthDTO authDTO,
            @NotBlank final String compkey,
            @NotNull final LocalDate uldrqdt,
            @NotNull final Integer roundno) {

        List<ShipmentMapResultDTO> shipMapList = new ArrayList<>();

        final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
        List<ShipmentMapDummyDTO> dummyList = qShipmentPlanRepository.findShipmentMapResultList(ptnrkey, compkey,
                uldrqdt, roundno);

        List<ShipmentMapDummyDTO> ptnrkeyList = dummyList.stream()
                .filter(t -> t.getDfvsttype().equals(VisitTypeEnum.DROPOFF))
                .collect(Collectors.groupingBy(ShipmentMapDummyDTO::getDfptnrkey))
                .values().stream()
                // .flatMap(List::stream) // 평탄화
                .map(list -> list.get(0)) // 각 그룹의 첫 번째 요소만 선택 (중복제거 distinct)
                .collect(Collectors.toList());

        for (ShipmentMapDummyDTO ptnrDummy : ptnrkeyList) {
            Integer totalvhctncd = 0;
            Integer ftlvhctncd = 0;
            Integer ltlvhctncd = 0;

            List<ShipmentMapDummyDTO> filteredPtnrList = dummyList.stream()
                    .filter(t -> t.getDfptnrkey().equals(ptnrDummy.getDfptnrkey()))
                    .collect(Collectors.toList());

            Point decoord = dummyList.stream()
                    .filter(t -> t.getDfptnrkey().equals(ptnrDummy.getDfptnrkey()))
                    .filter(t -> t.getDfvsttype().equals(VisitTypeEnum.DROPOFF))
                    .findFirst()
                    .map(t -> t.getDfdecoord())
                    .orElse(null); // 매칭되는 값이 없으면 null 반환

            ShipmentMapResultDTO onePtnr = new ShipmentMapResultDTO();
            onePtnr.setCompkey(compkey);
            onePtnr.setPtnrkey(ptnrDummy.getDfptnrkey());
            onePtnr.setPtnamlc(ptnrDummy.getDfptnamlc());
            onePtnr.setPtnrtyp(ptnrDummy.getDfptnrtyp());
            onePtnr.setDecoord(NumberUtils.getPoint6ByPoint(decoord));

            // shpmtky 갯수 (아래 List 를 이용)
            // long uniqueShpmtkyCount = filteredPtnrList.stream()
            // .map(ShipmentMapDummyDTO::getShpmtky) // shpmtky 값 추출
            // .distinct() // 중복 제거
            // .count(); // 갯수 계산
            // log.info("shpmtky 개수: " + uniqueShpmtkyCount);
            // totalvhctncd = NumberUtils.LongToInt(uniqueShpmtkyCount);

            // shpmtky unique list (DROPOFF)
            List<ShipmentMapDummyDTO> distinctShpmtkyList = filteredPtnrList.stream()
                    .filter(t -> VisitTypeEnum.DROPOFF.equals(t.getDfvsttype())) // Dfvsttype == DROPOFF
                    .collect(Collectors.toMap(
                            ShipmentMapDummyDTO::getShpmtky, // 중복제거 기준: shpmtky
                            t -> t, // 값을 그대로 유지
                            (existing, replacement) -> existing)) // 중복 발생 시 기존 값을 유지
                    .values()
                    .stream()
                    .collect(Collectors.toList());

            if (distinctShpmtkyList != null)
                totalvhctncd = distinctShpmtkyList.size();
            log.info("shpmtky List 개수: {}", totalvhctncd);

            Map<ShipmentVehicleTonEnum, Integer> ftlVehicleTonMap = new HashMap<>();
            Map<ShipmentVehicleTonEnum, Integer> ltlVehicleTonMap = new HashMap<>();
            for (ShipmentMapDummyDTO shmt : distinctShpmtkyList) {
                if (TruckLoadTypeEnum.LTL.equals(shmt.getCargoty())) {
                    ltlvhctncd += 1;
                    ShipmentVehicleTonEnum.addVehicleTonMap(shmt.getVhctncd(), ltlVehicleTonMap);
                } else if (TruckLoadTypeEnum.FTL.equals(shmt.getCargoty())) {
                    ftlvhctncd += 1;
                    ShipmentVehicleTonEnum.addVehicleTonMap(shmt.getVhctncd(), ftlVehicleTonMap);
                }
            }
            // log.info("shpmtky List FTL Map: {}", ftlVehicleTonMap);
            // log.info("shpmtky List LTL Map: {}", ltlVehicleTonMap);
            onePtnr.setFtlVehicleTonMap(ftlVehicleTonMap);
            onePtnr.setLtlVehicleTonMap(ltlVehicleTonMap);
            // log.info("shpmtky Str FTL : {}",
            // ShipmentVehicleTonEnum.strVehicleTonMap(ftlVehicleTonMap));
            // log.info("shpmtky Str LTL : {}",
            // ShipmentVehicleTonEnum.strVehicleTonMap(ltlVehicleTonMap));
            onePtnr.setFtlVehicleTonStr(ShipmentVehicleTonEnum.strVehicleTonMap(ftlVehicleTonMap));
            onePtnr.setLtlVehicleTonStr(ShipmentVehicleTonEnum.strVehicleTonMap(ltlVehicleTonMap));

            List<ShipmentMapDest> shipMapDest = new ArrayList<>();
            for (ShipmentMapDummyDTO sec : filteredPtnrList) {
                // PickUp
                ShipmentMapDest mapPuDest = new ShipmentMapDest();
                mapPuDest.setShpmtky(sec.getShpmtky());
                mapPuDest.setCargoty(sec.getCargoty());
                mapPuDest.setPtnrkey(sec.getPuptnrkey());
                mapPuDest.setPtnamlc(sec.getPuptnamlc());
                mapPuDest.setPtnrtyp(sec.getPuptnrtyp());
                mapPuDest.setDestkey(sec.getPudestkey());
                mapPuDest.setDenamlc(sec.getPudenamlc());
                mapPuDest.setDecoord(NumberUtils.getPoint6ByPoint(sec.getPudecoord()));
                mapPuDest.setVsttype(sec.getPuvsttype());
                mapPuDest.setOetmsky(sec.getPuoetmsky());
                shipMapDest.add(mapPuDest);

                // DropOff
                ShipmentMapDest mapDfDest = new ShipmentMapDest();
                mapDfDest.setShpmtky(sec.getShpmtky());
                mapDfDest.setCargoty(sec.getCargoty());
                mapDfDest.setPtnrkey(sec.getDfptnrkey());
                mapDfDest.setPtnamlc(sec.getDfptnamlc());
                mapDfDest.setPtnrtyp(sec.getDfptnrtyp());
                mapDfDest.setDestkey(sec.getDfdestkey());
                mapDfDest.setDenamlc(sec.getDfdenamlc());
                mapDfDest.setDecoord(NumberUtils.getPoint6ByPoint(sec.getDfdecoord()));
                mapDfDest.setVsttype(sec.getDfvsttype());
                mapDfDest.setOetmsky(sec.getDfoetmsky());
                shipMapDest.add(mapDfDest);

                // Integer vhc [] = ShipmentUtils.getTruckCountByVehicle(sec.getCargoty(),
                // sec.getVhctncd());
            }
            onePtnr.setShipmentMapDest(shipMapDest);
            onePtnr.setTotalvhctncd(totalvhctncd);
            onePtnr.setFtlvhctncd(ftlvhctncd);
            onePtnr.setLtlvhctncd(ltlvhctncd);

            shipMapList.add(onePtnr);
        }
        return shipMapList;
    }

    public ShipmentAllTrackInfoDTO getShipmentAllTrackList(final AuthDTO authDTO,
            @NotNull final LocalDate uldrqdt,
            @NotNull final Integer roundno,
            @NotBlank final String ptnrkey) {
        final String compkey = Objects.requireNonNull(authDTO).getCompany();
        final String shplPtnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
        ShipmentAllTrackInfoDTO results = null;

        try {
            ShipmentPlan shpl = shipmentPlanRepository.findFirstByCompkeyAndPtnrkeyAndShpmtdtAndRoundnoAndDeletatNull(
                    compkey, shplPtnrkey, uldrqdt, roundno);
            if (shpl == null)
                return results;

            List<String> shmtList = new ArrayList<>();
            for (Shipment shmt : shpl.getShipmentList()) {
                if (ptnrkey.equals(shmt.getPtnrkey())) {
                    shmtList.add(shmt.getShpmtky());
                }
            }
            results = shipmentService.getShipmentAllTrackInfo(compkey, shmtList);
            results.setUldrqdt(uldrqdt);
            results.setRoundno(roundno);
            results.setPtnrkey(ptnrkey);
            results.setShpplky(shpl.getShpplky());
        } catch (Exception ex) {
        }
        return results;
    }

    public ShipmentResultDTO getShipmentResult2List(final AuthDTO authDTO,
            @NotBlank final String compkey,
            @NotNull final LocalDate uldrqdt,
            @NotNull final Integer roundno,
            @NotBlank final String ptnrkey) {

        ShipmentResultDTO results = new ShipmentResultDTO();
        List<ShipmentResultItemDTO> itemList = new ArrayList<>();

        try {
            final String authPtnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
            List<ShipmentResultListDummyDTO> targetList = qShipmentPlanRepository.findShipmentResultPtnrkey(authPtnrkey,
                    compkey, uldrqdt, roundno, ptnrkey);

            if (targetList == null || targetList.size() < 1) {
                return results;
            }

            results.setCompkey(compkey);
            results.setShpmtdt(uldrqdt);
            results.setRoundno(roundno);

            ShipmentResultListDummyDTO first = targetList.get(0);
            results.setShpplky(first.getShpplky());
            results.setPtnrkey(first.getPtnrkey());
            results.setPtnamlc(first.getPtnamlc());
            results.setPtnrtyp(first.getPtnrtyp());

            // LTL. 만 추출
            List<ShipmentResultListDummyDTO> uniqueLTLList = targetList.stream()
                    .filter(t -> t.getCargoty().equals(TruckLoadTypeEnum.LTL)) // LTL 필드만 추출
                    .collect(Collectors.toList()); // List로 수집
            if (uniqueLTLList != null)
                results.setLtlcount(uniqueLTLList.size());

            // FTL. 만 추출
            List<ShipmentResultListDummyDTO> uniqueFTLList = targetList.stream()
                    .filter(t -> t.getCargoty().equals(TruckLoadTypeEnum.FTL)) // FTL 필드만 추출
                    .collect(Collectors.toList()); // List로 수집
            if (uniqueFTLList != null)
                results.setFtlcount(uniqueFTLList.size());

            // Shipment No. 만 추출
            List<String> uniqueShpmtkyList = targetList.stream()
                    .map(t -> t.getShpmtky()) // shpmtky 필드만 추출
                    .distinct() // 중복 제거
                    .collect(Collectors.toList()); // List로 수집

            Integer ftlCount = 0;
            Integer ltlCount = 0;

            for (String shpmtky : uniqueShpmtkyList) {
                // 하차지 순서로 소팅 후 주소를 비교해야 의미가 있을것 같음
                List<ShipmentResultListDummyDTO> filteredShpmtky = targetList.stream()
                        .filter(t -> t.getShpmtky().equals(shpmtky))
                        .sorted(Comparator.comparing(ShipmentResultListDummyDTO::getDestkey))
                        .collect(Collectors.toList());

                // List<ShipmentResultListDummyDTO> filteredShpmtky = targetList.stream()
                // .filter(t -> t.getShpmtky().equals(shpmtky))
                // .collect(Collectors.toList());

                ShipmentResultItemDTO one = new ShipmentResultItemDTO();
                if (filteredShpmtky != null && filteredShpmtky.size() > 0) {
                    ShipmentResultListDummyDTO firstOne = filteredShpmtky.get(0);
                    one.setShpmtky(firstOne.getShpmtky());
                    one.setCargoty(firstOne.getCargoty());
                    one.setVhctncd(firstOne.getVhctncd());
                    one.setLoadrat(firstOne.getLoadrat());
                    one.setOetmsky(firstOne.getOetmsky());
                    if (one.getCargoty().equals(TruckLoadTypeEnum.FTL))
                        ftlCount += 1;
                    if (one.getCargoty().equals(TruckLoadTypeEnum.LTL))
                        ltlCount += 1;

                    Integer pickupCount = 0;
                    Integer dropoffCount = 0;
                    Boolean foundCustomer = false;
                    Boolean foundDestkey = false;
                    String sumAddrPickup = "";
                    String sumAddrDropoff = "";
                    String prevAddr = null;
                    BigDecimal totalWeight = BigDecimal.ZERO;
                    List<String> debug_puaddr = new ArrayList<>();
                    List<String> debug_dfaddr = new ArrayList<>();
                    for (ShipmentResultListDummyDTO shipItem : filteredShpmtky) {
                        String piaddr = shipItem.getPiaddr();
                        log.info("getShipmentResult2List: {},{} addr = {} / {}", shipItem.getVstordr(),
                                shipItem.getVsttype(), piaddr, prevAddr);
                        if (piaddr == null || piaddr.equals(prevAddr)) {
                            // 이전과 동일 목적지
                        } else {
                            if (shipItem.getVsttype().equals(VisitTypeEnum.PICKUP)) {
                                if (sumAddrPickup.length() > 0) {
                                    sumAddrPickup = sumAddrPickup + "... > ";
                                }
                                sumAddrPickup = sumAddrPickup + piaddr.substring(0, Math.min(3, piaddr.length()));
                                pickupCount += 1;
                            } else if (shipItem.getVsttype().equals(VisitTypeEnum.DROPOFF)) {
                                // 같은 "삼성중공업" (조선소) 이라도 Dock 이 다르면 다른것으로 처리
                                if (sumAddrDropoff.length() > 0) {
                                    sumAddrDropoff = sumAddrDropoff + "... > ";
                                }
                                if (foundCustomer == false) {
                                    sumAddrDropoff = sumAddrDropoff + piaddr.substring(0, Math.min(3, piaddr.length()));
                                    dropoffCount += 1;
                                }

                                /*
                                 * // 같은 "삼성중공업" (조선소) 이라면 동일한 것으로 처리
                                 * if ( sumAddrDropoff.length() > 0 && ! foundCustomer ) {
                                 * sumAddrDropoff = sumAddrDropoff + "... > ";
                                 * }
                                 * if ( shipItem.getPtnrtyp().equals(PartnerTypeEnum.CUSTOMER) ) {
                                 * if ( foundCustomer == false ) {
                                 * String scptnamlc = shipItem.getScptnamlc();
                                 * sumAddrDropoff = scptnamlc.substring(0, Math.min(12, scptnamlc.length()));
                                 * dropoffCount += 1;
                                 * foundCustomer = true;
                                 * }
                                 * } else {
                                 * if ( foundCustomer == false ) {
                                 * sumAddrDropoff = sumAddrDropoff + piaddr.substring(0, Math.min(3,
                                 * piaddr.length()));
                                 * dropoffCount += 1;
                                 * }
                                 * }
                                 */
                            }
                        }
                        if (shipItem.getVsttype().equals(VisitTypeEnum.PICKUP)) {
                            debug_puaddr.add(piaddr);
                        } else if (shipItem.getVsttype().equals(VisitTypeEnum.DROPOFF)) {
                            debug_dfaddr.add(piaddr);
                        }
                        prevAddr = shipItem.getPiaddr();

                        // 총중량계산
                        if (shipItem.getVsttype().equals(VisitTypeEnum.DROPOFF)) {
                            log.info("Weight = {} * {}", shipItem.getOpakqty(), shipItem.getPkrweig());
                            totalWeight = NumberUtils.calcWeightAndaddSum(
                                    shipItem.getOpakqty(), shipItem.getPkrweig(), totalWeight);
                        }

                        if (shipItem.getVsttype().equals(VisitTypeEnum.DROPOFF)
                                && shipItem.getPtnrtyp().equals(PartnerTypeEnum.CUSTOMER) && !foundDestkey) {
                            one.setDestkey(shipItem.getDestkey());
                            foundDestkey = true;
                        }
                    }

                    one.setPiaddr(sumAddrPickup);
                    one.setDeaddr(sumAddrDropoff);
                    one.setCountofpickup(pickupCount);
                    one.setCountofdropoff(dropoffCount);
                    one.setDebug_puaddr(debug_puaddr);
                    one.setDebug_dfaddr(debug_dfaddr);
                    one.setTotalWeight(totalWeight);

                    one.setEstimatedfee(firstOne.getEstimatedfee());

                    // shipment 단위 제약사항
                    List<String> restrictions = new ArrayList<>();
                    List<ShipmentSectionDTO> shscList = shipmentSectionService.getShipmentSectionListByShipment(
                            firstOne.getCompkey(), firstOne.getShpmtky());
                    String concatenatedString = "";
                    // concatenatedString = shscList.stream()
                    // .map(ShipmentSectionDTO::getUnavat1)
                    // // .filter(Objects::nonNull)
                    // .map(ShipmentUtils::convertUnavat1Code)
                    // .collect(Collectors.joining(","));
                    // concatenatedString = ShipmentUtils.trimCommas(concatenatedString);

                    for (ShipmentSectionDTO s : shscList) {
                        restrictions.add(ShipmentUtils.convertUnavat1Code(s.getUnavat1()));
                    }
                    one.setRestrictions(restrictions);

                    concatenatedString = ShipmentUtils.list2SimpleCommas(restrictions);
                    one.setRestriction(concatenatedString);

                    itemList.add(one);
                }
            }

            results.setTotalcount((ftlCount + ltlCount));
            results.setFtlcount(ftlCount);
            results.setLtlcount(ltlCount);
            results.setShipmentItems(itemList);
        } catch (Exception ex) {
        }

        return results;
    }

    // @Transactional
    // public Boolean deleteShipmentPlanAfter(@NotBlank final String compkey,
    // @NotBlank final LocalDate today,
    // @NotBlank final LocalDate startShpmtdt,
    // @NotBlank final Integer startRoundno) {

    // final LocalDate endday = today.plusDays(5);
    // log.info("delete ShipmentPlan After Args startShpmtdt : {} / startRoundno :
    // {} / endday : {}",
    // startShpmtdt, startRoundno, endday);

    // LocalDate uldrqdt = startShpmtdt;
    // // 해당일자의 남은 Round
    // for ( int roundno = startRoundno; roundno <= 3; roundno ++ ) {
    // log.info("delete first shipment plan => date : {} / round : {}", uldrqdt,
    // roundno);
    // // this.deleteShipmentPlans(compkey, uldrqdt, roundno);
    // }
    // uldrqdt = uldrqdt.plusDays(1);

    // // 해당일자 이후의 일자들
    // while ( ! uldrqdt.isAfter(endday) ) {
    // for ( int roundno = 1; roundno <= 3; roundno ++ ) {
    // log.info("delete shipment plan => date : {} / round : {}", uldrqdt, roundno);
    // // this.deleteShipmentPlans(compkey, uldrqdt, roundno);
    // }
    // uldrqdt = uldrqdt.plusDays(1);
    // }

    // return true;
    // }

    public List<CarrierInfoDTO> getCarrierUserInfos() {
        List<CarrierInfoDTO> uniquePtnrKeys = null;

        try {
            List<CarrierInfoDTO> userList = qShipmentPlanRepository.getCarrierUserInfos();

            // ptnrkey를 기준으로 중복 제거
            uniquePtnrKeys = new ArrayList<>(
                    userList.stream()
                            .collect(Collectors.toMap(
                                    CarrierInfoDTO::getPtnrkey, // ptnrkey를 키로 사용
                                    dto -> dto, // 원본 객체를 값으로 사용
                                    (existing, replacement) -> existing, // 중복 키는 기존 객체 유지
                                    LinkedHashMap::new // 순서를 유지하는 Map
                            ))
                            .values());
        } catch (Exception ex) {
        }

        return uniquePtnrKeys;
    }

    public ShipmentPlanIsChangedDAO IsChangedShipmentPlan(final String ptnrkey,
            @NotBlank final String compkey,
            final LocalDate uldrqdt,
            final Integer roundno) {

        final LocalDate today = LocalDate.now();
        final LocalDate endday = today.plusDays(5);
        ShipmentPlanIsChangedDAO shipChangedInfo = new ShipmentPlanIsChangedDAO();
        shipChangedInfo.setIsChanged(false);

        // changed List
        List<ShipmentPlanChangedSimpleDTO> changedBasicList = qShipmentPlanRepository
                .findSimpleChanged2ShipmentPlan(ptnrkey, compkey, today, endday, uldrqdt, roundno);

        // TSHITST상태가 REQUEST->SHIP 된 것은 제외
        // List<ShipmentPlanChangedSimpleDTO> changedList = null;
        if (changedBasicList != null && changedBasicList.size() > 0) {
            changedBasicList = changedBasicList.stream()
                    .filter(t -> !(t.getTshitstn() != null && OrderStatusEnum.SHIP.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.PLANCMP.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.LOAD.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.LOADHOLD.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.UNLOAD.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.DLVCMP.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.STGREQU.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.STGAPPR.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.STGREJE.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.STGSELF.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.SHPSELF.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.NEW.equals(t.getTshitstn())))
                    .collect(Collectors.toList());

        }

        if (changedBasicList != null && changedBasicList.size() > 0) {
            List<String> oetmskyList = changedBasicList.stream()
                    .map(ShipmentPlanChangedSimpleDTO::getOetmsky)
                    .collect(Collectors.toList());

            oetmskyList = ShipmentUtils.distinctStringList(oetmskyList);
            log.info("Is ChangedShipmentPlan, oetmskyList:{}", oetmskyList);
            List<OetmhdSimpleDTO> oetmhdList = qShipmentRepository.findOetmhdSimpleByOetmskyList(ptnrkey, compkey,
                    oetmskyList);
            // 긴급운송 제외
            List<OetmhdSimpleDTO> filteredOetmhdList = oetmhdList.stream()
                    .filter(t -> t.getTurgtyn().equals(false))
                    .collect(Collectors.toList());

            if (filteredOetmhdList != null && filteredOetmhdList.size() > 0) {
                log.info("Is ChangedShipmentPlan, filteredOetmhdList Size:{}/{}", filteredOetmhdList.size(),
                        filteredOetmhdList.get(0).getOetmsky());
                // 해당 일자의 변경건 체크
                List<OetmhdSimpleDTO> dateOetmhdList = filteredOetmhdList.stream()
                        .filter(t -> t.getUldrqdt().equals(uldrqdt))
                        .collect(Collectors.toList());

                log.info("Is ChangedShipmentPlan, dateOetmhdList Size:{}", dateOetmhdList.size());
                for (OetmhdSimpleDTO one : dateOetmhdList) {
                    // 해당 회차의 변경건 체크
                    log.info("Is ChangedShipmentPlan, check Round({}) / time({} {} / {} {}) / {}", roundno,
                            one.getLodrqdt(), one.getLodrqtm(), one.getUldrqdt(), one.getUldrqtm(), one.getOetmsky());
                    if (ShipmentUtils.isTimeInRound(roundno, one.getLodrqdt(), one.getLodrqtm(), one.getUldrqdt(),
                            one.getUldrqtm())) {
                        log.info("Is ChangedShipmentPlan, found time({}) in Round({})/{}", one.getUldrqtm(), roundno,
                                one.getOetmsky());
                        shipChangedInfo.setIsChanged(true);
                        shipChangedInfo.setShpmtdt(one.getUldrqdt());
                        break;
                    }
                }

            } else {
                log.info("Is ChangedShipmentPlan, Empty filteredOetmhdList");
            }
        }
        shipChangedInfo.setRoundno(roundno);

        return shipChangedInfo;
    }

    public ShipmentPlanIsChangedDAO IsChanged3ShipmentPlan(final String ptnrkey,
            @NotBlank final String compkey,
            final ShipmentPlanCheckMadeDTO shPlan) {

        ShipmentPlanIsChangedDAO shipChangedInfo = new ShipmentPlanIsChangedDAO();
        shipChangedInfo.setIsChanged(false);

        // changed List
        List<ShipmentPlanChangedSimpleDTO> changedBasicList = qShipmentPlanRepository
                .findSimpleChanged3ShipmentPlan(ptnrkey, compkey, shPlan);

        if (changedBasicList == null || changedBasicList.size() < 1) {
            return shipChangedInfo;
        }

        // DEBUG
        // for ( ShipmentPlanChangedSimpleDTO s : changedBasicList ) {
        // log.info("{} = {} ({} {}) ({} {})", s.getOetmsky(), s.getCrudmod(),
        // s.getCredate(), s.getCretime(), s.getLmodate(), s.getLmotime());
        // }

        // oetmsky List
        List<String> oetmskyList = changedBasicList.stream()
                .map(ShipmentPlanChangedSimpleDTO::getOetmsky)
                .collect(Collectors.toList());
        // oetmsky 중복제거
        oetmskyList = ShipmentUtils.distinctStringList(oetmskyList);
        log.info("Is Changed3ShipmentPlan, oetmskyList size: {}", oetmskyList.size());

        // oetmsky 으로 oetmhd 정보
        List<OetmhdSimpleDTO> oetmhdList = qShipmentRepository.findOetmhdSimpleByOetmskyList(ptnrkey, compkey,
                oetmskyList);
        // 긴급운송 제외
        List<OetmhdSimpleDTO> unTurgtynAllList = oetmhdList.stream()
                .filter(t -> t.getTurgtyn().equals(false))
                .collect(Collectors.toList());

        // REQUEST,CANCEL, SHIP 이외 제거 (SHIP 은 아래서 개별로 체크)
        List<OetmhdSimpleDTO> unTurgtynList = unTurgtynAllList.stream()
                .filter(t -> !(t.getTshitst() != null && OrderStatusEnum.PLANCMP.equals(t.getTshitst()))
                        && !(t.getTshitst() != null && OrderStatusEnum.LOAD.equals(t.getTshitst()))
                        && !(t.getTshitst() != null && OrderStatusEnum.LOADHOLD.equals(t.getTshitst()))
                        && !(t.getTshitst() != null && OrderStatusEnum.UNLOAD.equals(t.getTshitst()))
                        && !(t.getTshitst() != null && OrderStatusEnum.DLVCMP.equals(t.getTshitst()))
                        && !(t.getTshitst() != null && OrderStatusEnum.STGREQU.equals(t.getTshitst()))
                        && !(t.getTshitst() != null && OrderStatusEnum.STGAPPR.equals(t.getTshitst()))
                        && !(t.getTshitst() != null && OrderStatusEnum.STGREJE.equals(t.getTshitst()))
                        && !(t.getTshitst() != null && OrderStatusEnum.STGSELF.equals(t.getTshitst()))
                        && !(t.getTshitst() != null && OrderStatusEnum.SHPSELF.equals(t.getTshitst()))
                        && !(t.getTshitst() != null && OrderStatusEnum.NEW.equals(t.getTshitst()))
                // && !(t.getTshitst() != null && OrderStatusEnum.SHIP.equals(t.getTshitst()))
                )
                .collect(Collectors.toList());

        if (unTurgtynList == null || unTurgtynList.size() < 1) {
            log.info("Is Changed3ShipmentPlan, unTurgtynList is Empty");
            return shipChangedInfo;
        }
        // log.info("Is Changed3ShipmentPlan, unTurgtynList Size: {}",
        // unTurgtynList.size());

        // Get oetmsky List in unTurgtynList
        List<String> unTOetmskyList = unTurgtynList.stream()
                .map(OetmhdSimpleDTO::getOetmsky)
                .collect(Collectors.toList());
        log.info("Is Changed3ShipmentPlan, unTOetmskyList size: {}", unTOetmskyList.size());

        // [1] ShipmentPlan 의 oetmsky 에서 변경된 부분이 있는지 확인
        // ShipmentPlan 의 oetmsky 와 매칭되는 리스트 생성
        ShipmentPlan onePlan = shipmentPlanRepository.findByShpplkyAndDeletatIsNull(shPlan.getShpplky());
        if (onePlan == null) {
            log.info("Is Changed3ShipmentPlan, Empty Plan. {}", shPlan.getShpplky());
            return shipChangedInfo;
        }

        List<String> orgListInPlan = new ArrayList<>();
        List<String> changedListInPlan = new ArrayList<>();

        // Get oetmsky List in ShipmentPlan
        /*
         * for ( Shipment oneShmt : onePlan.getShipmentList() ) {
         * if ( oneShmt.getDeletat() != null ) continue;
         * for ( ShipmentSection oneShsc : oneShmt.getShipmentSectionList() ) {
         * if ( oneShsc.getDeletat() != null ) continue;
         * orgListInPlan.add(oneShsc.getOetmsky());
         * }
         * }
         */

        for (Shipment oneShmt : onePlan.getShipmentList()) {
            for (ShipmentSection oneShsc : oneShmt.getShipmentSectionList()) {
                orgListInPlan.add(oneShsc.getOetmsky());
            }
        }

        orgListInPlan = ShipmentUtils.distinctStringList(orgListInPlan); // 중복제거
        log.info("Is Changed3ShipmentPlan, oetmsky in Plan size: {}", orgListInPlan.size());
        log.info("Is Changed3ShipmentPlan, oetmsky in Plan: {}", orgListInPlan);

        // 상세변경비교 oetmsky List in ShipmentPlan
        for (Shipment oneShmt : onePlan.getShipmentList()) {
            if (oneShmt.getDeletat() != null)
                continue;
            TruckLoadTypeEnum cargoty = oneShmt.getCargoty();
            for (ShipmentSection oneShsc : oneShmt.getShipmentSectionList()) {
                if (oneShsc.getDeletat() != null)
                    continue;

                // 상세 비교
                for (OetmhdSimpleDTO un : unTurgtynList) {
                    if (!Objects.equals(un.getOetmsky(), oneShsc.getOetmsky()))
                        continue;

                    // 운송상태 (SHIP CHECK)
                    if (OrderStatusEnum.SHIP.equals(un.getTshitst())
                            && OrderStatusEnum.REQUEST.equals(oneShsc.getTshitst())) {
                        // 운송확정으로 REQUEST -> SHIP 된 것은 제외
                    } else if (!Objects.equals(un.getTshitst(), oneShsc.getTshitst())) {
                        // 운송상태
                        log.info("Is Changed3ShipmentPlan, Change Tshitst: {}/{} ({})", un.getTshitst(),
                                oneShsc.getTshitst(), oneShsc.getOetmsky());
                        changedListInPlan.add(oneShsc.getOetmsky());
                        break;
                    }
                    // 상하차일시
                    if (VisitTypeEnum.PICKUP.equals(oneShsc.getVsttype())) {
                        // 상차일시
                        if (!Objects.equals(un.getLodrqdt(), oneShsc.getLudrqdt())) {
                            log.info("Is Changed3ShipmentPlan, Change Lodrqdt: {}/{} ({})", un.getLodrqdt(),
                                    oneShsc.getLudrqdt(), oneShsc.getOetmsky());
                            changedListInPlan.add(oneShsc.getOetmsky());
                            break;
                        }
                        if (!Objects.equals(un.getLodrqtm(), oneShsc.getLudrqtm())) {
                            log.info("Is Changed3ShipmentPlan, Change Lodrqtm: {}/{} ({})", un.getLodrqtm(),
                                    oneShsc.getLudrqtm(), oneShsc.getOetmsky());
                            changedListInPlan.add(oneShsc.getOetmsky());
                            break;
                        }
                    } else if (VisitTypeEnum.DROPOFF.equals(oneShsc.getVsttype())) {
                        // 하차일시
                        if (!Objects.equals(un.getUldrqdt(), oneShsc.getLudrqdt())) {
                            log.info("Is Changed3ShipmentPlan, Change Uldrqdt: {}/{} ({})", un.getUldrqdt(),
                                    oneShsc.getLudrqdt(), oneShsc.getOetmsky());
                            changedListInPlan.add(oneShsc.getOetmsky());
                            break;
                        }
                        if (!Objects.equals(un.getUldrqtm(), oneShsc.getLudrqtm())) {
                            log.info("Is Changed3ShipmentPlan, Change Uldrqtm: {}/{} ({})", un.getUldrqtm(),
                                    oneShsc.getLudrqtm(), oneShsc.getOetmsky());
                            changedListInPlan.add(oneShsc.getOetmsky());
                            break;
                        }
                    }
                    // 포장,박스 등
                    {
                        // 포장형태
                        if (!Objects.equals(un.getOpkcate(), oneShsc.getOpkcate())) {
                            log.info("Is Changed3ShipmentPlan, Change Opkcate: {}/{} ({})", un.getOpkcate(),
                                    oneShsc.getOpkcate(), oneShsc.getOetmsky());
                            changedListInPlan.add(oneShsc.getOetmsky());
                            break;
                        }
                        // 포장타입
                        if (!Objects.equals(un.getOpktype(), oneShsc.getOpktype())) {
                            log.info("Is Changed3ShipmentPlan, Change Opktype: {}/{} ({})", un.getOpktype(),
                                    oneShsc.getOpktype(), oneShsc.getOetmsky());
                            changedListInPlan.add(oneShsc.getOetmsky());
                            break;
                        }
                        // 포장ONE-SIDE 타입
                        if (!Objects.equals(un.getOnsidty(), oneShsc.getOnsidty())) {
                            log.info("Is Changed3ShipmentPlan, Change Onsidty: {}/{} ({})", un.getOnsidty(),
                                    oneShsc.getOnsidty(), oneShsc.getOetmsky());
                            changedListInPlan.add(oneShsc.getOetmsky());
                            break;
                        }
                        // 팔레트 박스 수량
                        if (!Objects.equals(un.getOpakqty(), oneShsc.getOpakqty())) {
                            log.info("Is Changed3ShipmentPlan, Change Opakqty: {}/{} ({})", un.getOpakqty(),
                                    oneShsc.getOpakqty(), oneShsc.getOetmsky());
                            changedListInPlan.add(oneShsc.getOetmsky());
                            break;
                        }
                        // 가로,세로,높이,중량
                        if (!Objects.equals(un.getPakwidh(), oneShsc.getPakwidh())) {
                            log.info("Is Changed3ShipmentPlan, Change Pakwidh: {}/{} ({})", un.getPakwidh(),
                                    oneShsc.getPakwidh(), oneShsc.getOetmsky());
                            changedListInPlan.add(oneShsc.getOetmsky());
                            break;
                        }
                        if (!Objects.equals(un.getPakdept(), oneShsc.getPakdept())) {
                            log.info("Is Changed3ShipmentPlan, Change Pakdept: {}/{} ({})", un.getPakdept(),
                                    oneShsc.getPakdept(), oneShsc.getOetmsky());
                            changedListInPlan.add(oneShsc.getOetmsky());
                            break;
                        }
                        if (!Objects.equals(un.getPakheig(), oneShsc.getPakheig())) {
                            log.info("Is Changed3ShipmentPlan, Change Pakheig: {}/{} ({})", un.getPakheig(),
                                    oneShsc.getPakheig(), oneShsc.getOetmsky());
                            changedListInPlan.add(oneShsc.getOetmsky());
                            break;
                        }
                        if (!Objects.equals(un.getPkrweig(), oneShsc.getPkrweig())) {
                            log.info("Is Changed3ShipmentPlan, Change Pkrweig: {}/{} ({})", un.getPkrweig(),
                                    oneShsc.getPkrweig(), oneShsc.getOetmsky());
                            changedListInPlan.add(oneShsc.getOetmsky());
                            break;
                        }
                    }
                    // 운송화물방식 (단,혼적)
                    if (!Objects.equals(un.getCargoty(), cargoty)) {
                        log.info("Is Changed3ShipmentPlan, Change cargoty: {}/{} ({})", un.getCargoty(), cargoty,
                                oneShsc.getOetmsky());
                        changedListInPlan.add(oneShsc.getOetmsky());
                        break;
                    }
                } // ~ for ( OetmhdSimpleDTO un : unTurgtynList )
            } // ~ for ( ShipmentSection oneShsc : oneShmt.getShipmentSectionList() )
        } // ~ for ( Shipment oneShmt : onePlan.getShipmentList() )

        if (changedListInPlan.size() > 0) {
            changedListInPlan = ShipmentUtils.distinctStringList(changedListInPlan); // 중복제거
            log.info("Is Changed3ShipmentPlan, 변경된 oetmsky: {}", changedListInPlan);
            shipChangedInfo.setIsChanged(true);
            shipChangedInfo.setShpmtdt(shPlan.getShpmtdt());
            shipChangedInfo.setRoundno(shPlan.getRoundno());

            return shipChangedInfo;
        }

        // [2] ShipmentPlan 에 없는 oetmsky 에서 동일 일,회차 변경된 부분이 있는지 확인
        // plan 에 포함되지 않는 리스트
        final List<String> fOrgListInPlan = orgListInPlan; // lambda expression error 때문에 final 지정
        List<String> notInPlanList = unTOetmskyList.stream()
                .filter(element -> !fOrgListInPlan.contains(element))
                .collect(Collectors.toList());
        log.info("Is Changed3ShipmentPlan, 추가된 oetmsky : {}", notInPlanList);
        if (notInPlanList == null)
            notInPlanList = new ArrayList<>(); // NULL Exception 방지

        // 해당 일,회차가 아닌 리스트 제거
        final List<String> fNotInPlanList = notInPlanList; // contains 를 사용하기 위해서 final 지정
        unTurgtynList.removeIf(one -> {
            if (!fNotInPlanList.contains(one.getOetmsky()))
                return true; // item 제거

            LocalDate shmtDate = one.getUldrqdt();
            Integer roundno = ShipmentUtils.getRoundPuDf(one.getLodrqdt(), one.getLodrqtm(), one.getUldrqdt(),
                    one.getUldrqtm());

            if (shmtDate.equals(shPlan.getShpmtdt()) && roundno.equals(shPlan.getRoundno())) {
                log.info("Is Changed3ShipmentPlan, roundno:{} / PU.{}, DF.{} {} / {} . ({})", roundno, one.getLodrqdt(),
                        one.getUldrqdt(), one.getUldrqtm(), one.getTshitst(), one.getOetmsky());

                if (OrderStatusEnum.CANCEL.equals(one.getTshitst())) {
                    log.info("Is Changed3ShipmentPlan, getTshitst : {}", one.getTshitst());
                    return true;
                }

                return false; // item 제거하지 않음
            }

            return true; // item 제거
        });
        // for DEBUG
        List<String> nextUntOetmskyList = unTurgtynList.stream()
                .map(OetmhdSimpleDTO::getOetmsky)
                .collect(Collectors.toList());
        log.info("Is Changed3ShipmentPlan, 동일 일,회차에 추가된 OetmskyList: {}", nextUntOetmskyList);
        // ~ for DEBUG

        if (unTurgtynList != null && unTurgtynList.size() > 0) {
            shipChangedInfo.setIsChanged(true);
        }

        shipChangedInfo.setShpmtdt(shPlan.getShpmtdt());
        shipChangedInfo.setRoundno(shPlan.getRoundno());

        return shipChangedInfo;
    }

    public Boolean deletePlanDispatch(final AuthDTO authDTO, final String shpplky, Boolean isCheck) {
        final String compkey = Objects.requireNonNull(authDTO).getCompany();
        try {
            // shipment list 찾기
            List<Shipment> shmtList = qShipmentRepository.findShipmentListByShipmentPlan(compkey, shpplky);
            if (shmtList == null || shmtList.size() < 1) {
                log.info("delete PlanDispatch, Empty ShmtList.{}", shpplky);
                return true;
            }

            List<String> oldShmtList = shmtList.stream()
                    .filter(t -> t.getDeletat() == null)
                    .map(Shipment::getShpmtky)
                    .collect(Collectors.toList());

            log.info("delete PlanDispatch, Cancel CheckDispatch ShmtList.{}", oldShmtList);

            Boolean bRet = dispatchPlanService.cancelDispatch(oldShmtList, isCheck);
            if (bRet == false) {
                log.info("delete PlanDispatch, 배차완료된 운송계획은 삭제할 수 없습니다.");
                // 배차확정이 아니더라도 배차에 리스트가 없으면 false 결과
                return false;
            } else {
                log.info("delete PlanDispatch, clear Dispatch. {}", oldShmtList);
                return true;
            }

        } catch (Exception ex) {
            log.info("Exception : {}", ex.toString());
        }
        return false;
    }

    public Boolean isIncludeShipmentOetmsky(final AuthDTO authDTO, final String shpplky, final String oetmsky) {
        try {
            log.info("is IncludeShipmentOetmsky, oetmsky. {} / shpplky. {}", oetmsky, shpplky);
            ShipmentPlan shpl = shipmentPlanRepository.findByShpplkyAndDeletatIsNull(shpplky);
            if (shpl == null)
                return false;
            for (Shipment shmt : shpl.getShipmentList()) {
                if (shmt.getDeletat() != null)
                    continue;
                for (ShipmentSection shsc : shmt.getShipmentSectionList()) {
                    if (shsc == null)
                        continue;
                    if (shsc.getDeletat() != null)
                        continue;
                    if (oetmsky.equals(shsc.getOetmsky()))
                        return true;
                }
            }
        } catch (Exception ex) {
        }
        return false;
    }

    public Boolean isAvailableDeletePlan(final AuthDTO authDTO, final List<String> oldShmtList) {
        try {
            log.info("is AvailableDeletePlan, Cancel CheckDispatch ShmtList.{}", oldShmtList);
            List<String> remainShmtList = dispatchPlanService.cancelCheckDispatch(oldShmtList);
            if (remainShmtList == null || remainShmtList.size() < 1) {
                log.info("is AvailableDeletePlan, Empty Cancel CheckDispatch ShmtList");
                return true;
            } else {
                log.info("is AvailableDeletePlan, Exist Cancel CheckDispatch. {}/ {}", remainShmtList.size(),
                        remainShmtList.get(0));
                return false;
            }

        } catch (Exception ex) {
            log.info("Exception : {}", ex.toString());
        }
        return false;
    }

    public Boolean isAvailableDeletePlan(final AuthDTO authDTO, final String shpplky) {
        final String compkey = Objects.requireNonNull(authDTO).getCompany();
        try {
            log.info("is AvailableDeletePlan, ShipmentPlan. {}", shpplky);

            // shipment list 찾기
            List<Shipment> shmtList = qShipmentRepository.findShipmentListByShipmentPlan(compkey, shpplky);
            if (shmtList == null || shmtList.size() < 1) {
                log.info("is AvailableDeletePlan, Empty ShmtList.{}", shpplky);
                return true;
            }

            List<String> oldShmtList = shmtList.stream()
                    .filter(t -> t.getDeletat() == null)
                    .map(Shipment::getShpmtky)
                    .collect(Collectors.toList());

            log.info("is AvailableDeletePlan, Cancel CheckDispatch ShmtList.{}", oldShmtList);
            List<String> remainShmtList = dispatchPlanService.cancelCheckDispatch(oldShmtList);
            if (remainShmtList == null || remainShmtList.size() < 1) {
                log.info("is AvailableDeletePlan, Empty Cancel CheckDispatch ShmtList");
                return true;
            } else {
                log.info("is AvailableDeletePlan, Exist Cancel CheckDispatch. {}/ {}", remainShmtList.size(),
                        remainShmtList.get(0));
                return false;
            }

        } catch (Exception ex) {
            log.info("Exception : {}", ex.toString());
        }
        return false;
    }

    public Boolean isAvailableDeletePlan(final AuthDTO authDTO, final LocalDate uldrqdt, Integer roundno) {
        try {
            final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
            final String compkey = Objects.requireNonNull(authDTO).getCompany();

            List<ShipmentPlanCheckMadeDTO> results = qShipmentPlanRepository.findShipmentPlanByDateRound(ptnrkey,
                    compkey, uldrqdt, roundno);

            if (results == null || results.size() < 1) {
                log.info("is AvailableDeletePlan, Empty ShipmentPlan. {} / {}", uldrqdt, roundno);
                return false;
            }
            return isAvailableDeletePlan(authDTO, results.get(0).getShpplky());
        } catch (Exception ex) {
            log.info("Exception : {}", ex.toString());
        }
        return false;
    }

    public List<String> deleteChangedShipmentPlan(final AuthDTO authDTO,
            @NotBlank final String compkey,
            ShipmentPlanChangedDTO plan) {

        final LocalDate today = LocalDate.now();
        List<String> oetmskyValues = new ArrayList<>();
        if (plan == null || plan.getChangedList() == null || plan.getChangedList().size() < 1) {
            return oetmskyValues;
        }

        List<ShipmentChangedListDTO> changedList = plan.getChangedList();
        log.info("delete ChangedShipmentPlan, List size : {}", changedList.size());
        for (ShipmentChangedListDTO chng : changedList) {
            long daysDifference = ChronoUnit.DAYS.between(today, chng.getShpmtdt());
            log.info("delete ChangedShipmentPlan, today : {} / changed Shpmtdt : {} / Diff : {}", today,
                    chng.getShpmtdt(), daysDifference);
            // if ( daysDifference >= 2 || daysDifference < -1 ) // TODO. 조건 활성화
            {
                // cancelDispatch 체크 후 삭제
                String _shpplky = chng.getShpplky();
                if (this.isAvailableDeletePlan(authDTO, _shpplky)) {

                    // 배차 테이블 먼저 삭제
                    if (this.deletePlanDispatch(authDTO, _shpplky, false) == false) {
                        log.info("delete ChangedShipmentPlan, 배차삭제 실패.{}", _shpplky);
                    }

                    // Delete Previous ShipmentPlan
                    log.info("delete ChangedShipmentPlan, No : {}", _shpplky);
                    List<String> olist = this.deleteShipmentPlanOne(compkey, _shpplky);
                    if (olist != null) {
                        oetmskyValues.addAll(olist);
                    }

                }
            }
        }

        log.info("delete ChangedShipmentPlan, oetmskyValues : {}", oetmskyValues);

        // 중복제거
        if (oetmskyValues != null && oetmskyValues.size() > 0) {
            List<String> uniqueValues = oetmskyValues.stream().distinct().collect(Collectors.toList());
            log.info("delete ChangedShipmentPlan, uniqueValues : {}", uniqueValues);
            return uniqueValues;
        }

        return null;
    }

    public AvailableDeletePlanDTO checkAvailableDeleteShipmentPlanByDate(final AuthDTO authDTO,
            final ShipmentPlan2DateTimesDTO inDto) {

        AvailableDeletePlanDTO result = new AvailableDeletePlanDTO();
        final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
        final String compkey = Objects.requireNonNull(authDTO).getCompany();
        Integer roundno = 0;
        List<ShipmentPlanCheckMadeDTO> planList = null;
        Boolean isPastDropoffDate = false;

        try {
            // 하차일자가 지난건인지 체크 (2025.02.04)
            if (ShipmentUtils.isPastDate(inDto.getOldDfDate())) {
                log.info("check DeleteShipmentPlan, Oetmsky: {} / Past Dropoff Date ({})", inDto.getOetmsky(),
                        inDto.getOldDfDate());
                isPastDropoffDate = true;
            }

            // 상차불가, 긴급건에 대해서 처리 않음 (2025.01.16)
            List<OetmhdSimpleDTO> oetmList = qShipmentRepository.findOetmhdSimpleByOetmskyList(ptnrkey, compkey,
                    List.of(inDto.getOetmsky()));
            if (oetmList != null && oetmList.size() > 0) {
                OetmhdSimpleDTO t = oetmList.get(0);
                log.info("check DeleteShipmentPlan, Oetmsky: {} / Turgtyn: {}/ Tshitst: {}", t.getOetmsky(),
                        t.getTurgtyn(), t.getTshitst());
                if (t.getTurgtyn() == true) {
                    log.info("check DeleteShipmentPlan, Oetmsky: {} / Equals Turgtyn: true");
                    result.setAvailableDelete(true);
                    return result;
                }
                if (OrderStatusEnum.LOADHOLD.equals(t.getTshitst())) {
                    log.info("check DeleteShipmentPlan, Oetmsky: {} / Equals Turgtyn: LOADHOLD");
                    result.setAvailableDelete(true);
                    return result;
                }
            }

            List<PickupDropoffDateAndTime> dateTimePairs = null;

            Boolean isIncOldShipment = false;
            // check Plan by Date , Time
            roundno = ShipmentUtils.getRoundPuDf(inDto.getOldPuDate(), inDto.getOldPuTime(), inDto.getOldDfDate(),
                    inDto.getOldDfTime());
            log.info("check DeleteShipmentPlan, Old Get Round : {} {} / {} {} /{}", inDto.getOldPuDate(),
                    inDto.getOldPuTime(), inDto.getOldDfDate(), inDto.getOldDfTime(), roundno);
            planList = qShipmentPlanRepository.findShipmentPlanByDateRound(ptnrkey, compkey, inDto.getOldDfDate(),
                    roundno);
            if (planList != null && planList.size() > 0) {
                for (ShipmentPlanCheckMadeDTO plan : planList) {
                    // 유효한 Shipment 에 포함된 oetmsky 인지 체크
                    if (this.isIncludeShipmentOetmsky(authDTO, plan.getShpplky(), inDto.getOetmsky())) {
                        log.info("check DeleteShipmentPlan, is Include Shipment, Plan. {}", plan.getShpplky());
                        isIncOldShipment = true;
                        break;
                    }
                }
            }
            // 하차일자가 지난건에 대해서는 OldDate 체크안함 (2025.02.04)
            if (isPastDropoffDate) {
                isIncOldShipment = false;
            }
            if (isIncOldShipment) {
                // Old date 가 유효한 shipment 에 포함되어 있으면 old,new date 모두 체크
                dateTimePairs = List.of(
                        new PickupDropoffDateAndTime(inDto.getOldPuDate(), inDto.getOldPuTime(), inDto.getOldDfDate(),
                                inDto.getOldDfTime()),
                        new PickupDropoffDateAndTime(inDto.getNewPuDate(), inDto.getNewPuTime(), inDto.getNewDfDate(),
                                inDto.getNewDfTime()));
            } else {
                log.info("check DeleteShipmentPlan, Oetmsky: {} Old Date not include valid shipment",
                        inDto.getOetmsky());
                // Old date 가 유효한 shipment 에 포함되어 있지 않으면 new date 만 체크
                dateTimePairs = List.of(
                        new PickupDropoffDateAndTime(inDto.getNewPuDate(), inDto.getNewPuTime(), inDto.getNewDfDate(),
                                inDto.getNewDfTime()));
            }

            for (PickupDropoffDateAndTime dtOne : dateTimePairs) {
                // check Plan by Date , Time
                roundno = ShipmentUtils.getRoundPuDf(dtOne.getPuDate(), dtOne.getPuTime(), dtOne.getDfDate(),
                        dtOne.getDfTime());
                log.info("check DeleteShipmentPlan, Get Round : {} {} / {} {} /{}", dtOne.getPuDate(),
                        dtOne.getPuTime(), dtOne.getDfDate(), dtOne.getDfTime(), roundno);
                planList = qShipmentPlanRepository.findShipmentPlanByDateRound(ptnrkey, compkey, dtOne.getDfDate(),
                        roundno);
                if (planList == null || planList.size() < 1) {
                    log.info("check DeleteShipmentPlan, Empty Plan");
                } else {
                    for (ShipmentPlanCheckMadeDTO plan : planList) {
                        // cancelDispatch 체크
                        if (this.isAvailableDeletePlan(authDTO, plan.getShpplky()) == false) {
                            log.info("check DeleteShipmentPlan, Cannot delete Plan. {}", plan.getShpplky());
                            result.setAvailableDelete(false);
                            result.setFixedDate(plan.getShpmtdt());
                            result.setFixedRound(plan.getRoundno());
                            result.setShpplky(plan.getShpplky());
                            return result;
                        } else {
                            log.info("check DeleteShipmentPlan, available delete Plan. {}", plan.getShpplky());
                        }
                    }
                }
                log.info("check DeleteShipmentPlan, Available delete DateRound. {} / {}", dtOne.getDfDate(), roundno);
            }
        } catch (Exception ex) {
            log.info("Exception : {}", ex.toString());
        }

        result.setAvailableDelete(true);
        return result;
    }

    @Transactional
    public Integer deleteShipmentPlanAndDispatchByDate(final AuthDTO authDTO,
            final LocalDate reqDate, final Integer roundno) {

        Integer result = 0;
        final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
        final String compkey = Objects.requireNonNull(authDTO).getCompany();

        try {
            log.info("delete ShipmentPlanAndDispatch, Get Round : {} {}", reqDate, roundno);
            List<ShipmentPlanCheckMadeDTO> planList = qShipmentPlanRepository.findShipmentPlanByDateRound(ptnrkey,
                    compkey, reqDate, roundno);
            if (planList == null || planList.size() < 1) {
                log.info("delete ShipmentPlanAndDispatch, Empty Plan");
                return 0;
            } else {
                for (ShipmentPlanCheckMadeDTO plan : planList) {
                    final String shpplky = plan.getShpplky();
                    // shipment list 찾기
                    List<String> shmtList = null;
                    List<Shipment> _tmp_shmt_list = qShipmentRepository.findShipmentListByShipmentPlan(compkey,
                            shpplky);
                    if (_tmp_shmt_list != null && _tmp_shmt_list.size() > 0) {
                        shmtList = _tmp_shmt_list.stream()
                                .filter(t -> t.getDeletat() == null)
                                .map(Shipment::getShpmtky)
                                .collect(Collectors.toList());
                    } else {
                        log.info("delete PlanDispatch, Empty ShmtList. {}", shpplky);
                    }
                    log.info("delete PlanDispatch, Old ShmtList.{}", shmtList);

                    // ShipmentPlan 삭제
                    ShipmentPlanDeletedInfoDTO deletedInfo = this.deleteShipmentPlanByKeyExceptFixedShipmnet(shpplky);

                    /*
                     * // Dispatch 의 ShipmentNo list 삭제
                     * if ( shmtList != null && !shmtList.isEmpty()) {
                     * Boolean bRet = dispatchPlanService.cancelDispatch(shmtList, false);
                     * if ( bRet == false ) {
                     * log.info("delete ShipmentPlanAndDispatch, return false cancelDispatch.");
                     * // 배차확정이 아니더라도 배차 shmt 리스트가 없으면 false 결과
                     * } else {
                     * log.info("delete ShipmentPlanAndDispatch, clear Dispatch. {}", shmtList);
                     * }
                     * }
                     */

                    // ShipmentPlan 의 oetmsky list 삭제
                    if (deletedInfo.getOetmskyValues() == null || deletedInfo.getOetmskyValues().size() < 1) {
                        log.info("delete ShipmentPlanAndDispatch, Empty OetmskyValues Plan. {}", shpplky);
                    } else {
                        log.info("delete ShipmentPlanAndDispatch, clear oetmsky List Plan. {}", shpplky);
                        this.clearOetmhdTmshpno(deletedInfo.getOetmskyValues());
                    }
                    result += 1;
                }
            }
        } catch (Exception ex) {
            log.info("Exception : {}", ex.toString());
        }

        return result;
    }

    @Transactional
    public AvailableDeletePlanDTO deleteDoubleShipmentPlanByDate(final AuthDTO authDTO,
            final ShipmentPlan2DateTimesDTO inDto) {

        AvailableDeletePlanDTO result = new AvailableDeletePlanDTO();
        final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
        final String compkey = Objects.requireNonNull(authDTO).getCompany();
        Integer roundno = 0;
        Integer deletedCount = 0;
        Boolean isPastDropoffDate = false;

        try {
            // 하차일자가 지난건인지 체크 (2025.02.04)
            if (ShipmentUtils.isPastDate(inDto.getOldDfDate())) {
                log.info("delete DoubleShipmentPlan, Oetmsky: {} / Past Dropoff Date ({})", inDto.getOetmsky(),
                        inDto.getOldDfDate());
                isPastDropoffDate = true;
            }

            // 상차불가, 긴급건에 대해서 처리 않음 (2025.01.16)
            List<OetmhdSimpleDTO> oetmList = qShipmentRepository.findOetmhdSimpleByOetmskyList(ptnrkey, compkey,
                    List.of(inDto.getOetmsky()));
            if (oetmList != null && oetmList.size() > 0) {
                OetmhdSimpleDTO t = oetmList.get(0);
                log.info("delete DoubleShipmentPlan, Oetmsky: {} / Turgtyn: {}/ Tshitst: {}", t.getOetmsky(),
                        t.getTurgtyn(), t.getTshitst());
                if (t.getTurgtyn() == true) {
                    log.info("delete DoubleShipmentPlan, Oetmsky: {} / Equals Turgtyn: true");
                    result.setAvailableDelete(true);
                    return result;
                }
                if (OrderStatusEnum.LOADHOLD.equals(t.getTshitst())) {
                    log.info("delete DoubleShipmentPlan, Oetmsky: {} / Equals Turgtyn: LOADHOLD");
                    result.setAvailableDelete(true);
                    return result;
                }
            }

            List<PickupDropoffDateAndTime> dateTimePairs = null;

            List<ShipmentPlanCheckMadeDTO> planList = null;
            Boolean isIncOldShipment = false;
            // check Plan by Date , Time
            roundno = ShipmentUtils.getRoundPuDf(inDto.getOldPuDate(), inDto.getOldPuTime(), inDto.getOldDfDate(),
                    inDto.getOldDfTime());
            log.info("delete DoubleShipmentPlan, Old Get Round : {} {} / {} {} /{}", inDto.getOldPuDate(),
                    inDto.getOldPuTime(), inDto.getOldDfDate(), inDto.getOldDfTime(), roundno);
            planList = qShipmentPlanRepository.findShipmentPlanByDateRound(ptnrkey, compkey, inDto.getOldDfDate(),
                    roundno);
            if (planList != null && planList.size() > 0) {
                for (ShipmentPlanCheckMadeDTO plan : planList) {
                    // 유효한 Shipment 에 포함된 oetmsky 인지 체크
                    if (this.isIncludeShipmentOetmsky(authDTO, plan.getShpplky(), inDto.getOetmsky())) {
                        log.info("delete DoubleShipmentPlan, is Include Shipment, Plan. {}", plan.getShpplky());
                        isIncOldShipment = true;
                        break;
                    }
                }
            }
            // 하차일자가 지난건에 대해서는 OldDate 체크안함 (2025.02.04)
            if (isPastDropoffDate) {
                isIncOldShipment = false;
            }
            if (isIncOldShipment) {
                // Old date 가 유효한 shipment 에 포함되어 있으면 old,new date 모두 체크
                dateTimePairs = List.of(
                        new PickupDropoffDateAndTime(inDto.getOldPuDate(), inDto.getOldPuTime(), inDto.getOldDfDate(),
                                inDto.getOldDfTime()),
                        new PickupDropoffDateAndTime(inDto.getNewPuDate(), inDto.getNewPuTime(), inDto.getNewDfDate(),
                                inDto.getNewDfTime()));
            } else {
                log.info("delete DoubleShipmentPlan, Oetmsky: {} Old Date not include valid shipment",
                        inDto.getOetmsky());
                // Old date 가 유효한 shipment 에 포함되어 있지 않으면 new date 만 체크
                dateTimePairs = List.of(
                        new PickupDropoffDateAndTime(inDto.getNewPuDate(), inDto.getNewPuTime(), inDto.getNewDfDate(),
                                inDto.getNewDfTime()));
            }

            for (PickupDropoffDateAndTime dtOne : dateTimePairs) {
                // check Plan by Date , Time
                roundno = ShipmentUtils.getRoundPuDf(dtOne.getPuDate(), dtOne.getPuTime(), dtOne.getDfDate(),
                        dtOne.getDfTime());
                log.info("delete DoubleShipmentPlan, Get Round : {} {} / {} {} / {}", dtOne.getPuDate(),
                        dtOne.getPuTime(), dtOne.getDfDate(), dtOne.getDfTime(), roundno);
                Integer count = this.deleteShipmentPlanAndDispatchByDate(authDTO, dtOne.getDfDate(), roundno);
                log.info("delete DoubleShipmentPlan, deleteCount ({}) DateRound. {} / {}", count, dtOne.getDfDate(),
                        roundno);
                deletedCount += count;
            }
        } catch (Exception ex) {
            log.info("Exception : {}", ex.toString());
        }

        result.setDeletedPlanCount(deletedCount);
        return result;
    }

    public ShipmentPlanChangedDTO getChangedShipmentPlan(final AuthDTO authDTO,
            @NotBlank final String compkey,
            @Nullable final LocalDate uldrqdt,
            final Integer roundno) {

        final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();

        LocalDate xDay = LocalDate.now();
        if (uldrqdt != null) {
            xDay = uldrqdt;
        }
        final LocalDate today = xDay;
        // final LocalDate endday = today.plusDays(5);
        final LocalDate endday = today;

        final String authPtnrkey = Objects.requireNonNull(authDTO).getPartnerKey();

        ShipmentPlanChangedDTO changedShipPlan = new ShipmentPlanChangedDTO();

        changedShipPlan.setIsChanged(false);

        // Dummy List
        List<ShipmentPlanChangedDummyDTO> dummyList = null;
        List<ShipmentPlanChangedSimpleDTO> dummyNewList = null;

        List<ShipmentPlanCheckMadeDTO> splMade = qShipmentPlanRepository.findShipmentPlanByDateRound(authPtnrkey,
                compkey, uldrqdt, roundno);

        if (splMade == null || splMade.size() < 1) {
            log.info("해당일 ({} / {}) 의 운송계획이 없음", today, roundno);
            return changedShipPlan;
        }

        List<ShipmentPlanChangedDummyDTO> dummyBasic1List = qShipmentPlanRepository.findChangedShipmentPlan(authPtnrkey,
                compkey, today, endday);

        // TSHITST상태가 REQUEST->SHIP 된 것은 제외
        if (dummyBasic1List != null && dummyBasic1List.size() > 0) {
            dummyList = dummyBasic1List.stream()
                    .filter(t -> !(t.getTshitstn() != null && OrderStatusEnum.SHIP.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.PLANCMP.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.LOAD.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.LOADHOLD.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.UNLOAD.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.DLVCMP.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.STGREQU.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.STGAPPR.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.STGREJE.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.STGSELF.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.SHPSELF.equals(t.getTshitstn()))
                            && !(t.getTshitstn() != null && OrderStatusEnum.NEW.equals(t.getTshitstn())))
                    .collect(Collectors.toList());
        } else {
            log.info("Changed ShipmentPlan, 이전 운송주문에서 변경은 없음");

            List<ShipmentPlanChangedSimpleDTO> changedBasicList = qShipmentPlanRepository
                    .findSimpleChanged2ShipmentPlan(authPtnrkey, compkey, today, endday, uldrqdt, roundno);

            // TSHITST상태가 REQUEST->SHIP 된 것은 제외
            if (changedBasicList != null && changedBasicList.size() > 0) {
                dummyNewList = changedBasicList.stream()
                        .filter(t -> !(t.getTshitstn() != null && OrderStatusEnum.SHIP.equals(t.getTshitstn()))
                                && !(t.getTshitstn() != null && OrderStatusEnum.PLANCMP.equals(t.getTshitstn()))
                                && !(t.getTshitstn() != null && OrderStatusEnum.LOAD.equals(t.getTshitstn()))
                                && !(t.getTshitstn() != null && OrderStatusEnum.LOADHOLD.equals(t.getTshitstn()))
                                && !(t.getTshitstn() != null && OrderStatusEnum.UNLOAD.equals(t.getTshitstn()))
                                && !(t.getTshitstn() != null && OrderStatusEnum.DLVCMP.equals(t.getTshitstn()))
                                && !(t.getTshitstn() != null && OrderStatusEnum.STGREQU.equals(t.getTshitstn()))
                                && !(t.getTshitstn() != null && OrderStatusEnum.STGAPPR.equals(t.getTshitstn()))
                                && !(t.getTshitstn() != null && OrderStatusEnum.STGREJE.equals(t.getTshitstn()))
                                && !(t.getTshitstn() != null && OrderStatusEnum.STGSELF.equals(t.getTshitstn()))
                                && !(t.getTshitstn() != null && OrderStatusEnum.SHPSELF.equals(t.getTshitstn()))
                                && !(t.getTshitstn() != null && OrderStatusEnum.NEW.equals(t.getTshitstn())))
                        .collect(Collectors.toList());
            }

            if (dummyNewList != null && dummyNewList.size() > 0) {
                // oetmsky 중복된 데이타는 제외
                Set<String> seenOetmsky = new HashSet<>();
                dummyNewList = dummyNewList.stream()
                        .filter(t -> seenOetmsky.add(t.getOetmsky()))
                        .collect(Collectors.toList());

                // oetmsky List
                List<String> oetmskyList = dummyNewList.stream()
                        .map(ShipmentPlanChangedSimpleDTO::getOetmsky)
                        .collect(Collectors.toList());

                // oetmsky 중복제거 List
                oetmskyList = ShipmentUtils.distinctStringList(oetmskyList);
                log.info("Changed ShipmentPlan, oetmskyList:{}", oetmskyList);
                List<OetmhdSimpleDTO> oetmhdList = qShipmentRepository.findOetmhdSimpleByOetmskyList(ptnrkey, compkey,
                        oetmskyList);
                // 긴급운송 제외
                List<OetmhdSimpleDTO> filteredOetmhdList = oetmhdList.stream()
                        .filter(t -> t.getTurgtyn().equals(false))
                        .collect(Collectors.toList());

                if (filteredOetmhdList == null || filteredOetmhdList.size() < 1) {
                    // 긴급 제외 후 zero 이면
                    log.info("Changed ShipmentPlan, 긴급 제외 후 zero");
                    dummyNewList = null;
                } else {
                    log.info("Changed ShipmentPlan, filteredOetmhdList Size:{}/{}", filteredOetmhdList.size(),
                            filteredOetmhdList.get(0).getOetmsky());
                    // 해당 일자의 변경건 체크
                    List<OetmhdSimpleDTO> dateOetmhdList = filteredOetmhdList.stream()
                            .filter(t -> t.getUldrqdt().equals(uldrqdt))
                            .collect(Collectors.toList());

                    log.info("Changed ShipmentPlan, dateOetmhdList Size:{}", dateOetmhdList.size());
                    // 회차의 변경건 체크는 하지 않음
                    if (dateOetmhdList != null && dateOetmhdList.size() > 0) {
                        // oetmsky List
                        List<String> dateOetmskyDupList = dateOetmhdList.stream()
                                .map(OetmhdSimpleDTO::getOetmsky)
                                .collect(Collectors.toList());

                        // oetmsky 중복제거 List
                        final List<String> dateOetmskyList = ShipmentUtils.distinctStringList(dateOetmskyDupList);
                        log.info("Changed ShipmentPlan, dateOetmskyList Size:{}/{}", dateOetmskyList.size(),
                                dateOetmskyList.get(0));
                        // 남은 oetmsky 리스트와 매칭되는 dummyNewList 필터링
                        dummyNewList = dummyNewList.stream()
                                .filter(t -> dateOetmskyList.contains(t.getOetmsky()))
                                .collect(Collectors.toList());
                        log.info("Changed ShipmentPlan, remain dummyNewList Size:{}", dummyNewList.size());

                    } else {
                        dummyNewList = null;
                    }
                }

            } else {
                log.info("Changed ShipmentPlan, 추가된 운송주문에서 변경은 없음");
                return changedShipPlan;
            }
        }

        List<ShipmentChangedListDTO> changedList = new ArrayList<>();

        try {
            Integer changeType = 0;
            if (dummyList == null || dummyList.size() < 1) {
                if (dummyNewList == null || dummyNewList.size() < 1) {
                    return changedShipPlan;
                } else {
                    changeType = 2;
                    log.info("Changed ShipmentPlan, 추가된 운송주문에서 변경이 있음");
                }
            } else {
                changeType = 1;
                log.info("Changed ShipmentPlan, 이전 운송주문에서 변경이 있음");
            }

            if (changeType == 1) {
                // ShipmentPlan (중복제거)
                final List<String> shplKeys = dummyList.stream()
                        .map(ShipmentPlanChangedDummyDTO::getShpplky)
                        .distinct()
                        .collect(Collectors.toList());
                for (String shplkey : shplKeys) {
                    // shpplky filtered 리스트
                    final List<ShipmentPlanChangedDummyDTO> shplFilterdList = dummyList.stream()
                            .filter(t -> t.getShpplky().equals(shplkey))
                            .collect(Collectors.toList());
                    // ptnrkey (중복제거)
                    final List<String> ptnrKeys = shplFilterdList.stream()
                            .map(ShipmentPlanChangedDummyDTO::getPtnrkey)
                            .distinct()
                            .collect(Collectors.toList());

                    ShipmentPlanChangedDummyDTO dto1 = shplFilterdList.get(0);
                    ShipmentChangedListDTO changedOne = new ShipmentChangedListDTO();
                    changedOne.setDday((int) (ChronoUnit.DAYS.between(dto1.getShpmtdt(), today)));
                    changedOne.setShpmtdt(dto1.getShpmtdt());
                    changedOne.setRoundno(dto1.getRoundno());
                    changedOne.setShpplky(dto1.getShpplky());
                    /*
                     * List<ShipmentChangedPartnerDTO> partnerList = new ArrayList<>();
                     * 
                     * for ( String ptnrkey : ptnrKeys ) {
                     * // ptnrkey filtered 리스트
                     * final List<ShipmentPlanChangedDummyDTO> ptnrFilterdList =
                     * shplFilterdList.stream()
                     * .filter(t -> t.getPtnrkey().equals(ptnrkey))
                     * .collect(Collectors.toList());
                     * // ptnrkey (중복제거)
                     * // final List<String> oetmKeys = ptnrFilterdList.stream()
                     * // .map(ShipmentPlanChangedDummyDTO::getOetmsky)
                     * // .distinct()
                     * // .collect(Collectors.toList());
                     * 
                     * ShipmentPlanChangedDummyDTO dto2 = ptnrFilterdList.get(0);
                     * ShipmentChangedPartnerDTO partnerOne = new ShipmentChangedPartnerDTO();
                     * partnerOne.setPtnrkey(ptnrkey);
                     * partnerOne.setPtnamlc(dto2.getPtnamlc());
                     * partnerOne.setPtnrtyp(dto2.getPtnrtyp());
                     * List<ShipmentChangedOetmskyDTO> oetmskyList = new ArrayList<>();
                     * 
                     * for ( ShipmentPlanChangedDummyDTO dto3 : ptnrFilterdList ) {
                     * ShipmentChangedOetmskyDTO oetmskyOne = new ShipmentChangedOetmskyDTO();
                     * oetmskyOne.setOetmsky(dto3.getOetmsky());
                     * oetmskyOne.setShpmtky(dto3.getShpmtky());
                     * oetmskyOne.setOrderStatus(ShipmentChangeStatusEnum.CHANGE);
                     * oetmskyOne.setTshitst(dto3.getTshitst());
                     * // tmshsc, tmshmt 등록된 데이타
                     * oetmskyOne.setShpmtdt(dto3.getShpmtdt());
                     * oetmskyOne.setCargoty(dto3.getCargoty());
                     * oetmskyOne.setTshitst(dto3.getTshitst());
                     * oetmskyOne.setOpkcate(dto3.getOpkcate());
                     * oetmskyOne.setOpktype(dto3.getOpktype());
                     * oetmskyOne.setOnsidty(dto3.getOnsidty());
                     * oetmskyOne.setOpakqty(dto3.getOpakqty());
                     * 
                     * // ITEM(oetmsky) Detail
                     * ShipmentPlanOrderHistoryDTO itemDetail = new ShipmentPlanOrderHistoryDTO();
                     * itemDetail.setOetmsky(dto3.getOetmsky());
                     * // itemDetail.setIsupdate(dto3.getIsupdate());
                     * itemDetail.setChangat(dto3.getChangat());
                     * 
                     * // tmspodhs
                     * itemDetail.setUldrqdto(dto3.getUldrqdto());
                     * itemDetail.setCargotyo(dto3.getCargotyo());
                     * itemDetail.setTshitsto(dto3.getTshitsto());
                     * itemDetail.setOpkcateo(dto3.getOpkcateo());
                     * itemDetail.setOpktypeo(dto3.getOpktypeo());
                     * itemDetail.setOnsidtyo(dto3.getOnsidtyo());
                     * itemDetail.setOpakqtyo(dto3.getOpakqtyo());
                     * 
                     * itemDetail.setUldrqdtn(dto3.getUldrqdtn());
                     * itemDetail.setCargotyn(dto3.getCargotyn());
                     * itemDetail.setTshitstn(dto3.getTshitstn());
                     * itemDetail.setOpkcaten(dto3.getOpkcaten());
                     * itemDetail.setOpktypen(dto3.getOpktypen());
                     * itemDetail.setOnsidtyn(dto3.getOnsidtyn());
                     * itemDetail.setOpakqtyn(dto3.getOpakqtyn());
                     * 
                     * oetmskyOne.setItemDetail(itemDetail);
                     * oetmskyList.add(oetmskyOne);
                     * }
                     * partnerOne.setOetmskyList(oetmskyList);
                     * partnerList.add(partnerOne);
                     * }
                     * changedOne.setPartnerList(partnerList);
                     */
                    changedList.add(changedOne);
                }

                changedShipPlan.setChangedList(changedList);
                changedShipPlan.setIsChanged(true);
            } else if (changeType == 2) {
                /*
                 * // ShipmentPlan (중복제거)
                 * final List<String> shplKeys = dummyNewList.stream()
                 * .map(ShipmentPlanChangedSimpleDTO::getShpplky)
                 * .distinct()
                 * .collect(Collectors.toList());
                 * for ( String shplkey : shplKeys ) {
                 * // shpplky filtered 리스트
                 * final List<ShipmentPlanChangedSimpleDTO> shplFilterdList =
                 * dummyNewList.stream()
                 * .filter(t -> t.getShpplky().equals(shplkey))
                 * .collect(Collectors.toList());
                 * 
                 * ShipmentPlanChangedSimpleDTO dto1 = shplFilterdList.get(0);
                 * ShipmentChangedListDTO changedOne = new ShipmentChangedListDTO();
                 * changedOne.setDday((int)(ChronoUnit.DAYS.between(dto1.getShpmtdt(), today)));
                 * changedOne.setShpmtdt(dto1.getShpmtdt());
                 * changedOne.setRoundno(dto1.getRoundno());
                 * changedOne.setShpplky(dto1.getShpplky());
                 * List<ShipmentChangedPartnerDTO> partnerList = new ArrayList<>();
                 * 
                 * {
                 * ShipmentPlanChangedSimpleDTO dto2 = shplFilterdList.get(0);
                 * ShipmentChangedPartnerDTO partnerOne = new ShipmentChangedPartnerDTO();
                 * // partnerOne.setPtnrkey(ptnrkey);
                 * // partnerOne.setPtnamlc(dto2.getPtnamlc());
                 * // partnerOne.setPtnrtyp(dto2.getPtnrtyp());
                 * List<ShipmentChangedOetmskyDTO> oetmskyList = new ArrayList<>();
                 * 
                 * {
                 * ShipmentChangedOetmskyDTO oetmskyOne = new ShipmentChangedOetmskyDTO();
                 * oetmskyOne.setOetmsky(dto2.getOetmsky());
                 * oetmskyOne.setShpmtky(dto2.getShpmtky());
                 * oetmskyOne.setOrderStatus(ShipmentChangeStatusEnum.ADD);
                 * oetmskyOne.setTshitst(dto2.getTshitsto());
                 * // tmshsc, tmshmt 등록된 데이타
                 * oetmskyOne.setShpmtdt(dto2.getShpmtdt());
                 * // oetmskyOne.setCargoty(dto2.getCargoty());
                 * oetmskyOne.setTshitst(dto2.getTshitstn());
                 * oetmskyOne.setOpkcate(dto2.getOpkcaten());
                 * oetmskyOne.setOpktype(dto2.getOpktypen());
                 * oetmskyOne.setOnsidty(dto2.getOnsidtyn());
                 * oetmskyOne.setOpakqty(dto2.getOpakqtyn());
                 * 
                 * // ITEM(oetmsky) Detail
                 * ShipmentPlanOrderHistoryDTO itemDetail = new ShipmentPlanOrderHistoryDTO();
                 * itemDetail.setOetmsky(dto2.getOetmsky());
                 * itemDetail.setChangat(dto2.getChangat());
                 * 
                 * // tmspodhs
                 * itemDetail.setUldrqdto(dto2.getUldrqdto());
                 * itemDetail.setCargotyo(dto2.getCargotyo());
                 * itemDetail.setTshitsto(dto2.getTshitsto());
                 * itemDetail.setOpkcateo(dto2.getOpkcateo());
                 * itemDetail.setOpktypeo(dto2.getOpktypeo());
                 * itemDetail.setOnsidtyo(dto2.getOnsidtyo());
                 * itemDetail.setOpakqtyo(dto2.getOpakqtyo());
                 * 
                 * itemDetail.setUldrqdtn(dto2.getUldrqdtn());
                 * itemDetail.setCargotyn(dto2.getCargotyn());
                 * itemDetail.setTshitstn(dto2.getTshitstn());
                 * itemDetail.setOpkcaten(dto2.getOpkcaten());
                 * itemDetail.setOpktypen(dto2.getOpktypen());
                 * itemDetail.setOnsidtyn(dto2.getOnsidtyn());
                 * itemDetail.setOpakqtyn(dto2.getOpakqtyn());
                 * 
                 * oetmskyOne.setItemDetail(itemDetail);
                 * oetmskyList.add(oetmskyOne);
                 * }
                 * partnerOne.setOetmskyList(oetmskyList);
                 * partnerList.add(partnerOne);
                 * }
                 * changedOne.setPartnerList(partnerList);
                 * changedList.add(changedOne);
                 * }
                 */

                ShipmentChangedListDTO changedOne = new ShipmentChangedListDTO();
                ShipmentPlanCheckMadeDTO shpl0 = splMade.get(0);
                changedOne.setShpmtdt(shpl0.getShpmtdt());
                changedOne.setRoundno(shpl0.getRoundno());
                changedOne.setShpplky(shpl0.getShpplky());
                changedList.add(changedOne);
                changedShipPlan.setChangedList(changedList);
                changedShipPlan.setIsChanged(true);
            }

            changedShipPlan.setCompkey(compkey);
            changedShipPlan.setToday(today);
            changedShipPlan.setEndday(endday);

        } catch (Exception ex) {
        }

        return changedShipPlan;
    }

    public ShipmentPlanChangedDTO getChanged3ShipmentPlan(final AuthDTO authDTO,
            @NotBlank final String compkey,
            @Nullable final LocalDate uldrqdt,
            final Integer roundno) {
        LocalDate xDay = LocalDate.now();
        log.info("get Changed3ShipmentPlan, uldrqdt({}) roundno({})", uldrqdt, roundno);
        if (uldrqdt != null) {
            xDay = uldrqdt;
        }
        final LocalDate today = xDay;
        // final LocalDate endday = today.plusDays(5);
        final LocalDate endday = today;

        ShipmentPlanChangedDTO changedShipPlan = new ShipmentPlanChangedDTO();
        List<ShipmentChangedListDTO> changedList = new ArrayList<>();

        try {
            changedShipPlan.setToday(today);
            changedShipPlan.setEndday(endday);
            changedShipPlan.setCompkey(compkey);
            changedShipPlan.setIsChanged(false);
            changedShipPlan.setChangedList(changedList);

            ShipmentPlanCheckMadeDTO planCheck = getShipmentPlan3CheckMade(authDTO, compkey, uldrqdt, roundno);
            if (!planCheck.getIschanged()) {
                return changedShipPlan;
            } else {
                changedShipPlan.setIsChanged(true);
            }

            ShipmentChangedListDTO changedOne = new ShipmentChangedListDTO();
            changedOne.setDday((int) (ChronoUnit.DAYS.between(planCheck.getShpmtdt(), today)));
            changedOne.setShpmtdt(planCheck.getShpmtdt());
            changedOne.setRoundno(planCheck.getRoundno());
            changedOne.setShpplky(planCheck.getShpplky());
            changedList.add(changedOne);

            changedShipPlan.setChangedList(changedList);

        } catch (Exception ex) {
        }

        return changedShipPlan;
    }

    @Transactional
    private List<ShipmentTargetDAO> updateReplaceTruckTonAndLoadPosition(
            List<ShipmentTargetDAO> targetList,
            MultiTruckLoadingResult optimizedList) {
        // 대체차량이면 ,
        // tmshmt 의 VHCTNCD 변경
        // LOADRAT 계산은 어떻게 ?
        // tmshsc 의 VHCTNCD , LOADYPLT , LOADXPLT , LOADYUNIT , LOADXUNIT 변경
        // LOADRAT 계산은 어떻게 ?

        for (TruckResult truck : optimizedList.getTruckResults()) {

            ShipmentVehicleTonEnum vhcton = ShipmentVehicleTonEnum.fromVehicleStringTon(truck.getTruckWeightStr());
            // 용적률 결과
            Integer volumeUtilization = truck.getResult().getVolumeUtilization();
            String prevShpmtky = "";

            for (LoadedItem item : truck.getResult().getLoadedItems()) {
                for (ShipmentTargetDAO sc : targetList) {
                    if (sc.getOetmsky() == item.getId()) {
                        qShipmentSectionRepository.updateReplaceVehicleInfos(
                                sc.getPkscky(),
                                sc.getDpscky(),
                                item.getPosition().getX(),
                                item.getPosition().getY(),
                                item.getPosition().getUnitWidth(),
                                item.getPosition().getUnitDepth(),
                                vhcton);

                        if (!prevShpmtky.equals(sc.getShpmtky())) {
                            qShipmentRepository.updateReplaceVehicleInfos(
                                    sc.getShpmtky(),
                                    vhcton,
                                    volumeUtilization);
                            prevShpmtky = sc.getShpmtky();
                        }
                    }
                }
            }
        }

        return targetList;
    }

    private List<ShipmentSection> reTouchVisitOrder(List<ShipmentSection> shipmentSectionList) {
        Integer visitOrder = 1;

        // vstordr 오름차순 정렬
        shipmentSectionList.sort(Comparator.comparingInt(ShipmentSection::getVstordr));

        /*
         * delete log
         * for ( ShipmentSection s : shipmentSectionList ) {
         * log.info("getVstordr = {} / {} / {}", s.getVstordr(), s.getVsttype(),
         * s.getOetmsky());
         * }
         */

        // 오름차순 정렬된 순서대로 reOrdering
        for (ShipmentSection s : shipmentSectionList) {
            s.setVstordr(visitOrder);
            visitOrder += 1;
        }

        // // PICKUP. 1 부터 넘버링
        // for ( ShipmentSection s : shipmentSectionList ) {
        // if ( s.getVsttype() == VisitTypeEnum.PICKUP ) {
        // s.setVstordr(visitOrder);
        // visitOrder += 1;
        // }
        // }

        // // DROPOFF. 이후 넘버링
        // for ( ShipmentSection s : shipmentSectionList ) {
        // if ( s.getVsttype() == VisitTypeEnum.DROPOFF ) {
        // s.setVstordr(visitOrder);
        // visitOrder += 1;
        // }
        // }

        return shipmentSectionList;
    }

    private List<ShipmentTargetDAO> fillBlankShipmentData(List<ShipmentTargetDAO> targetList) {
        for (ShipmentTargetDAO sc : targetList) {
            // 상차시간이 null
            if (sc.getLodrqtm() == null) {
                sc.setLodrqtm(LocalTime.of(0, 0, 0)); // 00:00:00
            }

            // 하차시간이 null
            if (sc.getUldrqtm() == null) {
                sc.setUldrqtm(LocalTime.of(23, 30, 0)); // 23:30:00
            }

            // FTL 의 요구차량톤수(OrderVehicleTonEnum)를 ShipmentVehicleTonEnum 으로 변환
            if (sc.getCargoty().equals(TruckLoadTypeEnum.FTL)) {
                ShipmentVehicleTonEnum vhctnDef = ShipmentVehicleTonEnum.T05; // for LTL "5톤"
                if (sc.getOrdervhton() != null) {
                    vhctnDef = ShipmentVehicleTonEnum.fromOrderVehicleTonEnum(sc.getOrdervhton());
                }
                sc.setOpvhton(vhctnDef);
            }

            // Coord 좌표 생성
            sc.setPkcoord(GeometryUtils.createPoint(sc.getPklongi(), sc.getPklatit()));
            sc.setDpcoord(GeometryUtils.createPoint(sc.getDplongi(), sc.getDplatit()));
        }

        return targetList;
    }

    private List<ShipmentTargetDAO> fillReplaceShipmentData(List<ShipmentTargetDAO> targetList) {
        for (ShipmentTargetDAO sc : targetList) {
            // 좌표 point
            if (sc.getPkcoord() != null) {
                sc.setPklongi(BigDecimal.valueOf(sc.getPkcoord().getX()));
                sc.setPklatit(BigDecimal.valueOf(sc.getPkcoord().getY()));
            }

            // 좌표 point
            if (sc.getDpcoord() != null) {
                sc.setDplongi(BigDecimal.valueOf(sc.getDpcoord().getX()));
                sc.setDplatit(BigDecimal.valueOf(sc.getDpcoord().getY()));
            }
        }

        return targetList;
    }

    public ShipmentOetmsInfo getShpplkyFromOetmsky(final AuthDTO authDTO, final String oetmsky) {
        // final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
        final String compkey = Objects.requireNonNull(authDTO).getCompany();

        ShipmentOetmsInfo oetmsShipInfo = new ShipmentOetmsInfo();
        List<String> shpplkys = new ArrayList<>();
        List<String> shpmtkys = new ArrayList<>();
        List<String> shpsckys = new ArrayList<>();

        try {
            List<ShipmentSection> scList =
                    // qShipmentSectionRepository.findShipmentSectionListDropoffAllByOetmsky(compkey,
                    // oetmsky);
                    qShipmentSectionRepository.findShipmentSectionListAllByOetmsky(compkey, oetmsky);

            if (scList == null || scList.size() < 1) {
                log.info("Shpplky FromOetmsky, scList is null.{}", oetmsky);
                return null;
            }
            log.info("Shpplky FromOetmsky, scList size:{}", scList.size());
            for (ShipmentSection sc : scList) {
                if (sc.getDeletat() == null) {
                    shpsckys.add(sc.getShpscky());
                }
            }

            // DropOff 만 필터링
            List<ShipmentSection> scDfList = scList.stream()
                    .filter(s -> VisitTypeEnum.DROPOFF.equals(s.getVsttype()))
                    .collect(Collectors.toList());

            List<Shipment> mtList = new ArrayList<>();
            for (ShipmentSection sc : scDfList) {
                String _shpmtky = sc.getShipment().getShpmtky();
                // log.info("Shpplky FromOetmsky, sc shpmtky:{}", _shpmtky);
                List<Shipment> oneMtList = shipmentRepository.findByShpmtkyAndCompkeyAndDeletatIsNull(_shpmtky,
                        compkey);
                if (oneMtList == null || oneMtList.size() < 1) {
                    // log.info("Shpplky FromOetmsky, oneMtList is null.{}", _shpmtky);
                    continue;
                } else {
                    log.info("Shpplky FromOetmsky, {} / oneMtList size:{}", _shpmtky, oneMtList.size());
                }
                mtList.addAll(oneMtList);
            }

            List<ShipmentPlan> plList = new ArrayList<>();
            for (Shipment mt : mtList) {
                shpmtkys.add(mt.getShpmtky());
                String _shpplky = mt.getShipmentPlan().getShpplky();
                log.info("Shpplky FromOetmsky, mt shpplky:{}", _shpplky);
                ShipmentPlan onePl = shipmentPlanRepository.findByShpplkyAndCompkeyAndDeletatIsNull(_shpplky, compkey);
                if (onePl == null) {
                    log.info("Shpplky FromOetmsky, onePl is null.{}", _shpplky);
                    continue;
                } else {
                    log.info("Shpplky FromOetmsky, onePl:{}", onePl);
                }
                plList.add(onePl);
                shpplkys.add(onePl.getShpplky());
            }

            if (plList == null || plList.size() < 1) {
                log.info("Shpplky FromOetmsky, plList is null.");
                return null;
            }

            log.info("Shpplky FromOetmsky, plList size:{}", plList.size());
            if (plList.size() > 1) {
                log.info("Shpplky FromOetmsky, plList:{}", plList);
            }

            oetmsShipInfo.setShpsckys(shpsckys);
            oetmsShipInfo.setShpmtkys(shpmtkys);
            oetmsShipInfo.setShpplkys(shpplkys);
            oetmsShipInfo.setOneShpplky(plList.get(0).getShpplky());
            return oetmsShipInfo;

        } catch (Exception ex) {
        }

        return null;
    }

    @Transactional
    public Boolean testFuncA(final AuthDTO authDTO, final ShipmentTestArgsDTO args) {
        final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();
        final String compkey = Objects.requireNonNull(authDTO).getCompany();
        final String oetmsky = Objects.requireNonNull(args).getOetmsky();

        try {

        } catch (Exception ex) {
            log.info("Exception : {}", ex.toString());
        }

        return false;
    }

    public ShipmentPlanSimpleInfo findShipmentPlanSimpleInfo(@NotBlank final String compkey,
            @NotBlank final String shpplky) {

        return qShipmentPlanRepository.findShipmentPlanSimpleInfo(compkey, shpplky);
    }

}
