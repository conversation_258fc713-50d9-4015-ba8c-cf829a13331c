package com.logistics.tms.shipment.service;

import com.logistics.tms.common.enumeration.VisitTypeEnum;
import com.logistics.tms.external.repository.McodemCustomRepository;
import com.logistics.tms.loadingoptimizationmanager.feign.type.Position;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.ShipmentSimpleInfoDTO;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.ShipmentAllTrackInfoDTO;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.ShipmentLoadingDummyDTO;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.ShipmentLoadingInfoDTO;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.ShipmentLoadingItemDTO;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.ShipmentSectionSimpleTrackDTO;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.ShipmentTrackInfoDTO;
import com.logistics.tms.shipment.dto.ShipmentDTO;
import com.logistics.tms.shipment.dto.ShipmentSectionDTO;
import com.logistics.tms.shipment.entity.Shipment;
import com.logistics.tms.shipment.entity.ShipmentOmsItem;
import com.logistics.tms.shipment.entity.ShipmentSection;
import com.logistics.tms.shipment.mapper.ShipmentMapper;
import com.logistics.tms.shipment.mapper.ShipmentSectionMapper;
import com.logistics.tms.shipment.repository.QShipmentOmsItemRepository;
import com.logistics.tms.shipment.repository.QShipmentRepository;
import com.logistics.tms.shipment.repository.ShipmentRepository;

import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Validated
@RequiredArgsConstructor
@Transactional(readOnly = true)
@Service
public class ShipmentService {

    private final ShipmentRepository shipmentRepository;
    private final QShipmentRepository qShipmentRepository;
    private final QShipmentOmsItemRepository qShipmentOmsItemRepository;
    private final ShipmentMapper shipmentMapper;
    private final ShipmentSectionService shipmentSectionService;
    private final ShipmentSectionMapper shipmentSectionMapper;
    private final McodemCustomRepository mcodemCustomRepository;

    public Optional<ShipmentDTO> getShipment(@NotBlank final String compkey,
            @NotBlank final String shpmtky) {

        return Optional.ofNullable(qShipmentRepository.findShipment(compkey, shpmtky))
                .map(shipmentMapper::toDto);
    }

    public Optional<ShipmentDTO> getShipment2(@NotBlank final String compkey,
            @NotBlank final String shpmtky) {

        return Optional.ofNullable(qShipmentRepository.findShipment2(compkey, shpmtky))
                .map(shipmentMapper::toDto);
    }

    public List<ShipmentDTO> getShipmentListByShipmentPlan(@NotBlank final String compkey,
            @NotBlank final String shpplky) {

        return qShipmentRepository.findShipmentListByShipmentPlan(compkey, shpplky).stream()
                .map(shipmentMapper::toDto)
                .collect(Collectors.toList());
    }

    public Page<ShipmentDTO> getShipmentPage(@NotBlank final String compkey,
            @Nullable final Pageable pageable) {

        return qShipmentRepository.findShipmentPage(compkey, pageable)
                .map(shipmentMapper::toDto);
    }

    public List<ShipmentLoadingInfoDTO> getShipmentLoadingInfo(@NotBlank String compkey,
            @NotBlank String shpmtky) {

        List<ShipmentLoadingInfoDTO> resultList = new ArrayList<>();

        List<ShipmentLoadingDummyDTO> dummyList = qShipmentRepository.findShipmentLoadingInfo(compkey, shpmtky);

        if (dummyList == null || dummyList.size() < 1) {
            return resultList;
        }

        ShipmentLoadingInfoDTO shipmentInfo = ShipmentLoadingInfoDTO.builder()
                .compkey(compkey)
                .shpmtky(shpmtky)
                .cargoty(dummyList.get(0).getCargoty())
                .vhctncd(dummyList.get(0).getVhctncd())
                .vehicleType(dummyList.get(0).getVehicleType())
                .itemList(new ArrayList<>())
                .build();

        for (ShipmentLoadingDummyDTO dummy : dummyList) {
            List<ShipmentOmsItem> omsItemList = qShipmentOmsItemRepository
                    .findShipmentOmsItemListByShpmtkyOetmsky(compkey, shpmtky, dummy.getOetmsky());

            List<Position> positions = new ArrayList<>();
            for (ShipmentOmsItem i : omsItemList) {
                Position pos = Position.builder()
                        .y(i.getLoadyplt())
                        .x(i.getLoadxplt())
                        .unitY(i.getLoadyunit())
                        .unitX(i.getLoadxunit())
                        .unitDepth(i.getLoadUnitDepth())
                        .unitWidth(i.getLoadUnitWidth())
                        .level(Optional.ofNullable(i.getLevel()).orElse(0)) // 기본값 0으로 안전 처리
                        .build();
                positions.add(pos);
            }

            ShipmentLoadingItemDTO item = ShipmentLoadingItemDTO.builder()
                    .puaddr(dummy.getPuaddr())
                    .dfaddr(dummy.getDfaddr())
                    .puptnamlc(dummy.getPuptnamlc())
                    .dfptnamlc(dummy.getDfptnamlc())
                    .pudenamlc(dummy.getPudenamlc())
                    .dfdenamlc(dummy.getDfdenamlc())
                    .destkey(dummy.getDestkey())
                    .oetmsky(dummy.getOetmsky())
                    .opkcate(dummy.getOpkcate())
                    .opktype(dummy.getOpktype())
                    .onsidty(dummy.getOnsidty())
                    .opakqty(dummy.getOpakqty())
                    .pkrweig(dummy.getPkrweig())
                    .pakwidh(dummy.getPakwidh())
                    .pakdept(dummy.getPakdept())
                    .pakheig(dummy.getPakheig())
                    .puvstordr(dummy.getPuvstordr())
                    .dfvstordr(dummy.getDfvstordr())
                    .positions(positions)
                    //// [향후 삭제 예정]
                    .loadyplt(dummy.getLoadyplt())
                    .loadxplt(dummy.getLoadxplt())
                    .loadyunit(dummy.getLoadyunit())
                    .loadxunit(dummy.getLoadxunit())
                    .unitwidh(dummy.getLoadUnitWidth())
                    .unitdept(dummy.getLoadUnitDepth())
                    //// ~ [향후 삭제 예정]
                    .build();

            shipmentInfo.getItemList().add(item);
        }

        resultList.add(shipmentInfo);
        return resultList;
    }

    @Deprecated
    public ShipmentTrackInfoDTO getShipmentTrackInfo(@NotBlank String compkey,
            @NotBlank String shpmtky) {

        ShipmentTrackInfoDTO result = new ShipmentTrackInfoDTO();
        result.setCompkey(compkey);
        result.setShpmtky(shpmtky);

        Shipment sh = shipmentRepository.findByCompkeyAndShpmtky(compkey, shpmtky);
        if (sh == null) {
            return result;
        }
        result.setCargoty(sh.getCargoty());

        Integer sum_loadrat = 0;
        Integer sum_estsecs = 0;
        String pudenamlc = "";
        String puptnamlc = "";

        List<ShipmentSectionDTO> tmpShipScList = shipmentSectionService.getShipmentSectionListByShipment(compkey,
                shpmtky);
        if (!tmpShipScList.isEmpty()) {

            List<ShipmentSectionSimpleTrackDTO> shipAllScList = shipmentSectionMapper
                    .toSimpleTrackDTOList(tmpShipScList);

            List<ShipmentSectionSimpleTrackDTO> shipScList = shipAllScList.stream()
                    .filter(s -> s.getVsttype() == VisitTypeEnum.PICKUP)
                    .collect(Collectors.toList());

            Integer isFirst = 1;
            for (ShipmentSectionSimpleTrackDTO shipmentSection : shipScList) {
                // Null 체크를 통해 null일 경우 0
                if (shipmentSection.getLoadrat() == null)
                    shipmentSection.setLoadrat(0);
                sum_loadrat += shipmentSection.getLoadrat();

                if (isFirst == 1) {
                    shipmentSection.setEstsecs(0);
                } else {
                    if (shipmentSection.getEstsecs() == null)
                        shipmentSection.setEstsecs(0);
                    sum_estsecs += shipmentSection.getEstsecs();
                }

                pudenamlc = concatDenameFormat(pudenamlc, shipmentSection.getDenamlc());
                puptnamlc = concatDenameFormat(puptnamlc, shipmentSection.getPtnamlc());

                isFirst += 1;
            }

            ShipmentSectionSimpleTrackDTO lastShip = shipAllScList.stream()
                    .filter(s -> s.getVsttype() == VisitTypeEnum.DROPOFF) // 필터링
                    .reduce((first, second) -> second) // 마지막 요소 가져오기
                    .orElse(null);

            // 마지막 하차지(조선소) 추가
            if (lastShip != null) {
                if (lastShip.getEstsecs() != null)
                    sum_estsecs += lastShip.getEstsecs();

                shipScList.add(lastShip);
                result.setDfdenamlc(lastShip.getDenamlc());
                result.setDfptnamlc(lastShip.getPtnamlc());
            }

            int countofpickup = (int) shipAllScList.stream()
                    .filter(s -> s.getVsttype() == VisitTypeEnum.PICKUP)
                    .count();
            int countofdropoff = (int) shipAllScList.stream()
                    .filter(s -> s.getVsttype() == VisitTypeEnum.DROPOFF)
                    .count();
            Integer estimatedfee = (countofpickup + countofdropoff) * 5000;
            result.setEstimatedfee(estimatedfee);

            result.setShipmentSectionList(shipScList);
            result.setPudenamlc(pudenamlc);
            result.setPuptnamlc(puptnamlc);
        }

        // result.setLoadrat(sum_loadrat);
        result.setLoadrat(sh.getLoadrat());
        result.setTotalesttime(sum_estsecs);
        return result;
    }

    public ShipmentTrackInfoDTO getShipment2TrackInfo(@NotBlank String compkey,
            @NotBlank String shpmtky) {

        ShipmentTrackInfoDTO result = new ShipmentTrackInfoDTO();
        result.setCompkey(compkey);
        result.setShpmtky(shpmtky);

        Shipment sh = shipmentRepository.findByCompkeyAndShpmtky(compkey, shpmtky);
        if (sh == null) {
            return result;
        }
        result.setCargoty(sh.getCargoty());

        List<ShipmentSectionDTO> tmpShipScList = shipmentSectionService.getShipmentSectionListByShipment(compkey,
                shpmtky);
        if (tmpShipScList == null || tmpShipScList.isEmpty())
            return result;

        List<ShipmentSectionSimpleTrackDTO> shipAllScList = shipmentSectionMapper.toSimpleTrackDTOList(tmpShipScList);
        if (shipAllScList == null || shipAllScList.isEmpty())
            return result;

        Integer sum_estsecs = 0;
        List<ShipmentSectionSimpleTrackDTO> shipScList = new ArrayList<>();

        shipAllScList.sort(Comparator.comparingInt(ShipmentSectionSimpleTrackDTO::getVstordr));

        List<String> dbgPuDeNames = new ArrayList<>();
        List<String> dbgDfDeNames = new ArrayList<>();
        List<String> dbgAllDeNames = new ArrayList<>();
        List<String> puDeNames = new ArrayList<>();
        List<String> puPtNames = new ArrayList<>();
        List<String> dfDeNames = new ArrayList<>();
        List<String> dfPtNames = new ArrayList<>();
        String prevName = "";
        for (ShipmentSectionSimpleTrackDTO sc : shipAllScList) {
            // Null 체크를 통해 null일 경우 0
            if (sc.getLoadrat() == null)
                sc.setLoadrat(0);

            if (sc.getEstsecs() == null)
                sc.setEstsecs(0);
            sum_estsecs += sc.getEstsecs();

            if (VisitTypeEnum.PICKUP.equals(sc.getVsttype())) {
                dbgPuDeNames.add(sc.getDenamlc());
            } else {
                dbgDfDeNames.add(sc.getDenamlc());
            }
            dbgAllDeNames.add(sc.getDenamlc());

            if (prevName.equals(sc.getDenamlc()))
                continue;
            prevName = sc.getDenamlc();

            shipScList.add(sc);
            if (VisitTypeEnum.PICKUP.equals(sc.getVsttype())) {
                puDeNames.add(sc.getDenamlc());
                puPtNames.add(sc.getPtnamlc());
            } else {
                dfDeNames.add(sc.getDenamlc());
                dfPtNames.add(sc.getPtnamlc());
            }
        }

        // log.info("Shipment TrackInfo, puDeNames:{}", puDeNames);
        // log.info("Shipment TrackInfo, puPtNames:{}", puPtNames);
        // log.info("Shipment TrackInfo, dfDeNames:{}", dfDeNames);
        // log.info("Shipment TrackInfo, dfPtNames:{}", dfPtNames);
        result.setOrgPuNames(dbgPuDeNames);
        result.setOrgDfNames(dbgDfDeNames);
        result.setOrgAllNames(dbgAllDeNames);
        log.info("Shipment TrackInfo, dbgAllDeNames:{}", dbgAllDeNames);

        // PickUps
        if (puDeNames.size() == 1) {
            result.setPudenamlc(puDeNames.get(0));
        } else if (puDeNames.size() > 1) {
            String concatNames = "";
            for (String cname : puDeNames) {
                concatNames = concatDenameFormat(concatNames, cname);
            }
            result.setPudenamlc(concatNames);
        }
        if (puPtNames.size() == 1) {
            result.setPuptnamlc(puPtNames.get(0));
        } else if (puPtNames.size() > 1) {
            String concatNames = "";
            for (String cname : puPtNames) {
                concatNames = concatDenameFormat(concatNames, cname);
            }
            result.setPuptnamlc(concatNames);
        }

        // Dropoffs
        if (dfDeNames.size() == 1) {
            result.setDfdenamlc(dfDeNames.get(0));
        } else if (dfDeNames.size() > 1) {
            String concatNames = "";
            for (String cname : dfDeNames) {
                concatNames = concatDenameFormat(concatNames, cname);
            }
            result.setDfdenamlc(concatNames);
        }
        if (dfPtNames.size() == 1) {
            result.setDfptnamlc(dfPtNames.get(0));
        } else if (dfPtNames.size() > 1) {
            String concatNames = "";
            for (String cname : dfPtNames) {
                concatNames = concatDenameFormat(concatNames, cname);
            }
            result.setDfptnamlc(concatNames);
        }

        result.setShipmentSectionList(shipScList);

        result.setEstimatedfee(sh.getEstimatedfee());

        result.setLoadrat(sh.getLoadrat());
        result.setTotalesttime(sum_estsecs);
        return result;
    }

    public ShipmentAllTrackInfoDTO getShipmentAllTrackInfo(@NotBlank String compkey,
            @NotEmpty List<String> shmtList) {
        ShipmentAllTrackInfoDTO result = new ShipmentAllTrackInfoDTO();
        try {
            List<ShipmentTrackInfoDTO> trackInfoList = new ArrayList<>();
            for (String shmtky : shmtList) {
                ShipmentTrackInfoDTO shTrackInfo = this.getShipment2TrackInfo(compkey, shmtky);
                trackInfoList.add(shTrackInfo);
            }
            result.setTrackInfos(trackInfoList);
        } catch (Exception ex) {
        }
        return result;
    }

    @Transactional
    public Integer updateShipmentPlanEmpty(@NotBlank final String shpmtky) {

        try {
            return qShipmentRepository.updateShipmentPlanEmpty(shpmtky);
        } catch (Exception ex) {
        }

        return 0;
    }

    public static String concatDenameFormat(String basename, String dename) {
        if (dename == null)
            return basename;

        StringBuilder result = new StringBuilder();

        result.append(basename);
        if (!basename.isBlank())
            result.append(" > ");

        // 문자열 길이가 3보다 크면 3글자까지 자르고 "..." 추가
        if (dename.length() > 3) {
            result.append(dename.substring(0, 3)).append("...");
        } else {
            result.append(dename);
        }
        result.append(" "); // 각 문자열 사이에 공백 추가

        // 마지막 공백 제거 후 결과 반환
        return result.toString().trim();
    }

    public Integer getDestFeeFromMcodem() {
        try {
            String configString = mcodemCustomRepository.getConfiguration("DEST_FEE");
            return Integer.valueOf(configString);
        } catch (Exception ex) {
        }
        return 0;
    }

    public Integer calcEstimatedFee(final Integer destFee, final List<ShipmentSection> _shpscList) {
        try {
            Integer pickupCount = 0;
            Integer dropoffCount = 0;
            String prevAddr = null;

            // vstordr 오름차순 정렬, List 사본만들기
            List<ShipmentSection> shpscList = _shpscList.stream()
                    .sorted(Comparator.comparing(ShipmentSection::getVstordr,
                            Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());

            for (ShipmentSection sc : shpscList) {
                String deAddr = sc.getDestkey();
                // delete log log.info("calc EstimatedFee: {},{} addr = {} / {}",
                // sc.getVstordr(), sc.getVsttype(), deAddr, prevAddr);
                if (deAddr == null || deAddr.equals(prevAddr)) {
                    // 이전과 동일 목적지
                } else {
                    if (sc.getVsttype().equals(VisitTypeEnum.PICKUP)) {
                        pickupCount += 1;
                    } else if (sc.getVsttype().equals(VisitTypeEnum.DROPOFF)) {
                        // 같은 "삼성중공업" (조선소) 이라도 Dock 이 다르면 다른것으로 처리
                        dropoffCount += 1;
                    }
                    prevAddr = deAddr;
                }
            }

            log.info("calc EstimatedFee : {}", (pickupCount + dropoffCount) * destFee);
            return ((pickupCount + dropoffCount) * destFee);

        } catch (Exception ex) {
        }

        return 0;
    }

    public Integer calcEstimatedFee(final Integer destFee, final Shipment shpmt) {
        try {
            List<ShipmentSection> shpscList = shpmt.getShipmentSectionList();
            return this.calcEstimatedFee(destFee, shpscList);
        } catch (Exception ex) {
        }
        return 0;
    }

    @Deprecated
    public List<ShipmentDTO> getAll() {

        return shipmentRepository.findAll().stream()
                .map(shipmentMapper::toDto)
                .collect(Collectors.toList());
    }

    @Deprecated
    @Transactional
    public ShipmentDTO save(final ShipmentDTO shipmentDTO) {

        final Shipment entity = shipmentMapper.toEntity(shipmentDTO);
        final Shipment savedEntity = shipmentRepository.save(entity);

        return shipmentMapper.toDto(savedEntity);
    }

    public ShipmentSimpleInfoDTO findShipmentSimpleInfo(@NotBlank final String compkey,
            @NotBlank final String shpmtky) {
        return qShipmentRepository.findShipmentSimpleInfo(compkey, shpmtky);
    }
}