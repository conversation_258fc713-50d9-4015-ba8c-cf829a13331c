package com.logistics.tms.shipment.dao;

import com.logistics.tms.common.enumeration.OrderStatusEnum;
import com.logistics.tms.common.enumeration.OrderVehicleTonEnum;
import com.logistics.tms.common.enumeration.ShipmentVehicleTonEnum;
import com.logistics.tms.common.enumeration.PackageCategoryEnum;
import com.logistics.tms.common.enumeration.PackageTypeEnum;
import com.logistics.tms.common.enumeration.PartnerTypeEnum;
import com.logistics.tms.common.enumeration.TruckLoadTypeEnum;

import lombok.Getter;
import lombok.Setter;
import org.locationtech.jts.geom.Point;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Getter
@Setter
public class ShipmentTargetDAO {

    private String compkey;

    private String oetmsky;

    private String ptnrkey;

    private OrderStatusEnum tshitst;

    private String loadkey;

    private String pknamlc;

    private String cunamlc;

    private LocalDate lodrqdt;

    private LocalTime lodrqtm;

    private Integer lodptim;

    private String pkaddr1;

    private String pkaddr2;

    private String pkaddr3;

    private String pkptaddr1;

    private String pkptaddr2;

    private String pkposcd;

    private BigDecimal pklatit;

    private BigDecimal pklongi;

    private Point pkcoord;

    private String custkey;

    private String destkey;

    private String dpnamlc;

    private LocalDate uldrqdt;

    private LocalTime uldrqtm;

    private Integer uldptim;

    private String dpaddr1;

    private String dpaddr2;

    private String dpaddr3;

    private String dpptaddr1;

    private String dpptaddr2;

    private String dpposcd;

    private ShipmentVehicleTonEnum vhctncd; // 차량톤수

    private Integer loadrat; // 적재율

    private BigDecimal dplatit;

    private BigDecimal dplongi;

    private Point dpcoord;

    private TruckLoadTypeEnum cargoty;

    private PackageCategoryEnum opkcate;

    private PackageTypeEnum opktype;

    private String onsidty;

    private OrderVehicleTonEnum ordervhton;

    private ShipmentVehicleTonEnum opvhton;

    private Integer opakqty;

    private Integer pakwidh;

    private Integer pakdept;

    private Integer pakheig;

    private BigDecimal pkrweig;

    private String pkavat1;

    private String pkavat2;

    private String pkavat3;

    private String dpavat1;

    private String dpavat2;

    private String dpavat3;

    private String ptnamlc;

    private String skudesc;

    private String trctname; // 인수담당자

    private String trctnum;

    private Integer rgitqty;

    private PartnerTypeEnum ptnrtyp; // 협력업체 타입

    private PartnerTypeEnum puptnrty; // 상차협력업체 타입

    private ShipmentVehicleTonEnum repVhctn;

    private String shpmtky;

    private String carrkey; // 운송사KEY

    private Boolean turgtyn; // 긴급운송여부

    private Boolean botskyn; // 하단 적재 가능 여부 (Bottom Stackable YN)

    private Boolean topskyn; // 상단 적재 가능 여부 (Top Stackable YN)

    private String pkscky; // pickup SHPSCKY

    private String dpscky; // dropoff SHPSCKY

    @Setter
    private List<TargetItem> oetmit;

    @Getter
    public static class TargetItem {

        private Integer oetmsit;

        private LocalDate slasndt;

    }

}
