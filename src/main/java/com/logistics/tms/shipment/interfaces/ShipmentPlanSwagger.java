package com.logistics.tms.shipment.interfaces;

import com.logistics.tms.common.constant.TmsConstant;
import com.logistics.tms.common.enumeration.SimulationOptionEnum;
import com.logistics.tms.common.enumeration.VisitTypeEnum;
import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.configuration.security.AuthUser;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentResultDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentTargetResultCountDTO;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.ShipmentAllTrackInfoDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentMapResultDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanChangedDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanChangedTargetDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanCheckMadeDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanDeletedDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanFixPrevDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanMakeResponseDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanDTO;
import com.logistics.tms.shipment.dto.ShipmentTargetDTO;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.springdoc.core.converters.models.PageableAsQueryParam;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Tag(name = "ShipmentPlan", description = "운송계획정보 API")
public interface ShipmentPlanSwagger {

    @Operation(summary = "운송계획대상정보 조회")
    @Parameters({
        @Parameter(name = "compkey", description = "시스템코드", required = true, example = TmsConstant.DEFAULT_COMPKEY),
        @Parameter(name = "uldrqdt", description = "하차일", required = true, example = "2024-10-30"),
        @Parameter(name = "roundno", description = "회차", required = true, example = "1"),
        @Parameter(name = "visityp", description = "상차/하차", required = true, example = "하차"),
        @Parameter(name = "ptnrkey", description = "상하차지 고객", required = true),
        @Parameter(name = "destkey", description = "상하차지 키", required = true),
    })
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "성공",
                                useReturnTypeSchema = true,
                                content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                                schema = @Schema(implementation = ShipmentTargetDTO.class))),
    })
    List<ShipmentTargetDTO> getShipmentTargetOne(@NotBlank final String compkey,
                                                 @NotNull final LocalDate uldrqdt,
                                                 @NotNull final Integer roundno,
                                                 @NotNull final VisitTypeEnum visityp,
                                                 @NotBlank final String ptnrkey,
                                                 @NotBlank final String destkey);

    @Operation(summary = "운송계획대상갯수 목록 조회")
    @Parameters({
        @Parameter(name = "compkey", description = "시스템코드", required = true, example = TmsConstant.DEFAULT_COMPKEY),
        @Parameter(name = "uldrqdt", description = "하차일", required = true, example = "2024-10-30"),
        @Parameter(name = "roundno", description = "회차", required = true, example = "1"),
        @Parameter(name = "visityp", description = "상차/하차", required = true, example = "하차"),
        @Parameter(name = "changedflag", description = "아이템 변경 여부", required = false, example = "false"),
        @Parameter(name = "changedshpmtdt", description = "변경 일자", required = false, example = "2024-11-21"),
        @Parameter(name = "changedroundno", description = "변경 회차", required = false, example = "1"),
    })
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "성공",
                                useReturnTypeSchema = true),
    })
    ShipmentPlanChangedTargetDTO getShipmentTargetCount(@NotBlank final String compkey,
                                                        @NotNull final LocalDate uldrqdt,
                                                        @NotNull final Integer roundno,
                                                        @NotNull final VisitTypeEnum visityp,
                                                        @Nullable final Boolean changedflag,
                                                        @Nullable final LocalDate changedshpmtdt,
                                                        @Nullable final Integer changedroundno
                                                        );

    @Operation(summary = "운송계획 생성")
    @Parameters({
            @Parameter(name = "compkey", description = "시스템코드", required = true, example = TmsConstant.DEFAULT_COMPKEY),
            @Parameter(name = "uldrqdt", description = "하차일", required = true, example = "2024-10-30"),
            @Parameter(name = "roundno", description = "회차", required = true, example = "1"),
            @Parameter(name = "simulop", description = SimulationOptionEnum.COLUMN_COMMENT, required = false, example = "지정"),
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = ShipmentPlanMakeResponseDTO.class))),
            @ApiResponse(responseCode = "404", description = "찾을 수 없음",
                    content = @Content(schema = @Schema(hidden = true))),
    })
    ShipmentPlanMakeResponseDTO makeShipmentPlanSimulation(@NotBlank final String compkey,
                                                           @NotNull final LocalDate uldrqdt,
                                                           @NotNull final Integer roundno,
                                                           @Nullable SimulationOptionEnum simulop);

    @Operation(summary = "운송계획 확정")
    @Parameters({
        @Parameter(name = "compkey", description = "시스템코드", required = true, example = TmsConstant.DEFAULT_COMPKEY),
        @Parameter(name = "shpplky", description = "운송계획 번호", required = true, example = "P000000001"),
    })
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "성공"),
        @ApiResponse(responseCode = "404", description = "찾을 수 없음",
                                content = @Content(schema = @Schema(hidden = true))),
    })
    ShipmentPlanCustomDTO.ShipmentPlanFixDTO fixShipmentPlan(@NotBlank final String compkey,
                                                             @NotBlank String shpplky);

    @Operation(summary = "운송계획 선택확정")
    @Parameters({
            @Parameter(name = "compkey", description = "시스템코드", required = true, example = TmsConstant.DEFAULT_COMPKEY),
            @Parameter(name = "shpplky", description = "운송계획 번호", required = true, example = "P000000001"),
            @Parameter(name = "shipmentList", description = "Shipment No.", required = true, example = "S000007103, S000007104, S000007105"),
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "성공"),
            @ApiResponse(responseCode = "404", description = "찾을 수 없음",
                    content = @Content(schema = @Schema(hidden = true))),
    })
    ShipmentPlanCustomDTO.ShipmentPlanFixDTO fixSelectedShipmentPlan(@AuthUser final AuthDTO authDTO,
                                                                     @RequestParam @NotNull final String shpplky,
                                                                     @RequestParam @NotNull final List<String> shipmentList);


    @Operation(summary = "운송계획 확정 확인 팝업")
    @Parameters({
        @Parameter(name = "compkey", description = "시스템코드", required = true, example = TmsConstant.DEFAULT_COMPKEY),
        @Parameter(name = "shpplky", description = "운송계획 번호", required = true, example = "P000000001"),
    })
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                    schema = @Schema(implementation = ShipmentPlanFixPrevDTO.class))),
        @ApiResponse(responseCode = "404", description = "찾을 수 없음",
                                content = @Content(schema = @Schema(hidden = true))),
    })
    ShipmentPlanFixPrevDTO getFixPrevPopupShipmentPlan(@NotBlank final String compkey,
                                                       @NotBlank String shpplky);


    @Operation(summary = "운송계획정보 상세 조회 (작업중)")
    @Parameters({
        @Parameter(name = "compkey", description = "시스템코드", required = true, example = TmsConstant.DEFAULT_COMPKEY),
        @Parameter(name = "shpplky", description = "운송계획 번호", required = true, example = "3000044219"),
    })
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "성공",
                                content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                                schema = @Schema(implementation = ShipmentPlanDTO.class))),
        @ApiResponse(responseCode = "404", description = "찾을 수 없음",
                                content = @Content(schema = @Schema(hidden = true))),
    })
    ShipmentPlanDTO getShipmentPlan(@NotBlank String compkey,
                                    @NotBlank String shpplky);


    @Hidden
    @Operation(summary = "운송계획정보 목록 조회 (작업중)")
    @Parameters({
        @Parameter(name = "compkey", description = "시스템코드", required = true, example = TmsConstant.DEFAULT_COMPKEY),
        @Parameter(name = "pageable", description = "페이지네이션", required = false, hidden = true),
    })
    @PageableAsQueryParam
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "성공",
                                useReturnTypeSchema = true),
    })
    Page<ShipmentPlanDTO> getShipmentPlanPage(@NotBlank String compkey,
                                              @Nullable Pageable pageable);


    @Operation(summary = "운송계획결과정보 목록 갯수 조회")
    @Parameters({
        @Parameter(name = "compkey", description = "시스템코드", required = true, example = TmsConstant.DEFAULT_COMPKEY),
        @Parameter(name = "uldrqdt", description = "하차일", required = true, example = "2024-10-30"),
        @Parameter(name = "roundno", description = "회차", required = true, example = "1"),
    })
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "성공",
                                content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                                schema = @Schema(implementation = ShipmentTargetResultCountDTO.class))),
    })
    ShipmentTargetResultCountDTO apiGetShipmentResultCount(@NotBlank final String compkey,
                                                           @NotNull final LocalDate uldrqdt,
                                                           @NotNull final Integer roundno);


    @Operation(summary = "운송계획 MAIN (결과 생성여부)")
    @Parameters({
        @Parameter(name = "compkey", description = "시스템코드", required = true, example = TmsConstant.DEFAULT_COMPKEY),
        @Parameter(name = "uldrqdt", description = "하차일", required = true, example = "2024-10-30"),
        @Parameter(name = "roundno", description = "회차", required = true, example = "1"),
    })
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "성공",
                                content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                                schema = @Schema(implementation = ShipmentPlanCheckMadeDTO.class))),
    })
    ShipmentPlanCheckMadeDTO getShipmentPlanCheckMade(@NotBlank final String compkey,
                                                      @NotNull final LocalDate uldrqdt,
                                                      @NotNull final Integer roundno);


    @Operation(summary = "운송계획결과정보 조회")
    @Parameters({
        @Parameter(name = "compkey", description = "시스템코드", required = true, example = TmsConstant.DEFAULT_COMPKEY),
        @Parameter(name = "uldrqdt", description = "하차일", required = true, example = "2024-10-30"),
        @Parameter(name = "roundno", description = "회차", required = true, example = "1"),
        @Parameter(name = "ptnrkey", description = "상하차지 고객", required = true),
    })
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "성공",
                                useReturnTypeSchema = true,
                                content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                                schema = @Schema(implementation = ShipmentResultDTO.class))),
    })
    ShipmentResultDTO getShipmentResultList(@NotBlank final String compkey,
                                            @NotNull final LocalDate uldrqdt,
                                            @NotNull final Integer roundno,
                                            @NotBlank final String ptnrkey);


    @Operation(summary = "운송계획결과 납품상세 조회")
    @Parameters({
        @Parameter(name = "compkey", description = "시스템코드", required = true, example = TmsConstant.DEFAULT_COMPKEY),
        @Parameter(name = "uldrqdt", description = "하차일", required = true, example = "2025-01-22"),
        @Parameter(name = "roundno", description = "회차", required = true, example = "2"),
        @Parameter(name = "ptnrkey", description = "상하차지 고객", required = true),
    })
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "성공",
                                useReturnTypeSchema = true,
                                content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                                schema = @Schema(implementation = ShipmentAllTrackInfoDTO.class))),
    })
    ShipmentAllTrackInfoDTO getShipmentAllTrackList(@NotBlank final String compkey,
                                                    @NotNull final LocalDate uldrqdt,
                                                    @NotNull final Integer roundno,
                                                    @NotBlank final String ptnrkey);


    @Operation(summary = "운송계획결과정보 지도")
    @Parameters({
        @Parameter(name = "compkey", description = "시스템코드", required = true, example = TmsConstant.DEFAULT_COMPKEY),
        @Parameter(name = "uldrqdt", description = "하차일", required = true, example = "2024-10-30"),
        @Parameter(name = "roundno", description = "회차", required = true, example = "1"),
    })
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "성공",
                                useReturnTypeSchema = true,
                                content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                                schema = @Schema(implementation = ShipmentResultDTO.class))),
    })
    List<ShipmentMapResultDTO> getShipmentMapResultList(@NotBlank final String compkey,
                                                        @NotNull final LocalDate uldrqdt,
                                                        @NotNull final Integer roundno);


    @Operation(summary = "운송계획 납품정보변경안내(팝업)")
    @Parameters({
        @Parameter(name = "uldrqdt", description = "계획생성일", required = false, example = "2024-11-11"),
        @Parameter(name = "roundno", description = "회차", required = false, example = "1"),
    })
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "성공",
                                content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                                schema = @Schema(implementation = ShipmentPlanChangedDTO.class))),
    })
    ShipmentPlanChangedDTO getChangedShipmentPlan(@Nullable final LocalDate uldrqdt, Integer roundno);


    @Operation(summary = "운송계획 삭제 (테스트용,사용금지)")
    @Parameters({
        @Parameter(name = "compkey", description = "Service Client", required = true, example = "100"),
        @Parameter(name = "uldrqdt", description = "계획생성일자", required = true, example = "2024-11-11"),
        @Parameter(name = "roundno", description = "회차", required = true, example = "1"),
    })
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "성공",
                        content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = @Schema(implementation = ShipmentPlanDeletedDTO.class))),
    })
    ShipmentPlanDeletedDTO deleteShipmentPlans(@NotBlank final String compkey,
                                               @NotNull final LocalDate uldrqdt,
                                               @NotNull final Integer roundno);

}
