package com.logistics.tms.shipment.repository;

import com.logistics.tms.common.enumeration.ShipmentVehicleTonEnum;
import com.logistics.tms.common.enumeration.VisitTypeEnum;
import com.logistics.tms.common.util.AuthUtils;
import com.logistics.tms.common.util.QueryDslUtils;
import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.external.model.QOetmhd;
import com.logistics.tms.shipment.dao.ShipmentTargetDAO;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.ShipmentSimpleInfoDTO;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.OetmhdSimpleDTO;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.ShipmentLoadingDummyDTO;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.ShipmentLoadingInfoDTO;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.ShipmentLoadingItemDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentSimpleInfoPlanDAO;
import com.logistics.tms.shipment.entity.QShipment;
import com.logistics.tms.shipment.entity.QShipmentPlan;
import com.logistics.tms.shipment.entity.QShipmentSection;
import com.logistics.tms.shipment.entity.Shipment;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.support.QuerydslRepositorySupport;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.logistics.tms.shipment.entity.QShipment.shipment;
import static com.logistics.tms.shipment.entity.QShipmentPlan.shipmentPlan;
import static com.logistics.tms.shipment.entity.QShipmentSection.shipmentSection;

@Slf4j
@Validated
@Repository
public class QShipmentRepository extends QuerydslRepositorySupport {

    private final JPAQueryFactory queryFactory;

    public QShipmentRepository(final JPAQueryFactory queryFactory) {
        super(Shipment.class);
        this.queryFactory = queryFactory;
    }

    public Shipment findShipment(@NotBlank final String compkey,
                                 @NotBlank final String shpmtky) {

        return queryFactory
                .selectFrom(shipment)
                .leftJoin(shipment.shipmentSectionList, shipmentSection)
                .fetchJoin()
                .where(
                        eqCompkey(compkey),
                        eqShpmtky(shpmtky)
                )
                .fetchFirst();
    }

    public Shipment findShipment2(@NotBlank final String compkey,
                                 @NotBlank final String shpmtky) {

        return queryFactory
                .selectFrom(shipment)
                .where(
                        eqCompkey(compkey),
                        eqShpmtky(shpmtky)
                )
                .fetchFirst();
    }

    public ShipmentSimpleInfoDTO findShipmentSimpleInfo(@NotBlank final String compkey,
                                                        @NotBlank final String shpmtky) {
        QShipment shpmt = QShipment.shipment;

        return queryFactory
                .select(Projections.fields(ShipmentSimpleInfoDTO.class,
                        shpmt.compkey, shpmt.shpmtky, shpmt.shipmentPlan.shpplky
                ))
                .from(shpmt)
                .where(
                        shpmt.compkey.eq(compkey),
                        shpmt.shpmtky.eq(shpmtky)
                )
                .fetchFirst();
    }

    public List<ShipmentTargetDAO> findShipmentOne(@NotBlank final String compkey,
                                                @NotNull final String shpmtky) {

        QShipment mt = QShipment.shipment;
        QShipmentSection pu = new QShipmentSection("pu");
        QShipmentSection df = new QShipmentSection("df");

        final Predicate[] predicates = {
            mt.deletat.isNull(),
            StringUtils.isNotEmpty(shpmtky) ? mt.shpmtky.eq(shpmtky) : Expressions.FALSE,
            StringUtils.isNotEmpty(compkey) ? mt.compkey.eq(compkey) : Expressions.FALSE
        };

        final List<ShipmentTargetDAO> result = queryFactory
            .select(Projections.fields(ShipmentTargetDAO.class,
                mt.shpmtky.as("shpmtky"),
                mt.cargoty.as("cargoty"),
                mt.loadrat.as("loadrat"),
                mt.vhctncd.as("vhctncd"),
                mt.repVhctn.as("repVhctn"),
                mt.vehicleType.as("vehicleType"),
                mt.manlshp.as("manlshp"),
                mt.ptnrkey.as("ptnrkey"),
                mt.ptnamlc.as("ptnamlc"),
                mt.ptnrtyp.as("ptnrtyp"),
                pu.oetmsky.as("oetmsky"),
                pu.opkcate.as("opkcate"),
                pu.opktype.as("opktype"),
                pu.onsidty.as("onsidty"),
                pu.opakqty.as("opakqty"),
                pu.pakwidh.as("pakwidh"),
                pu.pakdept.as("pakdept"),
                pu.pakheig.as("pakheig"),
                pu.pkrweig.as("pkrweig"),
                pu.rgitqty.as("rgitqty"),
                pu.destkey.as("loadkey"),
                pu.ludrqdt.as("lodrqdt"),
                pu.ludrqtm.as("lodrqtm"),
                pu.decoord.as("pkcoord"),
                pu.shpscky.as("pkscky"),

                df.shpscky.as("dpscky"),
                df.ludrqdt.as("uldrqdt"),
                df.ludrqtm.as("uldrqtm"),
                df.decoord.as("dpcoord"),
                df.ptnrkey.as("custkey"),
                df.destkey.as("destkey"),

                mt.compkey.as("compkey")
            ))
            .from(mt)
            .leftJoin(pu).on(pu.shipment.eq(mt).and(pu.deletat.isNull()).and(pu.vsttype.eq(VisitTypeEnum.PICKUP)))
            // -- df = mt.shpmtky 조건이 없으면 삭제된 이전의 oetmsky 가 추가될수 있음
            .leftJoin(df).on(df.shipment.eq(mt).and(df.oetmsky.eq(pu.oetmsky)).and(df.deletat.isNull()).and(df.vsttype.eq(VisitTypeEnum.DROPOFF)))
            .where(predicates)
            .fetch()
            ;

        return result;
    }

    public List<Shipment> findShipmentListByShipmentPlan(@NotBlank final String compkey,
                                                         @NotBlank final String shpplky) {

        return queryFactory
                .selectFrom(shipment)
                .leftJoin(shipment.shipmentPlan, shipmentPlan)
                .fetchJoin()
                .where(
                        eqCompkey(compkey),
                        eqShpplky(shpplky)
                )
                .fetch();
    }

    public List<ShipmentSimpleInfoPlanDAO> findShipmentByDateAndRound(final String compkey,
                                                    final String ptnrkey,
                                                    final LocalDate reqDate,
                                                    final Integer roundno) {

        QShipment mt = QShipment.shipment;
        QShipmentPlan pl = QShipmentPlan.shipmentPlan;

        List<ShipmentSimpleInfoPlanDAO> result = null;

        try {
            final Predicate[] predicates = {
                // mt.deletat.isNull(),
                mt.manlshp.ne(true), // Not Equal TRUE
                pl.shpmtdt.eq(reqDate),
                pl.roundno.eq(roundno),
                pl.deletat.isNull(),
                StringUtils.isNotEmpty(ptnrkey) ? pl.ptnrkey.eq(ptnrkey) : Expressions.FALSE,
                StringUtils.isNotEmpty(compkey) ? pl.compkey.eq(compkey) : Expressions.FALSE
            };

            result = queryFactory
                .select(Projections.fields(ShipmentSimpleInfoPlanDAO.class,
                    pl.shpplky.as("shpplky"),
                    pl.ptnrkey.as("ptnrkey"),
                    pl.shpmtdt.as("shpmtdt"),
                    pl.roundno.as("roundno"),
                    pl.deletat.as("pldeletat"),
                    mt.shpmtky.as("shpmtky"),
                    mt.manlshp.as("mtmanlshp"),
                    mt.deletat.as("mtdeletat"),
                    pl.compkey.as("compkey")
                ))
                .from(mt)
                    .leftJoin(pl).on(mt.shipmentPlan.eq(pl))
                // .from(pl)
                //     .leftJoin(mt).on(mt.shipmentPlan.eq(pl).and(mt.deletat.isNull()))
                .where(predicates)
                .fetch();
        } catch (Exception ex) {
            log.info("Shipment ByDateAndRound, Exception:{}", ex.toString());
        }

        return result;
    }

    public boolean existShipment(@NotBlank final String compkey,
                                 @NotBlank final String shpmtky) {

        final Shipment result = queryFactory
                .selectFrom(shipment)
                .where(
                        eqCompkey(compkey),
                        eqShpmtky(shpmtky)
                )
                .fetchFirst();

        return Objects.nonNull(result);
    }

    public Integer updateReplaceVehicleInfos (final String shpmtky,
                                            final ShipmentVehicleTonEnum vhctncd,
                                            final Integer volumeUtilization) {
        //
        Integer result = 0;
        QShipment shipment = QShipment.shipment;

        String modifyUser = " ";
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            modifyUser = authDTO.getId();
        }

        queryFactory.update(shipment)
            .set(shipment.vhctncd, vhctncd)
            .set(shipment.loadrat, volumeUtilization)
            .set(shipment.lmouser, modifyUser)
            .set(shipment.lmodate, LocalDate.now())
            .set(shipment.lmotime, LocalTime.now())

            .where(shipment.shpmtky.eq(shpmtky))
            .execute();

        return result;
    }

    public Integer updateShipmentPlanEmpty(final String shpmtky) {
        QShipment shipment = QShipment.shipment;
        log.info("Update ShipmentPlanEmpty : SHMT. {}", shpmtky);

        try {
            String modifyUser = " ";
            final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
            if (Objects.nonNull(authDTO)) {
                modifyUser = authDTO.getId();
            }

            long upResult = queryFactory.update(shipment)
                .set(shipment.shipmentPlan.shpplky, "")
                .set(shipment.lmouser, modifyUser)
                .set(shipment.lmodate, LocalDate.now())
                .set(shipment.lmotime, LocalTime.now())
                .where(shipment.shpmtky.eq(shpmtky))
                .execute();

            return (int) upResult;
        } catch (Exception ex) {}

        return 0;
    }

    public Integer deleteShpmtky(final String shpmtky) {
        Integer result = 0;
        QShipment shipment = QShipment.shipment;
        log.info("Delete Shpmtky : SHMT. {}", shpmtky);

        try {
            String modifyUser = " ";
            final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
            if (Objects.nonNull(authDTO)) {
                modifyUser = authDTO.getId();
            }

            queryFactory.update(shipment)
                .set(shipment.deletat, LocalDateTime.now())
                .set(shipment.lmouser, modifyUser)
                .set(shipment.lmodate, LocalDate.now())
                .set(shipment.lmotime, LocalTime.now())
                .where(shipment.shpmtky.eq(shpmtky))
                .execute();
        } catch (Exception ex) {}

        return result;
    }

    public List<OetmhdSimpleDTO> findOetmhdSimpleByOetmskyList(final String ptnrkey,
                                                            final String compkey,
                                                            final List<String> oetmskyList) {

        final QOetmhd oetmhd = QOetmhd.oetmhd;

        final Predicate[] predicates = {
            StringUtils.isNotEmpty(ptnrkey) ? oetmhd.carrkey.eq(ptnrkey) : Expressions.FALSE,
            StringUtils.isNotEmpty(compkey) ? oetmhd.compkey.eq(compkey) : Expressions.FALSE,
            oetmhd.oetmsky.in(oetmskyList)
        };

        List<OetmhdSimpleDTO> itemList = queryFactory
            .select(Projections.fields(OetmhdSimpleDTO.class,
                oetmhd.oetmsky.as("oetmsky"),
                oetmhd.ptnrkey.as("ptnrkey"),
                oetmhd.loadkey.as("loadkey"),
                oetmhd.custkey.as("custkey"),
                oetmhd.destkey.as("destkey"),
                oetmhd.tshitst.as("tshitst"),
                oetmhd.trnstat.as("trnstat"),
                oetmhd.manuvhp.as("manuvhp"),
                oetmhd.opkcate.as("opkcate"),
                oetmhd.opktype.as("opktype"),
                oetmhd.onsidty.as("onsidty"),
                oetmhd.opakqty.as("opakqty"),
                oetmhd.cargoty.as("cargoty"),
                oetmhd.pakwidh.as("pakwidh"),
                oetmhd.pakdept.as("pakdept"),
                oetmhd.pakheig.as("pakheig"),
                oetmhd.pkrweig.as("pkrweig"),
                oetmhd.lodrqdt.as("lodrqdt"),
                oetmhd.lodrqtm.as("lodrqtm"),
                oetmhd.uldrqdt.as("uldrqdt"),
                oetmhd.uldrqtm.as("uldrqtm"),
                oetmhd.tmshpno.as("tmshpno"),
                oetmhd.turgtyn.as("turgtyn"),
                oetmhd.vhctncd.as("vhctncd")
            ))
            .from(oetmhd)
            .where(predicates)
            .fetch();

        return itemList;
    }

    public Page<Shipment> findShipmentPage(@NotBlank final String compkey,
                                           @Nullable Pageable pageable) {

        pageable = Optional.ofNullable(pageable).orElseGet(Pageable::unpaged);
//        pageable = Objects.requireNonNullElseGet(pageable, Pageable::unpaged);
//        pageable = ObjectUtils.getIfNull(pageable, Pageable::unpaged);

        final JPAQuery<Shipment> selectQuery = queryFactory
                .selectFrom(shipment)
                .leftJoin(shipment.shipmentSectionList, shipmentSection)
                .fetchJoin()
                .where(
                        eqCompkey(compkey)
                )
                .distinct();

        if (pageable.getSort().isSorted()) {
            selectQuery
                    .orderBy(QueryDslUtils.getOrderSpecifiersByPath(pageable.getSort(),
                            Shipment.class,
                            "shipment"));
        }

        if (pageable.isPaged()) {
            selectQuery
                    .offset(pageable.getOffset())
                    .limit(pageable.getPageSize());
        }

        final List<Shipment> content = selectQuery.fetch();

        final JPAQuery<Long> countQuery = queryFactory
                .select(QShipment.shipment.count())
                .from(QShipment.shipment);

        final Page<Shipment> page = PageableExecutionUtils.getPage(content,
                pageable,
                countQuery::fetchOne);

        return page;
    }

    // 이 방법은 Query 를 2번 실행해야 함
    public List<ShipmentLoadingInfoDTO> find_OLD_ShipmentLoadingInfo(@NotBlank String compkey,
                                                                @NotBlank String shpmtky) {
                                                            
        final QShipment mt = QShipment.shipment;
        final QShipmentPlan pl = QShipmentPlan.shipmentPlan;
        // final QShipmentSection df = QShipmentSection.shipmentSection;
        // final QShipmentSection pu = QShipmentSection.shipmentSection;
        // -- 동일한 QShipmentSection 을 2개 사용할때는 위에처럼 하면 동일한 값이 결과값으로 나옴
        // ex) puaddr = dfaddr / pudenamlc = dfdenamlc 처럼 뒤의 값으로 통일됨
        final QShipmentSection df = new QShipmentSection("df");
        final QShipmentSection pu = new QShipmentSection("pu");
        final QOetmhd oh = QOetmhd.oetmhd;

        final Predicate[] predicates = {
            StringUtils.isNotEmpty(compkey) ? mt.compkey.eq(compkey) : null,
            StringUtils.isNotEmpty(shpmtky) ? mt.shpmtky.eq(shpmtky) : null,
            mt.deletat.isNull()
        };

        // JPAQuery<ShipmentLoadingInfoDTO> selectQuery = queryFactory
        //     .select(Projections.fields(ShipmentLoadingInfoDTO.class,
        //         pl.compkey.as("compkey"), // compkey
        //         mt.shpmtky.as("shpmtky"), // Shipment No.
        //         mt.cargoty.as("cargoty"), // 단,혼적
        //         mt.vhctncd.as("vhctncd"), // 차량톤수
        //         Expressions.list(
        //             Projections.fields(ShipmentLoadingItemDTO.class,
        //                 Expressions.stringTemplate(
        //                     "CONCAT({0}, ' ', {1}, ' ', {2})",
        //                     pu.deaddr1, pu.deaddr2, pu.deaddr3
        //                 ).as("puaddr"),  // 상차지 주소
        //                 Expressions.stringTemplate(
        //                     "CONCAT({0}, ' ', {1}, ' ', {2})",
        //                     df.deaddr1, df.deaddr2, df.deaddr3
        //                 ).as("dfaddr"),  // 하차지 주소
        //                 pu.denamlc.as("pudenamlc"), // 상차지 명칭
        //                 df.denamlc.as("dfdenamlc"), // 하차지 명칭
        //                 pu.destkey.as("destkey"), // 도착지 key
        //                 pu.oetmsky.as("oetmsky"), // 운송주문번호
        //                 pu.opkcate.as("opkcate"), // 포장형태
        //                 pu.opktype.as("opktype"), // 포장타입
        //                 pu.onsidty.as("onsidty"), // 포장one-side 타입
        //                 pu.opakqty.as("opakqty"), // 팔레트 박스 수량
        //                 pu.pkrweig.as("pkrweig"), // 중량
        //                 pu.loadyplt.as("loadyplt") // 팔레트 위치
        //             )
        //         ).as("itemList") // <== Error. cannot find symbol """.as"""
        //     ))
        //     .from(mt)
        //         .leftJoin(pl).on(mt.shipmentPlan.eq(pl).and(pl.deletat.isNull()))
        //         .leftJoin(df).on(df.shipment.eq(mt).and(df.deletat.isNull()).and(df.vsttype.eq(VisitTypeEnum.DROPOFF)))
        //         .leftJoin(pu).on(pu.oetmsky.eq(df.oetmsky).and(pu.deletat.isNull()).and(pu.vsttype.eq(VisitTypeEnum.PICKUP)))
        //         // .leftJoin(oh).on(oh.oetmsky.eq(pu.oetmsky))
        //     .where(predicates)
        //     ;

        // final List<ShipmentLoadingInfoDTO> content = selectQuery.fetch();
        // return content;


        // 1. itemList에 해당하는 ShipmentLoadingItemDTO 리스트 조회
        List<ShipmentLoadingItemDTO> itemList = queryFactory
            .select(Projections.fields(ShipmentLoadingItemDTO.class,
                Expressions.stringTemplate(
                    "CONCAT({0}, ' ', {1}, ' ', {2})",
                    pu.deaddr1, pu.deaddr2, pu.deaddr3
                ).as("puaddr"),  // 상차지 주소
                Expressions.stringTemplate(
                    "CONCAT({0}, ' ', {1}, ' ', {2})",
                    df.deaddr1, df.deaddr2, df.deaddr3
                ).as("dfaddr"), // 하차지 주소
                pu.denamlc.as("pudenamlc"), // 상차지 명칭
                df.denamlc.as("dfdenamlc"), // 하차지 명칭 
                pu.destkey.as("destkey"), // 도착지 key
                pu.oetmsky.as("oetmsky"), // 운송주문번호
                pu.opkcate.as("opkcate"), // 포장형태
                pu.opktype.as("opktype"), // 포장타입
                pu.onsidty.as("onsidty"), // 포장one-side 타입
                pu.opakqty.as("opakqty"), // 팔레트 박스 수량
                pu.pkrweig.as("pkrweig"), // 중량
                pu.loadyplt.as("loadyplt"), // 팔레트 위치 (Y) mm
                pu.loadxplt.as("loadxplt") // 팔레트 위치 (X) mm
            ))
            .from(mt)
                .leftJoin(df).on(df.shipment.eq(mt).and(df.deletat.isNull()).and(df.vsttype.eq(VisitTypeEnum.DROPOFF)))
                .leftJoin(pu).on(pu.oetmsky.eq(df.oetmsky).and(pu.deletat.isNull()).and(pu.vsttype.eq(VisitTypeEnum.PICKUP)))
            .where(predicates)
            .orderBy(pu.loadyplt.asc())
            .fetch();

        // 2. ShipmentLoadingInfoDTO 메인 정보 조회
        List<ShipmentLoadingInfoDTO> shipmentList = queryFactory
            .select(Projections.fields(ShipmentLoadingInfoDTO.class,
                mt.compkey.as("compkey"), // compkey
                mt.shpmtky.as("shpmtky"), // Shipment No.
                mt.cargoty.as("cargoty"), // 단,혼적
                mt.vhctncd.as("vhctncd") // 차량톤수
            ))
            .from(mt)
            .where(predicates)
            .fetch();

        // 3. 각 ShipmentLoadingInfoDTO에 itemList를 매핑
        shipmentList.forEach(shipment -> shipment.setItemList(itemList));

        return shipmentList;
    }

    public List<ShipmentLoadingDummyDTO> findShipmentLoadingInfo(@NotBlank String compkey,
                                                                @NotBlank String shpmtky) {
                                                            
        final QShipment mt = QShipment.shipment;
        final QShipmentPlan pl = QShipmentPlan.shipmentPlan;
        // final QShipmentSection df = QShipmentSection.shipmentSection;
        // final QShipmentSection pu = QShipmentSection.shipmentSection;
        // -- 동일한 QShipmentSection 을 2개 사용할때는 위에처럼 하면 동일한 값이 결과값으로 나옴
        // ex) puaddr = dfaddr / pudenamlc = dfdenamlc 처럼 뒤의 값으로 통일됨
        final QShipmentSection df = new QShipmentSection("df");
        final QShipmentSection pu = new QShipmentSection("pu");

        final Predicate[] predicates = {
            StringUtils.isNotEmpty(compkey) ? pl.compkey.eq(compkey) : null,
            StringUtils.isNotEmpty(shpmtky) ? mt.shpmtky.eq(shpmtky) : null,
            mt.deletat.isNull()
        };

        List<ShipmentLoadingDummyDTO> result = queryFactory
            .select(Projections.fields(ShipmentLoadingDummyDTO.class,
                pl.compkey.as("compkey"), // compkey
                mt.shpmtky.as("shpmtky"), // Shipment No.
                mt.cargoty.as("cargoty"), // 단,혼적
                mt.vhctncd.as("vhctncd"), // 차량톤수
                mt.vehicleType.as("vehicleType"), // 차량종류
                Expressions.stringTemplate(
                    "CONCAT({0}, ' ', {1}, ' ', {2})",
                    pu.deaddr1, pu.deaddr2, pu.deaddr3
                ).as("puaddr"),  // 상차지 주소
                Expressions.stringTemplate(
                    "CONCAT({0}, ' ', {1}, ' ', {2})",
                    df.deaddr1, df.deaddr2, df.deaddr3
                ).as("dfaddr"),  // 하차지 주소
                pu.ptnamlc.as("puptnamlc"), // 상차 협력업체 명칭
                df.ptnamlc.as("dfptnamlc"), // 하차 협력업체 명칭
                pu.denamlc.as("pudenamlc"), // 상차지 명칭
                df.denamlc.as("dfdenamlc"), // 하차지 명칭
                pu.destkey.as("destkey"), // 도착지 key
                pu.oetmsky.as("oetmsky"), // 운송주문번호
                pu.opkcate.as("opkcate"), // 포장형태
                pu.opktype.as("opktype"), // 포장타입
                pu.onsidty.as("onsidty"), // 포장one-side 타입
                pu.opakqty.as("opakqty"), // 팔레트 박스 수량
                pu.pkrweig.as("pkrweig"), // 중량
                pu.pakwidh.as("pakwidh"), // 가로
                pu.pakdept.as("pakdept"), // 세로
                pu.pakheig.as("pakheig"), // 높이
                pu.vstordr.as("puvstordr"), // 운송순서.Pickup
                df.vstordr.as("dfvstordr"), // 운송순서.Dropoff
                pu.loadyplt.as("loadyplt"), // 팔레트 위치 (Y) mm
                pu.loadxplt.as("loadxplt"), // 팔레트 위치 (X) mm
                pu.loadyunit.as("loadyunit"), // 팔레트 위치 (Y) unit
                pu.loadxunit.as("loadxunit"), // 팔레트 위치 (Y) unit
                pu.loadUnitDepth.as("loadUnitDepth"), // 팔레트 unit 단위 깊이
                pu.loadUnitWidth.as("loadUnitWidth") // 팔레트 unit 단위 폭
            ))
            .from(pl)
                .leftJoin(mt).on(mt.shipmentPlan.eq(pl).and(pl.deletat.isNull()))
                .leftJoin(df).on(df.shipment.eq(mt).and(df.deletat.isNull()).and(df.vsttype.eq(VisitTypeEnum.DROPOFF)))
                .leftJoin(pu).on(pu.oetmsky.eq(df.oetmsky)
                            .and(pu.shipment.eq(mt))
                            .and(pu.deletat.isNull()).and(pu.vsttype.eq(VisitTypeEnum.PICKUP)))
            .where(predicates)
            .fetch();

        return result;
    }

    private BooleanExpression eqCompkey(final String compkey) {

        if (StringUtils.isNotEmpty(compkey)) {
            return shipment.compkey.eq(compkey);
        } else {
            return Expressions.FALSE;
        }
    }

    private BooleanExpression eqShpmtky(final String shpmtky) {

        if (StringUtils.isNotEmpty(shpmtky)) {
            return shipment.shpmtky.eq(shpmtky);
        } else {
            return Expressions.FALSE;
        }
    }

    private BooleanExpression eqShpplky(final String shpplky) {

        if (StringUtils.isNotEmpty(shpplky)) {
            return shipmentPlan.shpplky.eq(shpplky);
        } else {
            return Expressions.FALSE;
        }
    }

}
