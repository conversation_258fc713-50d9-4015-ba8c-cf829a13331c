package com.logistics.tms.shipment.repository;

import com.logistics.tms.common.constant.TmsConstant;
import com.logistics.tms.common.enumeration.OrderStatusEnum;
import com.logistics.tms.common.enumeration.TruckLoadTypeEnum;
import com.logistics.tms.common.enumeration.UserTypeEnum;
import com.logistics.tms.common.enumeration.VisitTypeEnum;
import com.logistics.tms.common.util.AuthUtils;
import com.logistics.tms.common.util.QueryDslUtils;
import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.external.model.QMdesma;
import com.logistics.tms.external.model.QMptnma;
import com.logistics.tms.external.model.QOetmhd;
import com.logistics.tms.external.model.QOetmit;
import com.logistics.tms.external.model.QOetmhi;
import com.logistics.tms.external.model.QSusrma;
import com.logistics.tms.shipment.dao.ShipmentTargetDAO;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.CarrierInfoDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanSimpleInfo;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentMapDummyDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanChangedDummyDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanChangedSimpleDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanCheckMadeDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanFixDummyDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentResultCountDummyDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentResultListDummyDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentTargetCountDummyDAO;
import com.logistics.tms.shipment.dto.ShipmentTargetDTO;
import com.logistics.tms.shipment.entity.*;
import com.logistics.tms.order.entity.QOrderChangeHistory;
import com.querydsl.core.types.Order;
import com.querydsl.core.group.GroupBy;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.support.QuerydslRepositorySupport;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.logistics.tms.external.model.QMptnma.mptnma;
import static com.logistics.tms.external.model.QMskuma.mskuma;
import static com.logistics.tms.external.model.QOetmhd.oetmhd;
import static com.logistics.tms.external.model.QOetmit.oetmit;
import static com.logistics.tms.shipment.entity.QShipment.shipment;
import static com.logistics.tms.shipment.entity.QShipmentPlan.shipmentPlan;


@Slf4j
@Validated
@Repository
public class QShipmentPlanRepository extends QuerydslRepositorySupport {

    private final JPAQueryFactory queryFactory;

    public QShipmentPlanRepository(final JPAQueryFactory queryFactory) {
        super(ShipmentPlan.class);
        this.queryFactory = queryFactory;
    }

    public List<ShipmentTargetDTO> findShipmentTarget(final String _authPtnrkey,
                                                      @NotBlank final String compkey,
                                                      @NotNull final LocalDate uldrqdt,
                                                      @NotNull final Integer roundno,
                                                      @NotNull final VisitTypeEnum visityp,
                                                      @NotBlank final String ptnrkey,
                                                      @NotBlank final String destkey,
                                                      final Boolean allStatus) {

        final QMdesma pickup = new QMdesma("pickup");
        final QMptnma pickupMptnma = new QMptnma("pickupMptnma");
        final QMdesma dropoff = new QMdesma("dropoff");
        final QMptnma dropoffMptnma = new QMptnma("dropoffMptnma");

        BooleanExpression isFTL = oetmhd.cargoty.eq(TruckLoadTypeEnum.FTL);
        BooleanExpression isLTL = oetmhd.cargoty.eq(TruckLoadTypeEnum.LTL);
        Predicate cargotyPredicate = isFTL.or(isLTL);

        Predicate[] predicates;

        // 25.02.19 상태정보(OrderStatus) 를 allStatus(시뮬레이션 여부) 값에 따라 다르게 처리
        if (VisitTypeEnum.DROPOFF.equals(visityp)) {
            predicates = new Predicate[] {
                // 긴급운송 turgtyn(Y) 인 경우는 제외
                oetmhd.turgtyn.ne(true),
                // 수동배차 manuvhp 인 경우는 제외
                oetmhd.manuvhp.isNull().or(oetmhd.manuvhp.eq(0)).or(oetmhd.manuvhp.eq(1)),
                // OrderStatusEnum.REQUEST 인 경우만 생성
                // oetmhd.tshitst.eq(OrderStatusEnum.REQUEST), // 패키지운송상태 (REQUEST) 주문건에 대하여 Shipment 생성
                cargotyPredicate,
                roundPredicate(oetmhd, uldrqdt, roundno),
                StringUtils.isNotEmpty(_authPtnrkey) ? oetmhd.carrkey.eq(_authPtnrkey) : Expressions.FALSE,
                StringUtils.isNotEmpty(compkey) ? oetmhd.compkey.eq(compkey) : Expressions.FALSE,
                StringUtils.isNotEmpty(ptnrkey) ? oetmhd.custkey.eq(ptnrkey) : Expressions.FALSE,
                StringUtils.isNotEmpty(destkey) ? oetmhd.destkey.eq(destkey) : Expressions.FALSE
            };
        } else if (VisitTypeEnum.PICKUP.equals(visityp)) {
            predicates = new Predicate[] {
                // 긴급운송 turgtyn(Y) 인 경우는 제외
                oetmhd.turgtyn.ne(true),
                // 수동배차 manuvhp 인 경우는 제외
                oetmhd.manuvhp.isNull().or(oetmhd.manuvhp.eq(0)).or(oetmhd.manuvhp.eq(1)),
                // OrderStatusEnum.REQUEST 인 경우만 생성
                // oetmhd.tshitst.eq(OrderStatusEnum.REQUEST), // 패키지운송상태 (REQUEST) 주문건에 대하여 Shipment 생성
                cargotyPredicate,
                roundPredicate(oetmhd, uldrqdt, roundno),
                StringUtils.isNotEmpty(_authPtnrkey) ? oetmhd.carrkey.eq(_authPtnrkey) : Expressions.FALSE,
                StringUtils.isNotEmpty(compkey) ? oetmhd.compkey.eq(compkey) : Expressions.FALSE,
                StringUtils.isNotEmpty(ptnrkey) ? oetmhd.ptnrkey.eq(ptnrkey) : Expressions.FALSE,
                StringUtils.isNotEmpty(destkey) ? oetmhd.loadkey.eq(destkey) : Expressions.FALSE
            };
        } else {
            predicates = new Predicate[]{
                Expressions.FALSE
            };
        }

        // allStatus 값에 따라 조건 추가
        Predicate statusCondition = allStatus 
            ?   // 시뮬레이션 생성 이후
                // OrderStatusEnum.REQUEST/SHIP/PLANCMP/LOAD/LOADHOLD/UNLOAD/DLVCMP 경우만 생성
                oetmhd.tshitst.eq(OrderStatusEnum.REQUEST)
                    .or(oetmhd.tshitst.eq(OrderStatusEnum.SHIP))
                    .or(oetmhd.tshitst.eq(OrderStatusEnum.PLANCMP))
                    .or(oetmhd.tshitst.eq(OrderStatusEnum.LOAD))
                    .or(oetmhd.tshitst.eq(OrderStatusEnum.LOADHOLD))
                    .or(oetmhd.tshitst.eq(OrderStatusEnum.UNLOAD))
                    .or(oetmhd.tshitst.eq(OrderStatusEnum.DLVCMP))
            :   // OrderStatusEnum.REQUEST 인 경우만 생성
                oetmhd.tshitst.eq(OrderStatusEnum.REQUEST)
            ;

        // 조건 배열에 statusCondition 추가
        predicates = ArrayUtils.add(predicates, statusCondition);

        final List<ShipmentTargetDTO> result = queryFactory
            .selectFrom(oetmhd)
            .from(oetmhd)
            .leftJoin(oetmhd.oetmit, oetmit)
            .leftJoin(pickup).on(oetmhd.compkey.eq(pickup.compkey), oetmhd.ptnrkey.eq(pickup.ptnrkey), oetmhd.loadkey.eq(pickup.destkey))
            .leftJoin(pickupMptnma).on(oetmhd.compkey.eq(pickupMptnma.compkey), oetmhd.ptnrkey.eq(pickupMptnma.ptnrkey))
            .leftJoin(dropoff).on(oetmhd.compkey.eq(dropoff.compkey), oetmhd.custkey.eq(dropoff.ptnrkey), oetmhd.destkey.eq(dropoff.destkey))
            .leftJoin(dropoffMptnma).on(oetmhd.compkey.eq(dropoffMptnma.compkey), oetmhd.custkey.eq(dropoffMptnma.ptnrkey))
            .leftJoin(mptnma).on(oetmhd.compkey.eq(mptnma.compkey), oetmhd.custkey.eq(mptnma.ptnrkey))
            .where(predicates)
            .transform(
                GroupBy.groupBy(oetmhd.compkey, oetmhd.oetmsky)
                    .list(Projections.fields(ShipmentTargetDTO.class,
                        oetmhd.compkey.as("compkey"),
                        oetmhd.oetmsky.as("oetmsky"),
                        pickupMptnma.ptnamlc.as("pkptnamlc"),
                        pickup.denamlc.as("pknamlc"),
                        oetmhd.lodrqdt.as("lodrqdt"),
                        oetmhd.lodrqtm.as("lodrqtm"),
                        pickup.lodptim.as("lodptim"),
                        dropoffMptnma.ptnamlc.as("dpptnamlc"),
                        dropoff.denamlc.as("dpnamlc"),
                        oetmhd.uldrqdt.as("uldrqdt"),
                        oetmhd.uldrqtm.as("uldrqtm"),
                        dropoff.uldptim.as("uldptim"),
                        oetmhd.cargoty.as("cargoty"),
                        oetmhd.opkcate.as("opkcate"),
                        oetmhd.opktype.as("opktype"),
                        oetmhd.onsidty.as("onsidty"),
                        oetmhd.opakqty.as("opakqty"),
                        oetmhd.turgtyn.as("turgtyn"),
                        oetmhd.botskyn.as("botskyn"),
                        oetmhd.topskyn.as("topskyn"),
                        pickup.unavat1.as("pkavat1"),
                        pickup.unavat2.as("pkavat2"),
                        pickup.unavat3.as("pkavat3"),
                        dropoff.unavat1.as("dpavat1"),
                        dropoff.unavat2.as("dpavat2"),
                        dropoff.unavat3.as("dpavat3"),
                        mptnma.ptnamlc.as("ptnamlc"),
                        mptnma.ptnrtyp.as("ptnrtyp"),
                        GroupBy.list(Projections.fields(ShipmentTargetDTO.TargetItem.class,
                            oetmit.oetmsit.as("oetmsit"),
                            oetmit.slasndt.as("slasndt")
                        )).as("oetmit")
                    ))
            );

        return result;
    }

    /*
    @Deprecated
    public List<ShipmentTargetCountDTO> findShipmentTargetCount(final String ptnrkey,
                                                                @NotBlank final String compkey,
                                                                @NotNull final LocalDate uldrqdt,
                                                                @NotNull final Integer roundno,
                                                                @NotNull final VisitTypeEnum visityp) {

        List<ShipmentTargetCountDTO> result;

        LocalTime startTime = ShipmentConstant.SHIPMENT_ROUND_STARTTIME[roundno];
        LocalTime endTime = ShipmentConstant.SHIPMENT_ROUND_ENDTIME[roundno];
        Predicate roundTimePredicate = Expressions.TRUE;
        if ( roundno == 1 ) {
            roundTimePredicate = (oetmhd.uldrqtm.between(startTime, endTime)).or(oetmhd.uldrqtm.isNull());
        } else if ( roundno == 2 || roundno == 3 ) {
            roundTimePredicate = oetmhd.uldrqtm.between(startTime, endTime);
        }

        BooleanExpression isFTL = oetmhd.cargoty.eq(TruckLoadTypeEnum.FTL);
        BooleanExpression isLTL = oetmhd.cargoty.eq(TruckLoadTypeEnum.LTL);
        Predicate cargotyPredicate = isFTL.or(isLTL);

        Predicate[] predicates = new Predicate[] {
            // 수동배차 manuvhp 인 경우는 제외
            oetmhd.manuvhp.isNull().or(oetmhd.manuvhp.eq(0)).or(oetmhd.manuvhp.eq(1)),
            // OrderStatusEnum.REQUEST 인 경우만 생성
            oetmhd.tshitst.eq(OrderStatusEnum.REQUEST), // 패키지운송상태 (REQUEST) 주문건에 대하여 Shipment 생성
            // oetmhd.tshitst.ne(OrderStatusEnum.CANCEL), // 패키지운송상태 (CANCEL)은 제외
            cargotyPredicate,
            roundTimePredicate,
            StringUtils.isNotEmpty(ptnrkey) ? oetmhd.carrkey.eq(ptnrkey) : Expressions.FALSE,
            StringUtils.isNotEmpty(compkey) ? oetmhd.compkey.eq(compkey) : Expressions.FALSE,
            Objects.nonNull(uldrqdt) ? oetmhd.uldrqdt.eq(uldrqdt) : Expressions.FALSE
        };

        if (VisitTypeEnum.DROPOFF.equals(visityp)) {
            final QMdesma dropoff = new QMdesma("dropoff");

            result = queryFactory
                .select(Projections.fields(ShipmentTargetCountDTO.class,
                    oetmhd.custkey.as("ptnrkey"),
                    oetmhd.destkey.as("destkey"),
                    dropoff.denamlc.as("denamlc"),
                    mptnma.ptnamlc.as("ptnamlc"),
                    mptnma.ptnrtyp.as("ptnrtyp"),
                    oetmhd.lodrqdt.as("lodrqdt"),
                    oetmhd.uldrqdt.as("uldrqdt"),
                    oetmhd.count().castToNum(Integer.class).as("totcont"),
                    oetmhd.cargoty.when(TruckLoadTypeEnum.FTL)
                        .then(Expressions.asNumber(1L).castToNum(Integer.class))
                        .otherwise(0)
                        .sum()
                        .as("ftlcont"),
                    oetmhd.cargoty.when(TruckLoadTypeEnum.LTL)
                        .then(Expressions.asNumber(1L).castToNum(Integer.class))
                        .otherwise(0)
                        .sum()
                        .as("ltlcont"),
                    oetmhd.compkey.as("compkey")
                ))
                .from(oetmhd)
                .leftJoin(dropoff).on(oetmhd.compkey.eq(dropoff.compkey), oetmhd.custkey.eq(dropoff.ptnrkey), oetmhd.destkey.eq(dropoff.destkey))
                .leftJoin(mptnma).on(oetmhd.compkey.eq(mptnma.compkey), oetmhd.custkey.eq(mptnma.ptnrkey))
                .where(predicates)
                .groupBy(oetmhd.custkey, oetmhd.destkey)
                // .groupBy(oetmhd.ptnrkey, oetmhd.destkey) // ptnrkey 로 Group ?
                .fetch();
        } else if (VisitTypeEnum.PICKUP.equals(visityp)) {
            final QMdesma pickup = new QMdesma("pickup");

            result = queryFactory
                .select(Projections.fields(ShipmentTargetCountDTO.class,
                    oetmhd.compkey.as("compkey"),
                    oetmhd.ptnrkey.as("ptnrkey"),
                    oetmhd.loadkey.as("destkey"),
                    pickup.denamlc.as("denamlc"),
                    mptnma.ptnamlc.as("ptnamlc"),
                    mptnma.ptnrtyp.as("ptnrtyp"),
                    oetmhd.lodrqdt.as("lodrqdt"),
                    oetmhd.uldrqdt.as("uldrqdt"),
                    oetmhd.count().castToNum(Integer.class).as("totcont"),
                    oetmhd.cargoty.when(TruckLoadTypeEnum.FTL)
                        .then(Expressions.asNumber(1L).castToNum(Integer.class))
                        .otherwise(0)
                        .sum()
                        .as("ftlcont"),
                    oetmhd.cargoty.when(TruckLoadTypeEnum.LTL)
                        .then(Expressions.asNumber(1L).castToNum(Integer.class))
                        .otherwise(0)
                        .sum()
                        .as("ltlcont")
                ))
                .from(oetmhd)
                .leftJoin(pickup).on(oetmhd.compkey.eq(pickup.compkey), oetmhd.ptnrkey.eq(pickup.ptnrkey), oetmhd.loadkey.eq(pickup.destkey))
                .leftJoin(mptnma).on(oetmhd.compkey.eq(mptnma.compkey), oetmhd.ptnrkey.eq(mptnma.ptnrkey))
                .where(predicates)
                .groupBy(oetmhd.ptnrkey, oetmhd.loadkey)
                .fetch();
        } else {
            return null;
        }

        return result;
    }
    */

    public List<ShipmentTargetCountDummyDAO> findShipmentTarget2Count(final String ptnrkey,
                                                                @NotBlank final String compkey,
                                                                @NotNull final LocalDate uldrqdt,
                                                                @NotNull final Integer roundno,
                                                                @NotNull final VisitTypeEnum visityp,
                                                                final Boolean allStatus) {

        List<ShipmentTargetCountDummyDAO> result = null;

        BooleanExpression isFTL = oetmhd.cargoty.eq(TruckLoadTypeEnum.FTL);
        BooleanExpression isLTL = oetmhd.cargoty.eq(TruckLoadTypeEnum.LTL);
        Predicate cargotyPredicate = isFTL.or(isLTL);

        Predicate[] predicates = new Predicate[] {
            // 긴급운송 turgtyn(Y) 인 경우는 제외
            oetmhd.turgtyn.ne(true),
            // 수동배차 manuvhp 인 경우는 제외
            oetmhd.manuvhp.isNull().or(oetmhd.manuvhp.eq(0)).or(oetmhd.manuvhp.eq(1)),
            cargotyPredicate,
            roundPredicate(oetmhd, uldrqdt, roundno),
            StringUtils.isNotEmpty(ptnrkey) ? oetmhd.carrkey.eq(ptnrkey) : Expressions.FALSE,
            StringUtils.isNotEmpty(compkey) ? oetmhd.compkey.eq(compkey) : Expressions.FALSE
        };

        // allStatus 값에 따라 조건 추가
        Predicate statusCondition = allStatus 
            ?   // 시뮬레이션 생성 이후
                // OrderStatusEnum.REQUEST/SHIP/PLANCMP/LOAD/LOADHOLD/UNLOAD/DLVCMP 경우만 생성
                oetmhd.tshitst.eq(OrderStatusEnum.REQUEST)
                    .or(oetmhd.tshitst.eq(OrderStatusEnum.SHIP))
                    .or(oetmhd.tshitst.eq(OrderStatusEnum.PLANCMP))
                    .or(oetmhd.tshitst.eq(OrderStatusEnum.LOAD))
                    .or(oetmhd.tshitst.eq(OrderStatusEnum.LOADHOLD))
                    .or(oetmhd.tshitst.eq(OrderStatusEnum.UNLOAD))
                    .or(oetmhd.tshitst.eq(OrderStatusEnum.DLVCMP))
            :   // OrderStatusEnum.REQUEST 인 경우만 생성
                oetmhd.tshitst.eq(OrderStatusEnum.REQUEST)
            ;

        // 조건 배열에 statusCondition 추가
        predicates = ArrayUtils.add(predicates, statusCondition);

        if (VisitTypeEnum.DROPOFF.equals(visityp)) {
            final QMdesma dropoff = new QMdesma("dropoff");

            result = queryFactory
                .select(Projections.fields(ShipmentTargetCountDummyDAO.class,
                    oetmhd.custkey.as("custkey"),
                    oetmhd.destkey.as("destkey"),
                    oetmhd.ptnrkey.as("ptnrkey"),
                    oetmhd.loadkey.as("loadkey"),
                    dropoff.denamlc.as("denamlc"),
                    mptnma.ptnamlc.as("ptnamlc"),
                    mptnma.ptnrtyp.as("ptnrtyp"),
                    oetmhd.cargoty.as("cargoty"),
                    oetmhd.lodrqdt.as("lodrqdt"),
                    oetmhd.lodrqtm.as("lodrqtm"),
                    oetmhd.uldrqdt.as("uldrqdt"),
                    oetmhd.uldrqtm.as("uldrqtm"),
                    oetmhd.tshitst.as("tshitst"),
                    oetmhd.tmshpno.as("tmshpno"),
                    oetmhd.manuvhp.as("manuvhp"),
                    oetmhd.turgtyn.as("turgtyn"),
                    oetmhd.botskyn.as("botskyn"),
                    oetmhd.topskyn.as("topskyn"),
                    oetmhd.compkey.as("compkey")
                ))
                .from(oetmhd)
                .leftJoin(dropoff).on(oetmhd.compkey.eq(dropoff.compkey), oetmhd.custkey.eq(dropoff.ptnrkey), oetmhd.destkey.eq(dropoff.destkey))
                .leftJoin(mptnma).on(oetmhd.compkey.eq(mptnma.compkey), oetmhd.custkey.eq(mptnma.ptnrkey))
                .where(predicates)
                .fetch();
        } else if (VisitTypeEnum.PICKUP.equals(visityp)) {
            final QMdesma pickup = new QMdesma("pickup");

            result = queryFactory
                .select(Projections.fields(ShipmentTargetCountDummyDAO.class,
                    oetmhd.custkey.as("custkey"),
                    oetmhd.destkey.as("destkey"),
                    oetmhd.ptnrkey.as("ptnrkey"),
                    oetmhd.loadkey.as("loadkey"),
                    pickup.denamlc.as("denamlc"),
                    mptnma.ptnamlc.as("ptnamlc"),
                    mptnma.ptnrtyp.as("ptnrtyp"),
                    oetmhd.cargoty.as("cargoty"),
                    oetmhd.lodrqdt.as("lodrqdt"),
                    oetmhd.lodrqtm.as("lodrqtm"),
                    oetmhd.uldrqdt.as("uldrqdt"),
                    oetmhd.uldrqtm.as("uldrqtm"),
                    oetmhd.tshitst.as("tshitst"),
                    oetmhd.tmshpno.as("tmshpno"),
                    oetmhd.manuvhp.as("manuvhp"),
                    oetmhd.turgtyn.as("turgtyn"),
                    oetmhd.botskyn.as("botskyn"),
                    oetmhd.topskyn.as("topskyn"),
                    oetmhd.compkey.as("compkey")
                ))
                .from(oetmhd)
                .leftJoin(pickup).on(oetmhd.compkey.eq(pickup.compkey), oetmhd.ptnrkey.eq(pickup.ptnrkey), oetmhd.loadkey.eq(pickup.destkey))
                .leftJoin(mptnma).on(oetmhd.compkey.eq(mptnma.compkey), oetmhd.ptnrkey.eq(mptnma.ptnrkey))
                .where(predicates)
                .fetch();
        } else {
            return null;
        }

        return result;
    }

    public List<ShipmentTargetDAO> findShipmentTargetList(final String ptnrkey,
                                                          @NotBlank final String compkey,
                                                          @NotNull final LocalDate uldrqdt,
                                                          @NotNull final Integer roundno) {

        final QMdesma pickup = new QMdesma("pickup");
        final QMdesma dropoff = new QMdesma("dropoff");
        final QMptnma mptnmapu = new QMptnma("mptnmapu");
        final QMptnma mptnmadf = new QMptnma("mptnmadf");

        BooleanExpression isFTL = oetmhd.cargoty.eq(TruckLoadTypeEnum.FTL);
        BooleanExpression isLTL = oetmhd.cargoty.eq(TruckLoadTypeEnum.LTL);
        Predicate cargotyPredicate = isFTL.or(isLTL);

        final Predicate[] predicates = {
            // 긴급운송 turgtyn(Y) 인 경우는 제외
            oetmhd.turgtyn.ne(true),
            // 수동배차 manuvhp 인 경우는 제외
            oetmhd.manuvhp.isNull().or(oetmhd.manuvhp.eq(0)).or(oetmhd.manuvhp.eq(1)),
            // OrderStatusEnum.REQUEST 인 경우만 생성
            oetmhd.tshitst.eq(OrderStatusEnum.REQUEST), // 패키지운송상태 (REQUEST) 주문건에 대하여 Shipment 생성
            // oetmhd.tshitst.ne(OrderStatusEnum.CANCEL), // 패키지운송상태 (CANCEL)은 제외
            cargotyPredicate,
            roundPredicate(oetmhd, uldrqdt, roundno),
            StringUtils.isNotEmpty(ptnrkey) ? oetmhd.carrkey.eq(ptnrkey) : Expressions.FALSE,
            StringUtils.isNotEmpty(compkey) ? oetmhd.compkey.eq(compkey) : Expressions.FALSE
        };

        final List<ShipmentTargetDAO> result = queryFactory
                .selectFrom(oetmhd)
                .from(oetmhd)
                .leftJoin(oetmhd.oetmit, oetmit)
                .leftJoin(pickup).on(oetmhd.compkey.eq(pickup.compkey), oetmhd.ptnrkey.eq(pickup.ptnrkey), oetmhd.loadkey.eq(pickup.destkey))
                .leftJoin(dropoff).on(oetmhd.compkey.eq(dropoff.compkey), oetmhd.custkey.eq(dropoff.ptnrkey), oetmhd.destkey.eq(dropoff.destkey))
                .leftJoin(mptnmapu).on(oetmhd.compkey.eq(mptnmapu.compkey), oetmhd.ptnrkey.eq(mptnmapu.ptnrkey))
                .leftJoin(mptnmadf).on(oetmhd.compkey.eq(mptnmadf.compkey), oetmhd.custkey.eq(mptnmadf.ptnrkey))
                .leftJoin(mskuma).on(oetmit.compkey.eq(mskuma.compkey), oetmit.ownerky.eq(mskuma.ownerky), oetmit.skumkey.eq(mskuma.skumkey))
                .where(predicates)
                .transform(
                    GroupBy.groupBy(oetmhd.compkey, oetmhd.oetmsky)
                        .list(Projections.fields(ShipmentTargetDAO.class,
                            oetmhd.compkey.as("compkey"),
                            oetmhd.oetmsky.as("oetmsky"),
                            oetmhd.ptnrkey.as("ptnrkey"),
                            oetmhd.loadkey.as("loadkey"),
                            pickup.denamlc.as("pknamlc"),
                            oetmhd.lodrqdt.as("lodrqdt"),
                            oetmhd.lodrqtm.as("lodrqtm"),
                            oetmhd.tshitst.as("tshitst"),
                            pickup.lodptim.as("lodptim"),
                            pickup.deaddr1.as("pkaddr1"),
                            pickup.deaddr2.as("pkaddr2"),
                            pickup.deaddr3.as("pkaddr3"),
                            mptnmapu.ptaddr1.as("pkptaddr1"),
                            mptnmapu.ptaddr2.as("pkptaddr2"),
                            pickup.deposcd.as("pkposcd"),
                            // 위,경도 DB field 위치가 반대로 되어있어서, 임시로 반대로 입력받음 => 241216 정상화
                            pickup.destlat.as("pklatit"),
                            pickup.destlon.as("pklongi"),
                            // pickup.destlat.as("pklongi"),
                            // pickup.destlon.as("pklatit"),
                            oetmhd.custkey.as("custkey"),
                            oetmhd.destkey.as("destkey"),
                            dropoff.denamlc.as("dpnamlc"),
                            oetmhd.uldrqdt.as("uldrqdt"),
                            oetmhd.uldrqtm.as("uldrqtm"),
                            dropoff.uldptim.as("uldptim"),
                            dropoff.deaddr1.as("dpaddr1"),
                            dropoff.deaddr2.as("dpaddr2"),
                            dropoff.deaddr3.as("dpaddr3"),
                            mptnmadf.ptaddr1.as("dpptaddr1"),
                            mptnmadf.ptaddr2.as("dpptaddr2"),
                            dropoff.deposcd.as("dpposcd"),
                            // 위,경도 DB field 위치가 반대로 되어있어서, 임시로 반대로 입력받음 => 241216 정상화
                            dropoff.destlat.as("dplatit"),
                            dropoff.destlon.as("dplongi"),
                            // dropoff.destlat.as("dplongi"),
                            // dropoff.destlon.as("dplatit"),
                            oetmhd.cargoty.as("cargoty"),
                            oetmhd.opkcate.as("opkcate"),
                            oetmhd.opktype.as("opktype"),
                            oetmhd.onsidty.as("onsidty"),
                            oetmhd.opvhton.as("ordervhton"),
                            oetmhd.opakqty.as("opakqty"),
                            oetmhd.pakwidh.as("pakwidh"),
                            oetmhd.pakdept.as("pakdept"),
                            oetmhd.pakheig.as("pakheig"),
                            oetmhd.pkrweig.as("pkrweig"),
                            oetmhd.carrkey.as("carrkey"),
                            oetmhd.turgtyn.as("turgtyn"),
                            oetmhd.botskyn.as("botskyn"),
                            oetmhd.topskyn.as("topskyn"),
                            pickup.unavat1.as("pkavat1"),
                            pickup.unavat2.as("pkavat2"),
                            pickup.unavat3.as("pkavat3"),
                            dropoff.unavat1.as("dpavat1"),
                            dropoff.unavat2.as("dpavat2"),
                            dropoff.unavat3.as("dpavat3"),
                            mptnmapu.ptnamlc.as("ptnamlc"),
                            mptnmadf.ptnamlc.as("cunamlc"),
                            mskuma.skudesc.as("skudesc"),
                            oetmit.saatc07.as("trctname"),
                            oetmit.saatc08.as("trctnum"),
                            oetmit.rgitqty.as("rgitqty"),
                            mptnmadf.ptnrtyp.as("ptnrtyp"),
                            mptnmapu.ptnrtyp.as("puptnrty"),
                            GroupBy.list(Projections.fields(ShipmentTargetDAO.TargetItem.class,
                                oetmit.oetmsit.as("oetmsit"),
                                oetmit.slasndt.as("slasndt")
                            )).as("oetmit")
                        ))
                );

        return result;

    }

    public List<ShipmentTargetDAO> findShipmentTargetOne(@NotBlank final String compkey,
                                                        @NotBlank final String oetmsky,
                                                        @NotBlank final  String ptnrkey) {

        final QMdesma pickup = new QMdesma("pickup");
        final QMdesma dropoff = new QMdesma("dropoff");
        final QMptnma mptnmapu = new QMptnma("mptnmapu");
        final QMptnma mptnmadf = new QMptnma("mptnmadf");

        BooleanExpression isFTL = oetmhd.cargoty.eq(TruckLoadTypeEnum.FTL);
        BooleanExpression isLTL = oetmhd.cargoty.eq(TruckLoadTypeEnum.LTL);
        Predicate cargotyPredicate = isFTL.or(isLTL);

        final Predicate[] predicates = {
            // OrderStatusEnum.REQUEST , SHIP , LOADHOLD 인 경우만 생성
            oetmhd.tshitst.eq(OrderStatusEnum.REQUEST)
                .or(oetmhd.tshitst.eq(OrderStatusEnum.SHIP))
                .or(oetmhd.tshitst.eq(OrderStatusEnum.LOADHOLD)),
            cargotyPredicate,
            StringUtils.isNotEmpty(ptnrkey) ? oetmhd.carrkey.eq(ptnrkey) : Expressions.FALSE,
            StringUtils.isNotEmpty(oetmsky) ? oetmhd.oetmsky.eq(oetmsky) : Expressions.FALSE,
            StringUtils.isNotEmpty(compkey) ? oetmhd.compkey.eq(compkey) : Expressions.FALSE
        };

        final List<ShipmentTargetDAO> result = queryFactory
                .selectFrom(oetmhd)
                .from(oetmhd)
                .leftJoin(oetmhd.oetmit, oetmit)
                .leftJoin(pickup).on(oetmhd.compkey.eq(pickup.compkey), oetmhd.ptnrkey.eq(pickup.ptnrkey), oetmhd.loadkey.eq(pickup.destkey))
                .leftJoin(dropoff).on(oetmhd.compkey.eq(dropoff.compkey), oetmhd.custkey.eq(dropoff.ptnrkey), oetmhd.destkey.eq(dropoff.destkey))
                .leftJoin(mptnmapu).on(oetmhd.compkey.eq(mptnmapu.compkey), oetmhd.ptnrkey.eq(mptnmapu.ptnrkey))
                .leftJoin(mptnmadf).on(oetmhd.compkey.eq(mptnmadf.compkey), oetmhd.custkey.eq(mptnmadf.ptnrkey))
                .leftJoin(mskuma).on(oetmit.compkey.eq(mskuma.compkey), oetmit.ownerky.eq(mskuma.ownerky), oetmit.skumkey.eq(mskuma.skumkey))
                .where(predicates)
                .orderBy(oetmhd.credate.desc())
                .transform(
                    GroupBy.groupBy(oetmhd.compkey, oetmhd.oetmsky)
                        .list(Projections.fields(ShipmentTargetDAO.class,
                            oetmhd.compkey.as("compkey"),
                            oetmhd.oetmsky.as("oetmsky"),
                            oetmhd.ptnrkey.as("ptnrkey"),
                            oetmhd.loadkey.as("loadkey"),
                            pickup.denamlc.as("pknamlc"),
                            oetmhd.lodrqdt.as("lodrqdt"),
                            oetmhd.lodrqtm.as("lodrqtm"),
                            oetmhd.tshitst.as("tshitst"),
                            pickup.lodptim.as("lodptim"),
                            pickup.deaddr1.as("pkaddr1"),
                            pickup.deaddr2.as("pkaddr2"),
                            pickup.deaddr3.as("pkaddr3"),
                            mptnmapu.ptaddr1.as("pkptaddr1"),
                            mptnmapu.ptaddr2.as("pkptaddr2"),
                            pickup.deposcd.as("pkposcd"),
                            // 위,경도 DB field 위치가 반대로 되어있어서, 임시로 반대로 입력받음 => 241216 정상화
                            pickup.destlat.as("pklatit"),
                            pickup.destlon.as("pklongi"),
                            // pickup.destlat.as("pklongi"),
                            // pickup.destlon.as("pklatit"),
                            oetmhd.custkey.as("custkey"),
                            oetmhd.destkey.as("destkey"),
                            dropoff.denamlc.as("dpnamlc"),
                            oetmhd.uldrqdt.as("uldrqdt"),
                            oetmhd.uldrqtm.as("uldrqtm"),
                            dropoff.uldptim.as("uldptim"),
                            dropoff.deaddr1.as("dpaddr1"),
                            dropoff.deaddr2.as("dpaddr2"),
                            dropoff.deaddr3.as("dpaddr3"),
                            mptnmadf.ptaddr1.as("dpptaddr1"),
                            mptnmadf.ptaddr2.as("dpptaddr2"),
                            dropoff.deposcd.as("dpposcd"),
                            // 위,경도 DB field 위치가 반대로 되어있어서, 임시로 반대로 입력받음 => 241216 정상화
                            dropoff.destlat.as("dplatit"),
                            dropoff.destlon.as("dplongi"),
                            // dropoff.destlat.as("dplongi"),
                            // dropoff.destlon.as("dplatit"),
                            oetmhd.cargoty.as("cargoty"),
                            oetmhd.opkcate.as("opkcate"),
                            oetmhd.opktype.as("opktype"),
                            oetmhd.onsidty.as("onsidty"),
                            oetmhd.opvhton.as("ordervhton"),
                            oetmhd.opakqty.as("opakqty"),
                            oetmhd.pakwidh.as("pakwidh"),
                            oetmhd.pakdept.as("pakdept"),
                            oetmhd.pakheig.as("pakheig"),
                            oetmhd.pkrweig.as("pkrweig"),
                            oetmhd.carrkey.as("carrkey"),
                            oetmhd.turgtyn.as("turgtyn"),
                            oetmhd.botskyn.as("botskyn"),
                            oetmhd.topskyn.as("topskyn"),
                            pickup.unavat1.as("pkavat1"),
                            pickup.unavat2.as("pkavat2"),
                            pickup.unavat3.as("pkavat3"),
                            dropoff.unavat1.as("dpavat1"),
                            dropoff.unavat2.as("dpavat2"),
                            dropoff.unavat3.as("dpavat3"),
                            mptnmapu.ptnamlc.as("ptnamlc"),
                            mptnmadf.ptnamlc.as("cunamlc"),
                            mskuma.skudesc.as("skudesc"),
                            oetmit.saatc07.as("trctname"),
                            oetmit.saatc08.as("trctnum"),
                            oetmit.rgitqty.as("rgitqty"),
                            mptnmadf.ptnrtyp.as("ptnrtyp"),
                            mptnmapu.ptnrtyp.as("puptnrty"),
                            GroupBy.list(Projections.fields(ShipmentTargetDAO.TargetItem.class,
                                oetmit.oetmsit.as("oetmsit"),
                                oetmit.slasndt.as("slasndt")
                            )).as("oetmit")
                        ))
                );

        return result;

    }

    public ShipmentPlan findShipmentPlan(final String ptnrkey,
                                        @NotBlank final String compkey,
                                        @NotBlank final String shpplky) {

        return queryFactory
                .selectFrom(shipmentPlan)
                .leftJoin(shipmentPlan.shipmentList, shipment)
                .fetchJoin()
                .where(
                    shipmentPlan.manlshp.eq(false),
                    eqCompkey(compkey),
                    eqPtnrkey(ptnrkey),
                    eqShpplky(shpplky)
                )
                .fetchFirst();
    }

    public ShipmentPlanSimpleInfo findShipmentPlanSimpleInfo(@NotBlank final String compkey,
                                                             @NotBlank final String shpplky) {
        QShipmentPlan shpmtp = QShipmentPlan.shipmentPlan;

        return queryFactory
                .select(Projections.fields(ShipmentPlanSimpleInfo.class,
                        shpmtp.compkey, shpmtp.shpplky, shpmtp.ptnrkey, shpmtp.shpmtdt, shpmtp.roundno,
                        shpmtp.simulop, shpmtp.simulat, shpmtp.fixedat, shpmtp.showTooltip, shpmtp.manlshp
                ))
                .from(shpmtp)
                .where(
                        shpmtp.compkey.eq(compkey),
                        shpmtp.shpplky.eq(shpplky)
                )
                .fetchFirst();
    }

    public List<ShipmentPlanCheckMadeDTO> findShipmentPlanByDateRound(final String ptnrkey,
                                                                      @NotBlank final String compkey,
                                                                      @NotNull final LocalDate uldrqdt,
                                                                      @NotNull final Integer roundno) {

        final QShipmentPlan pl = QShipmentPlan.shipmentPlan;

        final Predicate[] predicates = {
            pl.deletat.isNull(),
            pl.manlshp.eq(false),
            StringUtils.isNotEmpty(ptnrkey) ? pl.ptnrkey.eq(ptnrkey) : Expressions.FALSE,
            StringUtils.isNotEmpty(compkey) ? pl.compkey.eq(compkey) : Expressions.FALSE,
            Objects.nonNull(uldrqdt) ? pl.shpmtdt.eq(uldrqdt) : Expressions.FALSE,
            pl.roundno.eq(roundno)
        };

        return queryFactory
            .select(Projections.fields(ShipmentPlanCheckMadeDTO.class,
                pl.compkey.as("compkey"),
                new CaseBuilder()
                    .when(pl.shpplky.isNull().or(pl.shpplky.isEmpty()))
                    .then(false)
                    .otherwise(true)
                    .as("ismade"),
                pl.showTooltip.as("showTooltip"),
                pl.shpmtdt.as("shpmtdt"),
                pl.roundno.as("roundno"),
                pl.simulat.as("simulat"),
                pl.fixedat.as("fixedat"),
                pl.shpplky.as("shpplky")
            ))
            .from(pl)
            .where(predicates)
            .fetch();
    }

    /*
    public List<ShipmentPlanCheckMadeDTO> findShipmentPlanByDateTime(final String ptnrkey,
                                                                @NotBlank final String compkey,
                                                                @NotNull final LocalDate uldrqdt,
                                                                @NotNull final LocalTime reqtime) {
        // check Plan by Date , Time
        Integer roundno = ShipmentUtils.getRoundByTime(reqtime);
        log.info("find ShipmentPlanByDateTime, Get Round : {} {} / {}", uldrqdt, reqtime, roundno);
        return this.findShipmentPlanByDateRound(ptnrkey, compkey, uldrqdt, roundno);
    }
    */

    public boolean existShipmentPlan(@NotBlank final String compkey,
                                     @NotBlank final String shpplky) {

        final ShipmentPlan result = queryFactory
                .selectFrom(shipmentPlan)
                .where(
                    eqCompkey(compkey),
                    eqShpplky(shpplky)
                )
                .fetchFirst();

        return Objects.nonNull(result);
    }

    public Page<ShipmentPlan> findShipmentPlanPage(@NotBlank final String compkey,
                                                   @Nullable Pageable pageable) {

        pageable = Optional.ofNullable(pageable).orElseGet(Pageable::unpaged);
//        pageable = Objects.requireNonNullElseGet(pageable, Pageable::unpaged);
//        pageable = ObjectUtils.getIfNull(pageable, Pageable::unpaged);

        final JPAQuery<ShipmentPlan> selectQuery = queryFactory
                .selectFrom(shipmentPlan)
                .leftJoin(shipmentPlan.shipmentList, shipment)
                .fetchJoin()
                .where(
                    eqCompkey(compkey)
                )
                .distinct();

        if (pageable.getSort().isSorted()) {
            selectQuery
                .orderBy(QueryDslUtils.getOrderSpecifiersByPath(pageable.getSort(),
                        ShipmentPlan.class,
                        "shipmentPlan"));
        }

        if (pageable.isPaged()) {
            selectQuery
                    .offset(pageable.getOffset())
                    .limit(pageable.getPageSize());
        }

        final List<ShipmentPlan> content = selectQuery.fetch();

        final JPAQuery<Long> countQuery = queryFactory
                .select(shipmentPlan.count())
                .from(shipmentPlan);

        final Page<ShipmentPlan> page = PageableExecutionUtils.getPage(content,
                pageable,
                countQuery::fetchOne);

        return page;
    }

    public List<ShipmentResultCountDummyDTO> findShipmentResultCount(final String ptnrkey,
                                                                     @NotBlank final String compkey,
                                                                     @NotNull final LocalDate uldrqdt,
                                                                     @NotNull final Integer roundno) {

        List<ShipmentResultCountDummyDTO> result;

        final QShipment mt = QShipment.shipment;
        final QShipmentPlan pl = QShipmentPlan.shipmentPlan;
        final QShipmentSection df = QShipmentSection.shipmentSection;
        // final QMptnma ma = QMptnma.mptnma;
        // final QShipmentSection pu = QShipmentSection.shipmentSection;

        result = queryFactory
            .select(Projections.fields(ShipmentResultCountDummyDTO.class,
                pl.compkey.as("compkey"),
                pl.shpplky.as("shpplky"),
                pl.fixedat.as("fixedat"),
                mt.shpmtky.as("shpmtky"),
                mt.cargoty.as("cargoty"),
                mt.vhctncd.as("vhctncd"),
                // mt.vhctncd.count().castToNum(Integer.class).as("vhctncd_count"),
                mt.ptnrkey.as("ptnrkey"),
                mt.ptnamlc.as("ptnamlc"),
                mt.ptnrtyp.as("ptnrtyp")
            ))
            .from(pl)
                .leftJoin(mt).on(mt.shipmentPlan.eq(pl).and(mt.deletat.isNull()))
                // .leftJoin(df).on(df.shipment.eq(mt).and(df.deletat.isNull()).and(df.vsttype.eq(VisitTypeEnum.DROPOFF)))
            .where(
                pl.deletat.isNull(),
                pl.manlshp.eq(false),
                pl.ptnrkey.eq(ptnrkey),
                pl.roundno.eq(roundno),
                mt.fixedat.isNull(),
                StringUtils.isNotEmpty(compkey) ? shipmentPlan.compkey.eq(compkey) : Expressions.FALSE,
                Objects.nonNull(uldrqdt) ? pl.shpmtdt.eq(uldrqdt) : Expressions.FALSE
            )
            // .groupBy(df.ptnrkey, mt.shpmtky, df.vhctncd)
            // .groupBy(mt.ptnrkey)
            .fetch();

        return result;
    }

    public List<ShipmentResultListDummyDTO> findShipmentResultPtnrkey(
                                        final String authPtnrkey,
                                        @NotBlank final String compkey,
                                        @NotNull final LocalDate uldrqdt,
                                        @NotNull final Integer roundno,
                                        final String ptnrkey) {

        List<ShipmentResultListDummyDTO> result;

        final QShipment mt = QShipment.shipment;
        final QShipmentPlan pl = QShipmentPlan.shipmentPlan;
        final QShipmentSection sc = new QShipmentSection("sc");

        result = queryFactory
            .select(Projections.fields(ShipmentResultListDummyDTO.class,
                pl.compkey.as("compkey"),
                pl.shpplky.as("shpplky"),
                mt.shpmtky.as("shpmtky"),
                mt.cargoty.as("cargoty"),
                mt.vhctncd.as("vhctncd"),
                mt.ptnrkey.as("ptnrkey"),
                mt.ptnamlc.as("ptnamlc"),
                mt.ptnrtyp.as("ptnrtyp"),
                mt.loadrat.as("loadrat"), // -- 적재율
                mt.estimatedfee.as("estimatedfee"), // -- 예상운임비
                sc.vstordr.as("vstordr"), // 운행순서
                sc.vsttype.as("vsttype"), // -- 상하차 TYPE
                sc.denamlc.as("piaddr"), // -- 상하차지 명칭
                sc.destkey.as("destkey"), // -- 도착지 key
                sc.ptnamlc.as("scptnamlc"), // -- 도착지 업체이름
                sc.oetmsky.as("oetmsky"), // -- 운송주문번호
                sc.opakqty.as("opakqty"), // 팔레트 박스 수량
                sc.pkrweig.as("pkrweig"), // 중량
                pl.shpmtdt.as("shpmtdt") // -- 운송일
            ))
            .from(sc)
                .leftJoin(mt).on(mt.shipment.eq(sc.shipment).and(mt.deletat.isNull()))
                .leftJoin(pl).on(pl.shipmentPlan.eq(mt.shipmentPlan).and(pl.deletat.isNull()))
            .where(
                sc.deletat.isNull(),
                pl.deletat.isNull(),
                pl.ptnrkey.eq(authPtnrkey),
                pl.roundno.eq(roundno),
                Objects.nonNull(uldrqdt) ? pl.shpmtdt.eq(uldrqdt) : Expressions.FALSE,
                StringUtils.isNotEmpty(ptnrkey) ? mt.ptnrkey.eq(ptnrkey) : Expressions.FALSE,
                StringUtils.isNotEmpty(compkey) ? shipmentPlan.compkey.eq(compkey) : Expressions.FALSE
            )
            .orderBy(sc.vstordr.asc())
            .fetch();

        return result;
    }

    public List<ShipmentMapDummyDTO> findShipmentMapResultList(final String ptnrkey,
                                                    @NotBlank final String compkey,
                                                    @NotNull final LocalDate uldrqdt,
                                                    @NotNull final Integer roundno) {

        List<ShipmentMapDummyDTO> result;

        final QShipment mt = QShipment.shipment;
        final QShipmentPlan pl = QShipmentPlan.shipmentPlan;
        final QShipmentSection pu = new QShipmentSection("pu");
        final QShipmentSection df = new QShipmentSection("df");

        result = queryFactory
            .select(Projections.fields(ShipmentMapDummyDTO.class,
                mt.shpmtky.as("shpmtky"), // -- Shipment No.
                mt.cargoty.as("cargoty"), // -- 단,혼적
                mt.vhctncd.as("vhctncd"), // -- 차량톤수
                mt.loadrat.as("loadrat"), // -- 적재율

                pu.ptnamlc.as("puptnamlc"), // -- 파트너명칭
                pu.ptnrkey.as("puptnrkey"), // -- 협력업체키
                pu.ptnrtyp.as("puptnrtyp"), // -- 협력업체타입
                pu.destkey.as("pudestkey"), // -- 상하차 key
                pu.denamlc.as("pudenamlc"), // -- 상하차 명칭
                pu.decoord.as("pudecoord"), // -- 상하차 위치
                pu.vsttype.as("puvsttype"), // -- 상하차 구분
                pu.oetmsky.as("puoetmsky"), // -- 운송주문번호

                df.ptnamlc.as("dfptnamlc"), // -- 파트너명칭
                df.ptnrkey.as("dfptnrkey"), // -- 협력업체키
                df.ptnrtyp.as("dfptnrtyp"), // -- 협력업체타입
                df.destkey.as("dfdestkey"), // -- 상하차 key
                df.denamlc.as("dfdenamlc"), // -- 상하차 명칭
                df.decoord.as("dfdecoord"), // -- 상하차 위치
                df.vsttype.as("dfvsttype"), // -- 상하차 구분
                df.oetmsky.as("dfoetmsky") // -- 운송주문번호
            ))
            .from(pl)
                .leftJoin(mt).on(mt.shipmentPlan.eq(pl).and(mt.deletat.isNull()))
                .leftJoin(pu).on(pu.shipment.eq(mt).and(pu.deletat.isNull()).and(pu.vsttype.eq(VisitTypeEnum.PICKUP)))
                .leftJoin(df).on(df.oetmsky.eq(pu.oetmsky).and(df.deletat.isNull()).and(df.vsttype.eq(VisitTypeEnum.DROPOFF)))
            .where(
                pl.deletat.isNull(),
                pl.ptnrkey.eq(ptnrkey),
                pl.roundno.eq(roundno),
                StringUtils.isNotEmpty(compkey) ? shipmentPlan.compkey.eq(compkey) : Expressions.FALSE,
                Objects.nonNull(uldrqdt) ? pl.shpmtdt.eq(uldrqdt) : Expressions.FALSE
            )
            .fetch();

        return result;
    }

    public List<ShipmentPlanFixDummyDTO> findShipmentFixPrevList(@NotBlank final String compkey,
                                                                @NotNull final String shpplky) {

        List<ShipmentPlanFixDummyDTO> result;

        final QShipment mt = QShipment.shipment;
        final QShipmentPlan pl = QShipmentPlan.shipmentPlan;
        final QShipmentSection pu = new QShipmentSection("pu");
        final QShipmentSection df = new QShipmentSection("df");

        final Predicate[] predicates = {
            mt.deletat.isNull(),
            StringUtils.isNotEmpty(shpplky) ? pl.shpplky.eq(shpplky) : Expressions.FALSE,
            StringUtils.isNotEmpty(compkey) ? shipmentPlan.compkey.eq(compkey) : Expressions.FALSE
        };

        result = queryFactory
            .select(Projections.fields(ShipmentPlanFixDummyDTO.class,
                mt.shpmtky.as("shpmtky"), // -- Shipment No.
                mt.cargoty.as("cargoty"), // -- 단,혼적
                mt.vhctncd.as("vhctncd"), // -- 차량톤수
                mt.loadrat.as("loadrat"), // -- 적재율
                mt.ptnamlc.as("ptnamlc"), // -- 파트너명칭
                mt.ptnrkey.as("ptnrkey"), // -- 협력업체키
                mt.ptnrtyp.as("ptnrtyp"), // -- 협력업체타입
                mt.vehcost.as("vehcost"), // -- 용차 섭외 금액
                mt.totalcost.as("totalcost"), // -- 총운송비용
                mt.estimatedfee.as("estimatedfee"), // -- 예상운임비
                pu.shpscky.as("puscky"), // -- 운송구간 번호 Pickup
                df.shpscky.as("dfscky"), // -- 운송구간 번호 Dropoff
                pl.shpmtdt.as("shpmtdt"), // -- 운송일자
                pl.roundno.as("roundno"), // -- 회차
                pl.shpplky.as("shpplky") // -- 운송계획 번호
            ))
            .from(pl)
                .leftJoin(mt).on(mt.shipmentPlan.eq(pl).and(mt.deletat.isNull()))
                .leftJoin(pu).on(pu.shipment.eq(mt).and(pu.deletat.isNull()).and(pu.vsttype.eq(VisitTypeEnum.PICKUP)))
                .leftJoin(df).on(df.oetmsky.eq(pu.oetmsky).and(df.deletat.isNull()).and(df.vsttype.eq(VisitTypeEnum.DROPOFF)))
            .where(predicates)
            .fetch();

        return result;
    }

    public List<ShipmentPlanChangedDummyDTO> findChangedShipmentPlan(final String ptnrkey,
                                                                    @NotBlank final String compkey,
                                                                    final LocalDate today,
                                                                    final LocalDate endday) {

        List<ShipmentPlanChangedDummyDTO> result;

        final QShipment mt = QShipment.shipment;
        final QShipmentPlan pl = QShipmentPlan.shipmentPlan;
        final QShipmentSection sc = new QShipmentSection("sc");
        final QShipmentPlanOrderHistory odh = new QShipmentPlanOrderHistory("odh");

        final Predicate[] predicates = {
            // 아래값이 모두 null 이면 제외, 한개라도 값이 있으면 포함
            (odh.opkcaten.isNotNull().and(odh.opkcaten.ne(odh.opkcateo))) // 포장형태 (신)
                .or(odh.opktypen.isNotNull().and(odh.opktypen.ne(odh.opktypeo))) // 포장타입 (신)
                .or(odh.tshitstn.isNotNull().and(odh.tshitstn.ne(odh.tshitsto))) // 운송오더 상태 (신)
                .or(odh.opakqtyn.isNotNull().and(odh.opakqtyn.ne(odh.opakqtyo))) // 팔레트 박스 수량 (신)
                .or(odh.uldrqdtn.isNotNull().and(odh.uldrqdtn.ne(odh.uldrqdto))) // 납품예정일 (신)
                .or(odh.cargotyn.isNotNull().and(odh.cargotyn.ne(odh.cargotyo))) // 단,혼적
            ,
            pl.fixedat.isNull(), // 확정된것은 제외
            pl.deletat.isNull(),
            pl.ptnrkey.eq(ptnrkey),
            pl.shpmtdt.between(today, endday),
            odh.changat.isNotNull().and(odh.changat.after(pl.simulat)),
            StringUtils.isNotEmpty(compkey) ? pl.compkey.eq(compkey) : Expressions.FALSE
        };

        result = queryFactory
            .select(Projections.fields(ShipmentPlanChangedDummyDTO.class,
                pl.shpmtdt.as("shpmtdt"), // 일자
                pl.roundno.as("roundno"), // 회차
                mt.shpmtky.as("shpmtky"), // Shipment No.
                mt.cargoty.as("cargoty"), // 운송화물방식
                sc.ptnrkey.as("ptnrkey"), // 협력업체키
                sc.ptnamlc.as("ptnamlc"), // 협력업체 명칭
                sc.ptnrtyp.as("ptnrtyp"), // 협력업체 타입
                sc.tshitst.as("tshitst"), // 운송오더상태 
                sc.opkcate.as("opkcate"), // 포장형태
                sc.opktype.as("opktype"), // 포장타입
                sc.onsidty.as("onsidty"), // 포장ONE-SIDE 타입
                sc.opakqty.as("opakqty"), // 팔레트 박스 수량
                odh.oetmsky.as("oetmsky"), // 운송주문번호
                odh.changat.as("changat"), // 변경일시
                odh.tshitsto.as("tshitsto"), // 운송오더 상태 (구)
                odh.tshitstn.as("tshitstn"), // 운송오더 상태 (신)
                odh.uldrqdto.as("uldrqdto"), // To고객 도착예정일 (구)
                odh.uldrqdtn.as("uldrqdtn"), // To고객 도착예정일 (신)
                odh.opkcateo.as("opkcateo"), // 포장형태 (구)
                odh.opkcaten.as("opkcaten"), // 포장형태 (신)
                odh.opktypeo.as("opktypeo"), // 포장타입 (구)
                odh.opktypen.as("opktypen"), // 포장타입 (신)
                odh.onsidtyo.as("onsidtyo"), // 포장ONE-SIDE 타입 (구)
                odh.onsidtyn.as("onsidtyn"), // 포장ONE-SIDE 타입 (신)
                odh.opakqtyo.as("opakqtyo"), // 팔레트 박스 수량 (구)
                odh.opakqtyn.as("opakqtyn"), // 팔레트 박스 수량 (신)
                odh.cargotyo.as("cargotyo"), // 운송화물방식 (OLD)
                odh.cargotyn.as("cargotyn"), // 운송화물방식 (NEW)
                pl.shpplky.as("shpplky") // 운송계획번호
            ))
            .from(pl)
                .leftJoin(mt).on(mt.shipmentPlan.eq(pl).and(mt.deletat.isNull()))
                .leftJoin(sc).on(sc.shipment.eq(mt).and(sc.deletat.isNull()))
                .leftJoin(odh).on(odh.oetmsky.eq(sc.oetmsky).and(sc.vsttype.eq(VisitTypeEnum.DROPOFF)))
            .where(predicates)
            .orderBy(new OrderSpecifier<>(Order.ASC, pl.shpmtdt))
            .fetch();

        return result;
    }

    public List<ShipmentPlanChangedDummyDTO> findChanged3ShipmentPlan(final String ptnrkey,
                                                                    @NotBlank final String compkey,
                                                                    final LocalDate today,
                                                                    final ShipmentPlanCheckMadeDTO shPlan
                                                                    ) {
        if ( shPlan == null ) return null;
        List<ShipmentPlanChangedDummyDTO> result;

        final QShipment mt = QShipment.shipment;
        final QShipmentPlan pl = QShipmentPlan.shipmentPlan;
        final QShipmentSection sc = new QShipmentSection("sc");
        final QOetmhi odh = new QOetmhi("odh");

        LocalDate rqdt = shPlan.getSimulat().toLocalDate();
        LocalTime rqtm = shPlan.getSimulat().toLocalTime();
        log.info("find Changed3 ShipmentPlan, shpmtdt:{}/credatetime:{},{}", shPlan.getShpmtdt(), rqdt, rqtm);

        final Predicate[] predicates = {
            pl.fixedat.isNull(), // 확정된것은 제외
            pl.deletat.isNull(),
            pl.ptnrkey.eq(ptnrkey),
            pl.shpmtdt.eq(today),
            odh.credate.after(rqdt).or(odh.credate.eq(rqdt).and(odh.cretime.after(rqtm)))
                .or(
                    odh.lmodate.after(rqdt).or(odh.lmodate.eq(rqdt).and(odh.lmotime.after(rqtm)))
                ),
            StringUtils.isNotEmpty(compkey) ? pl.compkey.eq(compkey) : Expressions.FALSE
        };

        result = queryFactory
            .select(Projections.fields(ShipmentPlanChangedDummyDTO.class,
                pl.shpmtdt.as("shpmtdt"), // 일자
                pl.roundno.as("roundno"), // 회차
                mt.shpmtky.as("shpmtky"), // Shipment No.
                mt.cargoty.as("cargoty"), // 운송화물방식
                sc.ptnrkey.as("ptnrkey"), // 협력업체키
                sc.ptnamlc.as("ptnamlc"), // 협력업체 명칭
                sc.ptnrtyp.as("ptnrtyp"), // 협력업체 타입
                sc.tshitst.as("tshitst"), // 운송오더상태 
                sc.opkcate.as("opkcate"), // 포장형태
                sc.opktype.as("opktype"), // 포장타입
                sc.onsidty.as("onsidty"), // 포장ONE-SIDE 타입
                sc.opakqty.as("opakqty"), // 팔레트 박스 수량
                odh.oetmsky.as("oetmsky"), // 운송주문번호
                odh.tshitst.as("tshitstn"), // 운송오더 상태 (신)
                odh.uldrqdt.as("uldrqdtn"), // To고객 도착예정일 (신)
                odh.opkcate.as("opkcaten"), // 포장형태 (신)
                odh.opktype.as("opktypen"), // 포장타입 (신)
                odh.onsidty.as("onsidtyn"), // 포장ONE-SIDE 타입 (신)
                odh.opakqty.as("opakqtyn"), // 팔레트 박스 수량 (신)
                odh.cargoty.as("cargotyn"), // 운송화물방식 (NEW)
                pl.shpplky.as("shpplky") // 운송계획번호
            ))
            .from(pl)
                .leftJoin(mt).on(mt.shipmentPlan.eq(pl).and(mt.deletat.isNull()))
                .leftJoin(sc).on(sc.shipment.eq(mt).and(sc.deletat.isNull()))
                .leftJoin(odh).on(odh.oetmsky.eq(sc.oetmsky).and(sc.vsttype.eq(VisitTypeEnum.DROPOFF)))
            .where(predicates)
            .orderBy(new OrderSpecifier<>(Order.ASC, pl.shpmtdt))
            .fetch();

        return result;
    }

    @Deprecated
    public List<ShipmentPlanChangedSimpleDTO> findSimpleChangedShipmentPlan(final String ptnrkey,
                                                                    @NotBlank final String compkey,
                                                                    final LocalDate today,
                                                                    final LocalDate endday,
                                                                    final LocalDate uldrqdt,
                                                                    final Integer roundno) {

        List<ShipmentPlanChangedSimpleDTO> result;

        final QShipment mt = QShipment.shipment;
        final QShipmentPlan pl = QShipmentPlan.shipmentPlan;
        final QShipmentSection sc = new QShipmentSection("sc");
        final QOrderChangeHistory odh = new QOrderChangeHistory("odh");

        final Predicate[] predicates = {
            pl.fixedat.isNull(), // 확정된것은 제외
            pl.deletat.isNull(),
            pl.ptnrkey.eq(ptnrkey),
            // pl.shpmtdt.between(today, endday),
            pl.shpmtdt.eq(uldrqdt),
            pl.roundno.eq(roundno),
            odh.changat.after(pl.simulat),
            StringUtils.isNotEmpty(compkey) ? pl.compkey.eq(compkey) : Expressions.FALSE
        };

        result = queryFactory
            .select(Projections.fields(ShipmentPlanChangedSimpleDTO.class,
                pl.shpmtdt.as("shpmtdt"), // 일자
                pl.roundno.as("roundno"), // 회차
                mt.shpmtky.as("shpmtky"), // Shipment No.
                odh.oetmsky.as("oetmsky"),
                odh.tshitsto.as("tshitsto"), // 운송오더 상태 (구)
                odh.tshitstn.as("tshitstn"), // 운송오더 상태 (신)
                odh.opakqtyo.as("opakqtyo"), // 팔레트 박스 수량 (구)
                odh.opakqtyn.as("opakqtyn"), // 팔레트 박스 수량 (신)
                pl.shpplky.as("shpplky") // 운송계획번호
            ))
            .from(pl)
                .leftJoin(mt).on(mt.shipmentPlan.eq(pl).and(mt.deletat.isNull()))
                .leftJoin(sc).on(sc.shipment.eq(mt).and(sc.deletat.isNull()))
                .leftJoin(odh).on(odh.oetmsky.eq(sc.oetmsky).and(sc.vsttype.eq(VisitTypeEnum.DROPOFF)))
            .where(predicates)
            .orderBy(new OrderSpecifier<>(Order.ASC, pl.shpmtdt))
            .fetch();

        return result;
    }

    public List<ShipmentPlanChangedSimpleDTO> findSimpleChanged2ShipmentPlan(final String ptnrkey,
                                                                    @NotBlank final String compkey,
                                                                    final LocalDate today,
                                                                    final LocalDate endday,
                                                                    final LocalDate uldrqdt,
                                                                    final Integer roundno) {

        List<ShipmentPlanChangedSimpleDTO> result;

        final QShipmentPlan pl = QShipmentPlan.shipmentPlan;
        final QShipmentPlanOrderHistory odh = new QShipmentPlanOrderHistory("odh");

        final Predicate[] predicates = {
            // 아래 5가지 값이 모두 null 이면 제외, 한개라도 값이 있으면 포함
            (odh.opkcaten.isNotNull().and(odh.opkcaten.ne(odh.opkcateo))) // 포장형태 (신)
                .or(odh.opktypen.isNotNull().and(odh.opktypen.ne(odh.opktypeo))) // 포장타입 (신)
                .or(odh.tshitstn.isNotNull().and(odh.tshitstn.ne(odh.tshitsto))) // 운송오더 상태 (신)
                .or(odh.opakqtyn.isNotNull().and(odh.opakqtyn.ne(odh.opakqtyo))) // 팔레트 박스 수량 (신)
                .or(odh.uldrqdtn.isNotNull().and(odh.uldrqdtn.ne(odh.uldrqdto))) // 납품예정일 (신)
                .or(odh.cargotyn.isNotNull().and(odh.cargotyn.ne(odh.cargotyo))) // 단,혼적
            ,
            odh.changat.isNotNull(),
            odh.changat.after(pl.simulat)
        };

        result = queryFactory
            .select(Projections.fields(ShipmentPlanChangedSimpleDTO.class,
                pl.shpplky.as("shpplky"),
                pl.shpmtdt.as("shpmtdt"),
                pl.roundno.as("roundno"),

                odh.oetmsky.as("oetmsky"),

                odh.tshitsto.as("tshitsto"), // 운송오더 상태 (구)
                odh.tshitstn.as("tshitstn"), // 운송오더 상태 (신)

                odh.uldrqdto.as("uldrqdto"), // 납품예정일 (구)
                odh.uldrqdtn.as("uldrqdtn"), // 납품예정일 (신)

                odh.cargotyo.as("cargotyo"), // 단,혼적 (구)
                odh.cargotyn.as("cargotyn"), // 단,혼적 (신)

                odh.opakqtyo.as("opakqtyo"), // 팔레트 박스 수량 (구)
                odh.opakqtyn.as("opakqtyn"), // 팔레트 박스 수량 (신)
                odh.opkcateo.as("opkcateo"), // 포장형태 (구)
                odh.opkcaten.as("opkcaten"), // 포장형태 (신)
                odh.opktypeo.as("opktypeo"), // 포장타입 (구)
                odh.opktypen.as("opktypen"), // 포장타입 (신)

                odh.onsidtyo.as("onsidtyo"), // (구)
                odh.onsidtyn.as("onsidtyn"), // (신)

                odh.changat
            ))
            .from(odh)
                .leftJoin(pl).on(pl.shpmtdt.eq(uldrqdt)
                    .and(pl.roundno.eq(roundno))
                    .and(pl.ptnrkey.eq(ptnrkey))
                    .and(pl.compkey.eq(compkey))
                    .and(pl.deletat.isNull())
                    .and(pl.fixedat.isNull()))
            .where(predicates)
            .distinct()
            .orderBy(new OrderSpecifier<>(Order.DESC, odh.changat))
            .fetch();

        return result;
    }

    public List<ShipmentPlanChangedSimpleDTO> findSimpleChanged3ShipmentPlan(final String ptnrkey,
                                                                             @NotBlank final String compkey,
                                                                             final ShipmentPlanCheckMadeDTO shPlan) {
        if ( shPlan == null ) return null;

        List<ShipmentPlanChangedSimpleDTO> result = null;

        final QOetmhi odh = new QOetmhi("odh");

        LocalDate rqdt = shPlan.getSimulat().toLocalDate();
        LocalTime rqtm = shPlan.getSimulat().toLocalTime();
        log.info("find Changed3 ShipmentPlan, shpmtdt:{}/credatetime:{},{}", shPlan.getShpmtdt(), rqdt, rqtm);

        final Predicate[] predicates = {
            odh.credate.after(rqdt).or(odh.credate.eq(rqdt).and(odh.cretime.goe(rqtm)))
            .or(
                odh.lmodate.after(rqdt).or(odh.lmodate.eq(rqdt).and(odh.lmotime.goe(rqtm)))
            )
            .and(odh.carrkey.eq(ptnrkey))
            .and(odh.compkey.eq(compkey))
        };

        result = queryFactory
            .select(Projections.fields(ShipmentPlanChangedSimpleDTO.class,
                odh.oetmsky.as("oetmsky"),
                odh.tshitst.as("tshitstn"), // 운송오더 상태
                odh.opakqty.as("opakqtyn"), // 팔레트 박스
                odh.opkcate.as("opkcaten"), // 포장형태
                odh.opktype.as("opktypen"), // 포장타입
                odh.onsidty.as("onsidtyn"), //
                odh.lodrqdt.as("lodrqdtn"), // 상차예정일
                odh.lodrqtm.as("lodrqtmn"), // 상차예정시
                odh.uldrqdt.as("uldrqdtn"), // 납품예정일
                odh.uldrqtm.as("uldrqtmn"), // 납품예정시
                odh.crudmod.as("crudmod"),
                odh.credate.as("credate"),
                odh.cretime.as("cretime"),
                odh.lmodate.as("lmodate"),
                odh.lmotime.as("lmotime"),
                odh.cargoty.as("cargotyn") // 단,혼적
            ))
            .from(odh)
            .where(predicates)
            .orderBy(new OrderSpecifier<>(Order.DESC, odh.oehissq))
            .fetch();

        return result;
    }

    public Integer updateOetmhdTmshpno(final String shmtky, final String oetmsky) {

        Integer result = 0;
        QOetmhd oetmhd = new QOetmhd("oetmhd");

        if ( shmtky == null || oetmsky == null ) return result;
        if ( shmtky.isBlank() || oetmsky.isBlank() ) return result;

        try {
            String modifyUser = " ";
            final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
            if (Objects.nonNull(authDTO)) {
                modifyUser = authDTO.getId();
            }

            // shipment 번호 저장
            queryFactory.update(oetmhd)
                .set(oetmhd.tmshpno, shmtky)
                .set(oetmhd.lmouser, modifyUser)
                .set(oetmhd.lmodate, LocalDate.now())
                .set(oetmhd.lmotime, LocalTime.now())
                .where(oetmhd.oetmsky.eq(oetmsky))
                .execute();

            result += 1;
        } catch (Exception ex) {
        }

        return result;
    }

    public Integer updateOetmhdTshitstPlanFix(List<String> oetmskyValues) {
        Integer result = 0;
        QOetmhd qOetmhd = QOetmhd.oetmhd;
        try {
            if ( oetmskyValues == null ) return 0;

            String modifyUser = " ";
            final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
            if (Objects.nonNull(authDTO)) {
                modifyUser = authDTO.getId();
            }

            // Tshitst (SHIP) 업데이트
            for (String oetmkey : oetmskyValues) {
                queryFactory.update(qOetmhd)
                    .set(qOetmhd.tshitst, OrderStatusEnum.SHIP)
                    .set(qOetmhd.lmouser, modifyUser)
                    .set(qOetmhd.lmodate, LocalDate.now())
                    .set(qOetmhd.lmotime, LocalTime.now())
                    .where(qOetmhd.oetmsky.eq(oetmkey))
                    .execute();
                result += 1;
            }
        } catch (Exception ex) {
        }
        return result;
    }

    public Integer updateOetmitTshitstPlanFix(List<String> oetmskyValues) {
        Integer result = 0;
        QOetmit qOetmit = QOetmit.oetmit;
        try {
            if ( oetmskyValues == null ) return 0;

            String modifyUser = " ";
            final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
            if (Objects.nonNull(authDTO)) {
                modifyUser = authDTO.getId();
            }

            // Tshitst (SHIP) 업데이트
            for (String oetmkey : oetmskyValues) {
                queryFactory.update(qOetmit)
                    .set(qOetmit.tshitst, OrderStatusEnum.SHIP)
                    .set(qOetmit.lmouser, modifyUser)
                    .set(qOetmit.lmodate, LocalDate.now())
                    .set(qOetmit.lmotime, LocalTime.now())
                    .where(qOetmit.oetmsky.eq(oetmkey))
                    .execute();
                result += 1;
            }
        } catch (Exception ex) {
        }
        return result;
    }

    public Integer updateOetmhdTmshpno(ShipmentPlan shipmentPlan) {

        Integer result = 0;
        QOetmhd qOetmhd = QOetmhd.oetmhd;

        if ( shipmentPlan == null ) return result;
        if ( shipmentPlan.getShipmentList() == null || shipmentPlan.getShipmentList().size() < 1 ) return result;

        String modifyUser = " ";
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            modifyUser = authDTO.getId();
        }

        LocalDate today = LocalDate.now();
        LocalTime nowTime = LocalTime.now();

        // shipment 번호 저장
        for (Shipment shipment : shipmentPlan.getShipmentList()) {
            String shipNo = shipment.getShpmtky();
            // 각 shipmentSection의 oetmsky를 중복 제거한 리스트
            List<String> oetmKeys = shipment.getShipmentSectionList().stream()
                    .map(ShipmentSection::getOetmsky)
                    .distinct()
                    .collect(Collectors.toList());

            if (oetmKeys.isEmpty()) {
                continue;
            }

            // 한 번의 UPDATE로 해당 키들 모두에 대해 shipNo 세팅
            long updated = queryFactory.update(qOetmhd)
                    .set(qOetmhd.tmshpno, shipNo)
                    .set(qOetmhd.lmouser, modifyUser)
                    .set(qOetmhd.lmodate, today)
                    .set(qOetmhd.lmotime, nowTime)
                    .where(qOetmhd.oetmsky.in(oetmKeys))
                    .execute();

            result += (int) updated;
        }

        return result;
    }

    public List<CarrierInfoDTO> getCarrierUserInfos() {

        QSusrma ur = QSusrma.susrma;

        final Predicate[] predicates = {
            ur.usertyp.eq(UserTypeEnum.CARRIER.getEnglish())
        };

        final List<CarrierInfoDTO> userList = queryFactory
                .select(Projections.fields(CarrierInfoDTO.class,
                    ur.compkey,
                    ur.useract,
                    ur.usertyp,
                    ur.ptnrkey
                ))
                .from(ur)
                .where(predicates)
                .fetch();

        return userList;
    }

    public Integer clearOetmhdTmshpno(final List<String> oetmskyValues) {

        Integer result = 0;
        QOetmhd qOetmhd = QOetmhd.oetmhd;
        if ( oetmskyValues == null || oetmskyValues.size() < 1 ) return result;

        BooleanExpression commonPredicate =
                qOetmhd.compkey.eq(TmsConstant.DEFAULT_COMPKEY)
                        // 수동배차 manuvhp 인 경우는 제외
                        .and(qOetmhd.manuvhp.isNull().or(qOetmhd.manuvhp.eq(0)).or(qOetmhd.manuvhp.eq(1)))
                        // OrderStatusEnum.REQUEST , SHIP 인 경우만 clear
                        .and(qOetmhd.tshitst.eq(OrderStatusEnum.REQUEST).or(qOetmhd.tshitst.eq(OrderStatusEnum.SHIP)));

        String modifyUser = " ";
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            modifyUser = authDTO.getId();
        }

        LocalDate today = LocalDate.now();
        LocalTime nowTime = LocalTime.now();
        // 기존 shipmentNo 번호 제거
        long updatedCount = queryFactory.update(qOetmhd)
                .set(qOetmhd.tmshpno, Expressions.nullExpression())
                .set(qOetmhd.tshitst, OrderStatusEnum.REQUEST)
                .set(qOetmhd.lmouser, modifyUser)
                .set(qOetmhd.lmodate, today)
                .set(qOetmhd.lmotime, nowTime)
                .where(commonPredicate
                        .and(qOetmhd.oetmsky.in(oetmskyValues)))
                .execute();

        return (int) updatedCount;
    }

    public Integer clearOetmitTmshpno(final List<String> oetmskyValues) {

        Integer result = 0;
        QOetmit qOetmit = QOetmit.oetmit;
        if ( oetmskyValues == null || oetmskyValues.size() < 1 ) return result;

        BooleanExpression predicate =
                qOetmit.tshitst.eq(OrderStatusEnum.REQUEST).or(qOetmit.tshitst.eq(OrderStatusEnum.SHIP))
                        // 수동배차 manuvhp 인 경우는 제외
                .and(qOetmit.oetmhd.manuvhp.isNull().or(qOetmit.oetmhd.manuvhp.eq(0)).or(qOetmit.oetmhd.manuvhp.eq(1)))
                .and(qOetmit.oetmsky.in(oetmskyValues));

        String modifyUser = " ";
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            modifyUser = authDTO.getId();
        }

        // 기존 shipmentNo 번호 제거
        LocalDate today = LocalDate.now();
        LocalTime nowTime = LocalTime.now();

        long updatedCount = queryFactory.update(qOetmit)
                .set(qOetmit.tshitst, OrderStatusEnum.REQUEST)
                .set(qOetmit.lmouser, modifyUser)
                .set(qOetmit.lmodate, today)
                .set(qOetmit.lmotime, nowTime)
                .where(predicate)
                .execute();

        return (int) updatedCount;
    }

    private BooleanExpression eqCompkey(final String compkey) {

        if (StringUtils.isNotEmpty(compkey)) {
            return shipmentPlan.compkey.eq(compkey);
        } else {
            return Expressions.FALSE;
        }
    }

    private BooleanExpression eqShpplky(final String shpplky) {

        if (StringUtils.isNotEmpty(shpplky)) {
            return shipmentPlan.shpplky.eq(shpplky);
        } else {
            return Expressions.FALSE;
        }
    }

    private BooleanExpression eqPtnrkey(final String ptnrkey) {

        if (StringUtils.isNotEmpty(ptnrkey)) {
            return shipmentPlan.ptnrkey.eq(ptnrkey);
        } else {
            return Expressions.FALSE;
        }
    }

    /*
    private BooleanExpression eqUldrqdt(final LocalDate uldrqdt) {

        if (Objects.nonNull(uldrqdt)) {
            return oetmhd.uldrqdt.eq(uldrqdt);
        } else {
            return Expressions.FALSE;
        }
    }
    */

    private Predicate roundPredicate(QOetmhd oetmhd, final LocalDate reqDate, final Integer roundno) {
        Predicate roundTimePredicate = Expressions.TRUE;

        final LocalTime middle = LocalTime.of(12, 0);

        if ( Objects.nonNull(reqDate) ) {
            if ( roundno == 1 ) {
                // 하차일은 reqDate
                // 상차일은 reqDate 이전
                roundTimePredicate = (
                        oetmhd.uldrqdt.eq(reqDate)
                                .and(oetmhd.lodrqdt.before(reqDate))
                );
            } else if ( roundno == 2 ) {
                // 하차일은 reqDate
                // 상차일은 reqDate
                // 상차시는 middle 이전
                roundTimePredicate = (
                        oetmhd.uldrqdt.eq(reqDate)
                                .and(oetmhd.lodrqdt.eq(reqDate))
                                .and(oetmhd.lodrqtm.before(middle))
                );
            } else if ( roundno == 3 ) {
                // 하차일은 reqDate
                // 상차일은 reqDate
                // 상차시는 middle 이후
                roundTimePredicate = (
                        oetmhd.uldrqdt.eq(reqDate)
                                .and(oetmhd.lodrqdt.eq(reqDate))
                                .and(
                                        (oetmhd.lodrqtm.after(middle))
                                                .or(oetmhd.lodrqtm.eq(middle))
                                )
                );
            }
        } else {
            return Expressions.FALSE;
        }

        return roundTimePredicate;
    }

}
