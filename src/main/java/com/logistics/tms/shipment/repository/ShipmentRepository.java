package com.logistics.tms.shipment.repository;

import com.logistics.tms.shipment.entity.Shipment;
import jakarta.validation.constraints.NotBlank;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.validation.annotation.Validated;

import java.util.List;

@Validated
@Repository
public interface ShipmentRepository
        extends JpaRepository<Shipment, String>, JpaSpecificationExecutor<Shipment> {

    @Query(value = "SELECT FN_TM_SHPMTKY()", nativeQuery = true)
    String getNextSequenceKey();

    Shipment findByCompkeyAndShpmtky(@NotBlank final String compkey,
                                     @NotBlank final String shpmtky);

    Shipment findFirstByShpmtkyAndCompkeyAndDeletatIsNull(
                                        @NotBlank final String shpmtky,
                                        @NotBlank final String compkey);

    List<Shipment> findByShpmtkyAndCompkeyAndDeletatIsNull(
                                        @NotBlank final String shpmtky,
                                        @NotBlank final String compkey);

    List<Shipment> findAllByDeletatIsNotNull();

}
