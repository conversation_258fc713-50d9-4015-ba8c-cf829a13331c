package com.logistics.tms.shipment.repository;

import com.logistics.tms.shipment.entity.ShipmentPlan;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Validated
@Repository
public interface ShipmentPlanRepository
        extends JpaRepository<ShipmentPlan, String>, JpaSpecificationExecutor<ShipmentPlan> {

    @Query(value = "SELECT FN_TM_SHPPLKY()", nativeQuery = true)
    String getNextSequenceKey();

    ShipmentPlan findByCompkeyAndShpplky(@NotBlank final String compkey,
                                         @NotBlank final String shpplky);

    ShipmentPlan findByShpplky(@NotBlank final String shpplky);

    ShipmentPlan findByShpplkyAndDeletatIsNull(@NotBlank final String shpplky);

    ShipmentPlan findByShpplkyAndCompkeyAndDeletatIsNull(
                                @NotBlank final String shpplky,
                                @NotBlank final String compkey);

    List<ShipmentPlan> findByPtnrkeyAndCompkeyAndShpmtdtAndRoundnoAndDeletatNull(
                                                        final String ptnrkey,
                                                        @NotBlank final String compkey,
                                                        @NotNull final LocalDate shpmtdt,
                                                        @NotNull final Integer roundno);

    ShipmentPlan findFirstByCompkeyAndPtnrkeyAndShpmtdtAndRoundnoAndDeletatNull(
                                @NotBlank final String compkey,
                                @NotBlank final String ptnrkey,
                                @NotNull final LocalDate shpmtdt,
                                @NotNull final Integer roundno);

    List<ShipmentPlan> findAllByDeletatIsNotNull();

    ShipmentPlan findByShipmentList_CompkeyAndShipmentList_Shpmtky(@NotBlank final String compkey,
                                                                   @NotBlank final String shpmtky);

    @Query(value = """
        SELECT 
            sub.compkey ,
            sub.shpmtky , -- Shipment No.
            sub.cargoty , -- 단,혼적
            sub.vhctncd , -- 차량톤수
            -- DEADDR1이 3글자 이상이면 3글자만 표시하고 나머지는 '...'
            GROUP_CONCAT(
                CASE 
                WHEN CHAR_LENGTH(pudenamlc) > 3 THEN CONCAT(SUBSTRING(pudenamlc, 1, 3), '...')
                ELSE pudenamlc
                END SEPARATOR ' > '
            ) as piaddr,
            GROUP_CONCAT(
                CASE 
                WHEN CHAR_LENGTH(dfdenamlc) > 3 THEN CONCAT(SUBSTRING(dfdenamlc, 1, 3), '...')
                ELSE dfdenamlc
                END SEPARATOR ' > '
            ) as deaddr,
            sub.loadrat, -- 적재율
            sub.ptnrkey, -- 협력업체키
            sub.destkey, -- 도착지 key
            sub.shpmtdt, -- 운송일
            sub.oetmsky, -- 운송주문번호
            sub.ptnamlc, -- 파트너명칭
            sub.ptnrtyp, -- 협력업체타입
            sub.shpplky, -- 운송계획번호
            count(sub.shsckypickup) as countofpickup, -- 상차지 수
            count(sub.shsckydropoff) as countofdropoff -- 하차지 수
        from (
            select 
                pl.shpplky , -- 운송계획번호
                mt.shpmtky , -- Shipment No.
                mt.cargoty , -- 단,혼적
                mt.vhctncd , -- 차량톤수
                pu.denamlc as pudenamlc , -- 상차지 명칭
                df.denamlc as dfdenamlc , -- 도착지 명칭
                COALESCE(mt.loadrat, 0) AS loadrat , -- 적재율
                df.ptnrkey , -- 협력업체키
                df.destkey , -- 도착지 key
                df.oetmsky , -- 운송주문번호
                pl.shpmtdt , -- 운송일
                df.ptnamlc , -- 파트너명칭
                df.ptnrtyp , -- 협력업체타입
                pu.shpscky as shsckypickup , -- 상차지 sc key
                df.shpscky as shsckydropoff , -- 하차지 sc key
                pl.compkey
            from tmshsc df
                left join tmshmt mt on (mt.shpmtky = df.shpmtky and mt.deletat is null)
                left join tmshpl pl on (pl.shpplky = pl.shpplky and pl.deletat is null)
                left join tmshsc pu on (pu.oetmsky = df.oetmsky and pu.deletat is null and pu.vsttype = 'PICKUP')
/*
            from tmshpl pl
                left join tmshmt mt on (mt.shpplky = pl.shpplky and mt.deletat is null)
                left join tmshsc df on (df.shpmtky = mt.shpmtky and df.deletat is null and df.vsttype = 'DROPOFF')
                left join tmshsc pu on (pu.oetmsky = df.oetmsky and pu.deletat is null and pu.vsttype = 'PICKUP')
*/
            WHERE 
                pl.deletat is null 
                and df.vsttype = 'DROPOFF'
                and pl.ptnrkey = :authptnrkey
                and	pl.compkey = :compkey 
                and	pl.shpmtdt = :uldrqdt 
                and pl.roundno = :roundno
                -- and pu.ludrqtm BETWEEN :startTime AND :endTime
                and	mt.ptnrkey = :ptnrkey 
        ) AS sub GROUP BY sub.shpmtky
        """, nativeQuery = true)
    List<Object[]> findShipmentResultRaw(
        @Param("authptnrkey") String authptnrkey,
        @Param("compkey") String compkey, 
        @Param("uldrqdt") LocalDate uldrqdt,
        @Param("roundno") Integer roundno,
        @Param("ptnrkey") String ptnrkey
    );

}
