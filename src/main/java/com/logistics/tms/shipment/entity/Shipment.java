package com.logistics.tms.shipment.entity;

import com.logistics.tms.common.constant.TmsConstant;
import com.logistics.tms.common.enumeration.PartnerTypeEnum;
import com.logistics.tms.common.enumeration.ShipmentVehicleTonEnum;
import com.logistics.tms.common.enumeration.TruckLoadTypeEnum;
import com.logistics.tms.common.enumeration.VehicleTypeEnum;
import com.logistics.tms.common.model.BaseEntity;
import com.logistics.tms.framework.converter.AsetecDateYmdConverter;
import com.logistics.tms.framework.converter.AsetecDateYmdNullConverter;
import com.logistics.tms.shipment.constant.ShipmentConstant;
import com.logistics.tms.shipment.listener.ShipmentEntityListener;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Comment;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@ToString(of = {"shpmtky"})
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
//@IdClass(ShipmentKey.class)
@Comment("운송정보")
@Entity
@Table(name = ShipmentConstant.SHIPMENT_DB_TABLE_NAME,
        indexes = {
                @Index(name = "IDX_SHPPLKY_COMPKEY_DELETAT",
                        columnList = "shpplky, compkey, deletat"),
        })
@EntityListeners({AuditingEntityListener.class, ShipmentEntityListener.class})
public class Shipment extends BaseEntity {

    @Builder.Default
    @Size(max = 20)
    @NotNull
//    @Id
    @Comment("Service Client")
    @Column(nullable = false, length = 20)
    private String compkey = TmsConstant.DEFAULT_COMPKEY;

    @NonNull
    @Id
    @Comment("Shipment No.")
    @Column(length = ShipmentConstant.SHIPMENT_KEY_ID_LENGTH, nullable = false)
    private String shpmtky;

    @Comment("운송계획 일련번호")
    @ManyToOne(fetch = FetchType.EAGER, optional = false)
//    @JoinColumns(
//            value = {
//                    @JoinColumn(name = "compkey", nullable = false),
//                    @JoinColumn(name = "shpplky", nullable = false),
//            },
//            foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    @JoinColumn(name = "shpplky", nullable = false, foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private ShipmentPlan shipmentPlan;

    @Builder.Default
    @OneToMany(mappedBy = "shipment", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private List<ShipmentSection> shipmentSectionList = new ArrayList<>();

    @Column(columnDefinition = "DATETIME(3)")
    @Comment("시뮬레이션시작기준일시")
    private LocalDateTime simulat;

    @Comment("확정일시")
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime fixedat;

    @Builder.Default
    @Comment("수동여부")
    @Column(nullable = false)
    private Boolean manlshp = false;

    @Comment("차량톤수")
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, columnDefinition = ShipmentVehicleTonEnum.COLUMN_DEFINITION)
    private ShipmentVehicleTonEnum vhctncd;

    @Comment("차량종류")
    @Builder.Default
    @Enumerated(EnumType.STRING)
    @Column(name = "VHCLTYP", nullable = true, columnDefinition = VehicleTypeEnum.COLUMN_DEFINITION)
    private VehicleTypeEnum vehicleType = VehicleTypeEnum.카고;

    @Comment(TruckLoadTypeEnum.COLUMN_COMMENT)
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, columnDefinition = TruckLoadTypeEnum.COLUMN_DEFINITION)
    private TruckLoadTypeEnum cargoty;  // oetmhd.cargoty

    @Comment("적재율")
    @Column
    private Integer loadrat;

    @Comment("용차 섭외 금액")
    @Builder.Default
    @Column
    private Integer vehcost = 0;

    @Comment("총운송비용")
    @Builder.Default
    @Column
    private Integer totalcost = 0;

    @Comment("예상운임비")
    @Builder.Default
    @Column(name = "ESTFEE", nullable = false, columnDefinition = "INT default 0")
    private Integer estimatedfee = 0;

    @Comment("도착 협력업체키")
    @Column(length = 20)
    private String ptnrkey;

    @Comment("도착 협력업체 명칭")
    @Column(length = 60)
    private String ptnamlc;

    @Comment("도착 협력업체 타입")
    @Column(length = 10)
    private PartnerTypeEnum ptnrtyp;

    // ADD : 2024-11-28 (요청)
    @Comment("마감 여부 (y/n)")
    @Builder.Default
    @Column(length = 1)
    @Pattern(regexp = "Y|N", message = "closlyn must be either 'Y' or 'N'")
    private String closlyn = "N";

    @Comment("마감 일자 (yyyymmdd 형식)")
    @Column(length = 8)
    private String clodate;

    @Comment("마감 시간 (hhmmss 형식)")
    @Column(length = 6)
    private String clotime;

    @Comment("마감 사용자 (담당자 id 또는 이름)")
    @Column(length = 60)
    private String clouser;

    @Comment("마감 번호 (마감을 식별하는 고유 번호)")
    @Column(length = 10)
    private String closeno;
    // ~ ADD : 2024-11-28

    // ADD : 2024-12-05 (요청)
    @Comment("기타운임")
    @Column(columnDefinition = "INTEGER DEFAULT 0")
    private Integer othechg;

    @Comment("기타운임사유")
    @Column(length = 200, columnDefinition = "VARCHAR(200) DEFAULT ' '")
    private String othersn;
    // ~ ADD : 2024-12-05

    @Comment("대체가능차량톤수")
    @Enumerated(EnumType.STRING)
    @Column(name = "REPVHCT", nullable = true, columnDefinition = ShipmentVehicleTonEnum.COLUMN_DEFINITION)
    private ShipmentVehicleTonEnum repVhctn;

    @Comment("자동배차기사")
    @Builder.Default
    @Column(name = "AUTODRV")
    private Long autoDrvId = null;

    @Comment("자동배차차량")
    @Builder.Default
    @Column(name = "AUTOVHC")
    private Long autoVhcId = null;

    @Comment("운송완료일자")
    @ColumnDefault("null")
    @Column(name = "COMPLDT", length = 8)
    @Convert(converter = AsetecDateYmdNullConverter.class)
    @Builder.Default
    private LocalDate completedDate = null;

    @Comment("영업일자 - 운송완료일자")
    @ColumnDefault("' '")
    @Column(name = "POSTDAT", length = 8)
    @Convert(converter = AsetecDateYmdConverter.class)
    private LocalDate postdat;

    @Comment("삭제일시")
    @Column(columnDefinition = "DATETIME(3)")
    private LocalDateTime deletat;


    @PreUpdate
    public void synchronizeDates() {
        this.postdat = this.completedDate;
    }

    public void addShipmentSection(final ShipmentSection... shipmentSectionArray) {

        addShipmentSectionList(List.of(shipmentSectionArray));
    }

    public void addShipmentSectionList(final List<ShipmentSection> shipmentSectionList) {

        if (CollectionUtils.isEmpty(shipmentSectionList)) {
            return;
        }

        if (Objects.isNull(this.shipmentSectionList)) {
            this.shipmentSectionList = shipmentSectionList;
        } else {
            this.shipmentSectionList.addAll(shipmentSectionList);
        }

        shipmentSectionList.forEach(sp -> {
            if (!Objects.equals(sp.getShipment(), this)) {
                sp.setShipment(this);
            }
        });
    }

}
