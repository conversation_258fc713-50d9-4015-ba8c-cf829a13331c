package com.logistics.tms.shipment.entity;

import org.hibernate.annotations.Comment;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import com.logistics.tms.common.constant.TmsConstant;
import com.logistics.tms.common.model.BaseEntity;
import com.logistics.tms.shipment.constant.ShipmentConstant;
import com.logistics.tms.shipment.listener.ShipmentOmsItemEntityListener;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Comment("운송아이템정보")
@Entity
@Table(name = ShipmentConstant.SHIPMENT_OMSITEM_DB_TABLE_NAME, indexes = {
                @Index(name = "IDX_SHPMTKY_OETMSKY_DELETAT", columnList = "shpmtky, oetmsky, deletat"),
                @Index(name = "IDX_OETMSKY_COMPKEY_DELETAT", columnList = "oetmsky, compkey, deletat"),
})
@EntityListeners({ AuditingEntityListener.class, ShipmentOmsItemEntityListener.class })
public class ShipmentOmsItem extends BaseEntity {

        @Id
        @Comment("운송 ITEM 번호")
        @GeneratedValue(strategy = GenerationType.IDENTITY)
        @Column(name = "SHPITKY")
        private Long shpitky;

        @NonNull
        @Comment("Shipment No.")
        @Column(nullable = false, length = 10)
        private String shpmtky;

        @NonNull
        @Comment("운송주문번호")
        @Column(nullable = false, length = 10)
        private String oetmsky; // Foreign Key for oetmhd.oetmsky

        @Builder.Default
        @Comment("팔레트 위치 (Y) mm")
        @Column(columnDefinition = "INTEGER DEFAULT 0")
        private Integer loadyplt = 0;

        @Builder.Default
        @Comment("팔레트 위치 (X) mm")
        @Column(columnDefinition = "INTEGER DEFAULT 0")
        private Integer loadxplt = 0;

        @Builder.Default
        @Comment("팔레트 위치 (Y) unit")
        @Column(columnDefinition = "INTEGER DEFAULT 0")
        private Integer loadyunit = 0;

        @Builder.Default
        @Comment("팔레트 위치 (X) unit")
        @Column(columnDefinition = "INTEGER DEFAULT 0")
        private Integer loadxunit = 0;

        @Builder.Default
        @Comment("팔레트 unit 단위 깊이")
        @Column(name = "UNDEPTH", columnDefinition = "INTEGER DEFAULT 0")
        private Integer loadUnitDepth = 0;

        @Builder.Default
        @Comment("팔레트 unit 단위 폭")
        @Column(name = "UNWIDTH", columnDefinition = "INTEGER DEFAULT 0")
        private Integer loadUnitWidth = 0;

        @Builder.Default
        @Comment("적재 층수 (0: 하단/1층, 1: 상단/2층) 없을 경우 0으로 간주함")
        @Column(name = "LEVEL", columnDefinition = "INTEGER DEFAULT 0")
        private Integer level = 0;

        @Comment("삭제일시")
        @Column(columnDefinition = "DATETIME(3)")
        private LocalDateTime deletat;

        @Builder.Default
        @Size(max = 20)
        @NotNull
        @Comment("Service Client")
        @Column(nullable = false, length = 20)
        private String compkey = TmsConstant.DEFAULT_COMPKEY;

}
