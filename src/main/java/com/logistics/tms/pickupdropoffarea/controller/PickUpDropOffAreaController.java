package com.logistics.tms.pickupdropoffarea.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.logistics.tms.common.enumeration.UserTypeEnum;
import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.configuration.security.AuthUser;
import com.logistics.tms.driver.dto.DriverInfoDTO;
import com.logistics.tms.driver.service.DriverInfoCustomService;
import com.logistics.tms.external.dto.MptnmaCarrierDTO;
import com.logistics.tms.external.dto.MptnmaPartnerDTO;
import com.logistics.tms.external.dto.SusrmaDTO;
import com.logistics.tms.external.service.MptnmaService;
import com.logistics.tms.external.service.SusrmaService;
import com.logistics.tms.pickupdropoffarea.dto.PickUpDropOffAreaDTO;
import com.logistics.tms.pickupdropoffarea.dto.PickUpDropOffAreaDistrictDTO;
import com.logistics.tms.pickupdropoffarea.dto.PickUpDropOffAreaDriverDTO;
import com.logistics.tms.pickupdropoffarea.interfaces.PickUpDropOffAreaSwagger;
import com.logistics.tms.pickupdropoffarea.service.PickUpDropOffAreaDistrictService;
import com.logistics.tms.pickupdropoffarea.service.PickUpDropOffAreaDriverService;
import com.logistics.tms.pickupdropoffarea.service.PickUpDropOffAreaService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@RestController
@RequestMapping("/api/pickup-dropoff-areas")
@RequiredArgsConstructor
public class PickUpDropOffAreaController implements PickUpDropOffAreaSwagger {

    private final PickUpDropOffAreaService pickUpDropOffAreaService;
    private final PickUpDropOffAreaDistrictService pickUpDropOffAreaDistrictService;
    private final PickUpDropOffAreaDriverService pickUpDropOffAreaDriverService;
    private final DriverInfoCustomService driverInfoCustomService;
    private final MptnmaService mptnmaService;
    private final SusrmaService susrmaService;

    @Hidden
    @GetMapping
    public List<PickUpDropOffAreaDTO.PickUpDropOffAreaAll> getAllPickUpDropOffAreas() {
        return pickUpDropOffAreaService.findAll();
    }

    @Hidden
    @GetMapping("/{id}")
    public ResponseEntity<PickUpDropOffAreaDTO.PickUpDropOffAreaAll> getPickUpDropOffAreaById(@PathVariable Long id) {
        return pickUpDropOffAreaService.findById(id)
                .map(dto -> ResponseEntity.ok().body(dto))
                .orElse(ResponseEntity.notFound().build());
    }

    // 현재 권역 등록되어 있는 기사 리스트
    @Hidden
    @GetMapping("/area/driver/list")
    public List<PickUpDropOffAreaDriverDTO.DriverResponse> getPickUpDropOffAreasDriverByCarrier(@AuthUser final AuthDTO authDTO, @RequestParam final String carrierCode) {
        Long userasq = null;
        String useract = null;
        Optional<SusrmaDTO> susrmaDTO = susrmaService.findByUseract(authDTO.getId());
        if(susrmaDTO.isPresent()) {
            userasq = susrmaDTO.get().getUserasq();
            useract = susrmaDTO.get().getUseract();
        } else {
            return null;
        }

        String partnerKey = authDTO.getPartnerKey();
        MptnmaPartnerDTO mptnmaCarrierDTO = mptnmaService.findPartnerNameByPtnrkey(partnerKey);
        if(mptnmaCarrierDTO == null) {
            return null;
        }
        String partnerType = mptnmaCarrierDTO.getPtnrtyp();
        if(!UserTypeEnum.CARRIER.equals(partnerType)) {
            return null;
        }
        String ptnamlc = mptnmaCarrierDTO.getPtnamlc();

        log.info("useract->{}, userasq->{}, partnerKey->{}, partnerType->{}, ptnamlc -> {}", useract, userasq, partnerKey, partnerType, ptnamlc);

        return pickUpDropOffAreaDriverService.findByPtnrkey(carrierCode);
    }

    @Hidden
    @GetMapping("/destination/list")
    public List<PickUpDropOffAreaDTO.AreaShipmentResponse> getDestinationPickDropAreaByLoadKeyAndCustKey(
            @RequestParam final String loadKey,
            @RequestParam final String custKey,
            @RequestParam final String ptnrkey,
            @RequestParam final LocalDate requestDate
    ) {
        return pickUpDropOffAreaService.findDestinationPickDropAreaByLoadKeyAndCustKey(loadKey, custKey, ptnrkey, requestDate);
    }

    @Hidden
    @GetMapping("/destination/area")
    public List<PickUpDropOffAreaDTO.AreaShipmentResponse> getDestination2PickDropAreaByLoadKeyAndCustKey(
            @RequestParam final String loadKey,
            @RequestParam final String custKey,
            @RequestParam final String ptnrkey
    ) {
        return pickUpDropOffAreaService.findDestination2PickDropAreaByLoadKeyAndCustKey(loadKey, custKey, ptnrkey);
    }

    @Hidden
    @GetMapping("/carrier/customer")
    public MptnmaCarrierDTO findCarrierKeyByPartnerKey(
            @AuthUser final AuthDTO authDTO,
            @RequestParam final String partnerKey
    ) {
        log.info("findCarrierKeyByPartnerKey : userasq->{}, carrierCode->{}, partnerKey->{}", authDTO.getId(), authDTO.getPartnerKey(), partnerKey);
        return mptnmaService.findCarrierKeyByPartnerKey(partnerKey);
    }

    @GetMapping("/customer/carrier")
    public List<PickUpDropOffAreaDTO.AreaCustomerResponse> findCustomerByCarrierKey(
            @AuthUser final AuthDTO authDTO
    ) {
        log.info("findCustomerByCarrierKey : userasq->{}, partnerKey->{}", authDTO.getId(), authDTO.getPartnerKey());
        String carrierKey = authDTO.getPartnerKey();

        return pickUpDropOffAreaService.findCustomerListByCarrierCode(carrierKey);
    }

    @GetMapping("/delivery")
    public List<PickUpDropOffAreaDTO.AreaDeliveryCount> findByPtnrkeyFromOetmhd(
            @RequestParam final String ptnrKey
    ) {
        return pickUpDropOffAreaService.findByPtnrkeyFromOetmhd(ptnrKey);
    }

    @GetMapping("/list/carrier")
    public ResponseEntity<?> getPickUpDropOffAreaListByCarrier(
            @AuthUser final AuthDTO authDTO,
            @RequestParam final String carrierCode
    ) throws JsonProcessingException {
        try {
            log.info("getPickUpDropOffAreaListByCarrier : userasq->{}, partnerKey->{}", authDTO.getId(), authDTO.getPartnerKey());
            String partnerKey = authDTO.getPartnerKey();
            if (!partnerKey.equals(carrierCode)) {
                return ResponseEntity.badRequest().body("운송사 코드 불일치!!");
            }
            List<PickUpDropOffAreaDTO.AreaResponse> responses = pickUpDropOffAreaService.findByCarrierCode(partnerKey);

            return ResponseEntity.ok(responses);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/list/customer")
    public ResponseEntity<?> getPickUpDropOffAreaListByCustomer(
            @AuthUser final AuthDTO authDTO,
            @RequestParam final String carrierCode,
            @RequestParam final String customerCode
    ) throws JsonProcessingException {
        try {
            log.info("getPickUpDropOffAreaListByCustomer : userasq->{}, partnerKey->{}", authDTO.getId(), authDTO.getPartnerKey());
            String partnerKey = authDTO.getPartnerKey();
            if (!partnerKey.equals(carrierCode)) {
                return ResponseEntity.badRequest().body("운송사 코드 불일치!!");
            }

            List<PickUpDropOffAreaDTO.AreaResponse> responses = pickUpDropOffAreaService.findByCarrierCodeAndCustomerCode(partnerKey, customerCode);

            return ResponseEntity.ok(responses);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @Operation(summary = "상하차지 권역정보 입력")
    @PostMapping
    public ResponseEntity<?> createPickUpDropOffArea(
                        @AuthUser final AuthDTO authDTO,
                        @RequestBody PickUpDropOffAreaDTO.AreaAdd pickUpDropOffAreaAdd) throws JsonProcessingException {
        try {
            log.info("createPickUpDropOffArea : userasq->{}, partnerKey->{}", authDTO.getId(), authDTO.getPartnerKey());
            Long userasq = null;
            Optional<SusrmaDTO> susrmaDTO = susrmaService.findByUseract(authDTO.getId());
            if (susrmaDTO.isPresent()) {
                userasq = susrmaDTO.get().getUserasq();
            } else {
                return ResponseEntity.badRequest().body("유저를 찾을 수 없음!!");
            }

            String partnerKey = authDTO.getPartnerKey();
            if (!partnerKey.equals(pickUpDropOffAreaAdd.getCarrierCode())) {
                log.info("updatePickUpDropOffArea : 운송사 일치하지 않음 !!! authDTO->{}, pickUpDropOffAreaAdd->{}", authDTO.getPartnerKey(), pickUpDropOffAreaAdd.getCarrierCode());
                return ResponseEntity.badRequest().body("운송사 코드 불일치!!");
            }

            MptnmaCarrierDTO mptnmaCarrierDTO = mptnmaService.findCarrierKeyByPartnerKey(pickUpDropOffAreaAdd.getCustomerCode());
            if (mptnmaCarrierDTO == null) {
                return ResponseEntity.badRequest().body("운송사 코드 오류!!");
            }
            String carrierKey = mptnmaCarrierDTO.getCarrkey();
            if (!carrierKey.equals(pickUpDropOffAreaAdd.getCarrierCode())) {
                return ResponseEntity.badRequest().body("해당 운송사에 할당된 조선소가 아님!!");
            }

            MptnmaPartnerDTO mptnmaPartnerDTO = mptnmaService.findPartnerNameByPtnrkey(partnerKey);
            if (mptnmaPartnerDTO == null) {
                return ResponseEntity.badRequest().body("운송사 코드 오류!!");
            }
            String carrierPtnrtyp = mptnmaPartnerDTO.getPtnrtyp();
            if (!UserTypeEnum.CARRIER.equals(carrierPtnrtyp)) {
                return ResponseEntity.badRequest().body("운송사 코드 오류!!");
            }
            String carrierPtnamlc = mptnmaPartnerDTO.getPtnamlc();

            MptnmaPartnerDTO mptnmaCustomerDTO = mptnmaService.findPartnerNameByPtnrkey(pickUpDropOffAreaAdd.getCustomerCode());
            if (mptnmaCustomerDTO == null) {
                return ResponseEntity.badRequest().body("조선소 코드 오류!!");
            }
            String customerPtnrtyp = mptnmaCustomerDTO.getPtnrtyp();
            if (!UserTypeEnum.CUSTOMER.equals(customerPtnrtyp)) {
                return ResponseEntity.badRequest().body("조선소 코드 오류!!");
            }
            String customerPtnamlc = mptnmaCustomerDTO.getPtnamlc();

            List<Long> masterAreaIdList = new ArrayList<>();
            for (String masterAreaId : pickUpDropOffAreaAdd.getMasterAreaIdList()) {
                masterAreaIdList.add(Long.parseLong(masterAreaId));
            }

            List<PickUpDropOffAreaDistrictDTO.DistrictMain> districtMainList =
                    pickUpDropOffAreaDistrictService.findDistrictListByMasterAreaId(masterAreaIdList);
            if (!ObjectUtils.isEmpty(districtMainList)) {
                for (PickUpDropOffAreaDistrictDTO.DistrictMain districtMain : districtMainList) {
                    Long areaId = districtMain.getAreaId();
                    Optional<PickUpDropOffAreaDTO.PickUpDropOffAreaAll> tempAreaAll = pickUpDropOffAreaService.findByAreaIdAndDeletedDtIsNull(areaId);
                    if (tempAreaAll.isPresent()) {
                        if ((partnerKey.equals(tempAreaAll.get().getCarrierCode()))
                                && (pickUpDropOffAreaAdd.getCustomerCode().equals(tempAreaAll.get().getCustomerCode()))) {
                            log.info("updatePickUpDropOffArea : 중복된 소권역 !!! addMasterAreaId -> {}, addCustomer -> {}, existAreaId -> {}, existMasterAreaId -> {}, existCustomer -> {}",
                                    pickUpDropOffAreaAdd.getMasterAreaIdList(), pickUpDropOffAreaAdd.getCustomerCode(),
                                    districtMain.getAreaId(), districtMain.getMasterAreaId(), tempAreaAll.get().getCustomerCode());
                            return ResponseEntity.badRequest().body("중복된 소권역!!");
                        }
                    }
                }
            }

            PickUpDropOffAreaDTO.PickUpDropOffAreaAll pickUpDropOffAreaAll = new PickUpDropOffAreaDTO.PickUpDropOffAreaAll();
            pickUpDropOffAreaAll.setCompkey(authDTO.getCompany());
            pickUpDropOffAreaAll.setCreuser(authDTO.getId());
            pickUpDropOffAreaAll.setLmouser(authDTO.getId());

            pickUpDropOffAreaAll.setUpdtchk(pickUpDropOffAreaAdd.getUpdtchk());
            pickUpDropOffAreaAll.setUserasq(userasq);
            pickUpDropOffAreaAll.setUseract(authDTO.getId());

            pickUpDropOffAreaAll.setAreaName(pickUpDropOffAreaAdd.getAreaName());
            pickUpDropOffAreaAll.setCarrierName(carrierPtnamlc);
            pickUpDropOffAreaAll.setCarrierCode(pickUpDropOffAreaAdd.getCarrierCode());
            pickUpDropOffAreaAll.setCustomerName(customerPtnamlc);
            pickUpDropOffAreaAll.setCustomerCode(pickUpDropOffAreaAdd.getCustomerCode());
            pickUpDropOffAreaAll.setMasterAreaIdList(pickUpDropOffAreaAdd.getMasterAreaIdList());

            PickUpDropOffAreaDTO.AreaResponse createdDTO = pickUpDropOffAreaService.save(pickUpDropOffAreaAll);

            return ResponseEntity.ok(createdDTO);
        } catch (RuntimeException e) {
            log.info("addPickUpDropOffAreaDriver : RuntimeException e -> {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/driver/{id}")
    @Operation(summary = "상하차지 운송기사 추가/수정")
    @Parameter(name = "id", description = "권역관리ID", required = true, example = "2")
    public ResponseEntity<?> addPickUpDropOffAreaDriver(
                        @AuthUser final AuthDTO authDTO,
                        @PathVariable Long id,
                        @RequestBody PickUpDropOffAreaDTO.AreaAddDriver updateDTO) {
        try {
            log.info("addPickUpDropOffAreaDriver : userasq->{}, partnerKey->{}", authDTO.getId(), authDTO.getPartnerKey());
            Optional<PickUpDropOffAreaDTO.PickUpDropOffAreaAll> pickUpDropOffAreaAll = pickUpDropOffAreaService.findByAreaIdAndDeletedDtIsNull(id);

            if(pickUpDropOffAreaAll.isPresent()) {
                PickUpDropOffAreaDTO.PickUpDropOffAreaAll updatePickUpDropOffArea = pickUpDropOffAreaAll.orElseThrow();

                List<String> driverList = updateDTO.getDriverList();
                if(driverList.size() != driverList.stream().distinct().count() ) {
                    log.info("addPickUpDropOffAreaDriver : 입력한 리스트내 중복된 운송기사 존재 !!! driverList -> {}", driverList);
                    log.info("addPickUpDropOffAreaDriver : driverList.size -> {}", driverList.size());
                    log.info("addPickUpDropOffAreaDriver : driverList.stream().distinct().count -> {}", driverList.stream().distinct().count());
                    return ResponseEntity.badRequest().body("중복된 운송기사 존재 !!");
                }

                for(String driver : driverList) {
                    Optional<PickUpDropOffAreaDriverDTO.DriverResponse> driverResponse = pickUpDropOffAreaDriverService.findByUseractAndDeletedDtIsNull(driver);
                    if (driverResponse.isPresent()) {
                        if(!updatePickUpDropOffArea.getDriverList().contains(driver)) {
                            PickUpDropOffAreaDriverDTO.DriverResponse driverMainDTO = driverResponse.get();
                            log.info("addPickUpDropOffAreaDriver : 중복된 운송기사 !!! driver -> {}, existDriver -> {}", driver, driverMainDTO.getDriverUseract());
                            return ResponseEntity.badRequest().body("중복된 운송기사 존재 !!");
                        }
                    }

                    Optional<DriverInfoDTO.DriverAllInfo> driverAllInfo = driverInfoCustomService.findByUseract(driver);
                    if(driverAllInfo.isPresent()) {
                        String currCarrierCode = updatePickUpDropOffArea.getCarrierCode();
                        String updateCarrierCode = driverAllInfo.get().getPtnrkey();
                        if(!updateCarrierCode.equals(currCarrierCode)) {
                            log.info("addPickUpDropOffAreaDriver : 타운송사 운송기사 !!! updateDriverCarrier -> {}, existDriverCarrier -> {}", updateCarrierCode, currCarrierCode);
                            return ResponseEntity.badRequest().body("타운송사 운송기사 !!");
                        }
                    } else {
                        log.info("addPickUpDropOffAreaDriver : 존재하지 않는 운송기사 !!!");
                        return ResponseEntity.badRequest().body("존재하지 않는 운송기사 !!");
                    }
                }

                updatePickUpDropOffArea.setLmouser(authDTO.getId());
                updatePickUpDropOffArea.setDriverList(updateDTO.getDriverList());
                PickUpDropOffAreaDTO.AreaResponse updatedDTO = pickUpDropOffAreaService.addDriver(id, updatePickUpDropOffArea);

                return ResponseEntity.ok(updatedDTO);
            } else {
                log.info("addPickUpDropOffAreaDriver : pickUpDropOffAreaAll not Found !!!");
                return ResponseEntity.badRequest().body("addPickUpDropOffAreaDriver : pickUpDropOffAreaAll not Found !!!");
            }
        } catch (RuntimeException e) {
            log.info("addPickUpDropOffAreaDriver : RuntimeException e -> {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/modify/{id}")
    @Operation(summary = "상하차지 권역정보 수정")
    @Parameter(name = "id", description = "권역관리ID", required = true, example = "2")
    public ResponseEntity<?> updatePickUpDropOffArea(@AuthUser final AuthDTO authDTO,
                                                     @PathVariable Long id,
                                                     @RequestBody PickUpDropOffAreaDTO.AreaUpdate updateDTO) {
        try {
            log.info("updatePickUpDropOffArea : userasq->{}, partnerKey->{}", authDTO.getId(), authDTO.getPartnerKey());
            Optional<PickUpDropOffAreaDTO.PickUpDropOffAreaAll> pickUpDropOffAreaAll = pickUpDropOffAreaService.findByAreaIdAndDeletedDtIsNull(id);

            if(pickUpDropOffAreaAll.isPresent()) {
                PickUpDropOffAreaDTO.PickUpDropOffAreaAll updatePickUpDropOffArea = pickUpDropOffAreaAll.orElseThrow();

                String partnerKey = authDTO.getPartnerKey();
                if(!partnerKey.equals(updateDTO.getCarrierCode())) {
                    log.info("updatePickUpDropOffArea : 운송사 일치하지 않음 !!! authDTO->{}, updateDTO->{}", authDTO.getPartnerKey(), updateDTO.getCarrierCode());
                    return ResponseEntity.badRequest().body("운송사 일치하지 않음!!");
                }

                MptnmaCarrierDTO mptnmaCarrierDTO = mptnmaService.findCarrierKeyByPartnerKey(updateDTO.getCustomerCode());
                if (mptnmaCarrierDTO == null) {
                    return ResponseEntity.badRequest().body("운송사 코드 오류!!");
                }
                String carrierKey = mptnmaCarrierDTO.getCarrkey();
                if (!carrierKey.equals(updateDTO.getCarrierCode())) {
                    return ResponseEntity.badRequest().body("해당 운송사에 할당된 조선소가 아님!!");
                }

                MptnmaPartnerDTO mptnmaPartnerDTO = mptnmaService.findPartnerNameByPtnrkey(updateDTO.getCarrierCode());
                if(mptnmaPartnerDTO == null) {
                    return ResponseEntity.badRequest().body("운송사 코드 오류!!");
                }
                String carrierPtnrtyp = mptnmaPartnerDTO.getPtnrtyp();
                if(!UserTypeEnum.CARRIER.equals(carrierPtnrtyp)) {
                    return ResponseEntity.badRequest().body("운송사 코드 오류!!");
                }
                String carrierPtnamlc = mptnmaPartnerDTO.getPtnamlc();

                MptnmaPartnerDTO mptnmaCustomerDTO = mptnmaService.findPartnerNameByPtnrkey(updateDTO.getCustomerCode());
                if(mptnmaCustomerDTO == null) {
                    return ResponseEntity.badRequest().body("조선소 코드 오류!!");
                }
                String customerPtnrtyp = mptnmaCustomerDTO.getPtnrtyp();
                if(!UserTypeEnum.CUSTOMER.equals(customerPtnrtyp)) {
                    return ResponseEntity.badRequest().body("조선소 코드 오류!!");
                }
                String customerPtnamlc = mptnmaCustomerDTO.getPtnamlc();

                List<Long> masterAreaIdList = new ArrayList<>();
                for(String masterAreaId : updateDTO.getMasterAreaIdList()) {
                    masterAreaIdList.add(Long.parseLong(masterAreaId));
                }

                List<PickUpDropOffAreaDistrictDTO.DistrictMain> districtMainList =
                        pickUpDropOffAreaDistrictService.findDistrictListByMasterAreaId(masterAreaIdList);
                if (!ObjectUtils.isEmpty(districtMainList)) {
                    for(PickUpDropOffAreaDistrictDTO.DistrictMain districtMain : districtMainList) {
                        Long areaId = districtMain.getAreaId();
                        Optional<PickUpDropOffAreaDTO.PickUpDropOffAreaAll> tempAreaAll = pickUpDropOffAreaService.findByAreaIdAndDeletedDtIsNull(areaId);
                        if(tempAreaAll.isPresent()) {
                            if ((!areaId.equals(id))
                                    && (partnerKey.equals(tempAreaAll.get().getCarrierCode()))
                                    && (updateDTO.getCustomerCode().equals(tempAreaAll.get().getCustomerCode()))) {
                                log.info("updatePickUpDropOffArea : 중복된 소권역 !!! id -> {}, updateMasterAreaId -> {}, addCustomer -> {}, existAreaId -> {}, existMasterAreaId -> {}, existCustomer -> {}",
                                        id, updateDTO.getMasterAreaIdList(),  updateDTO.getCustomerCode(),
                                        districtMain.getAreaId(), districtMain.getMasterAreaId(), tempAreaAll.get().getCustomerCode());
                                return ResponseEntity.badRequest().body("중복된 소권역!!");
                            }
                        }
                    }
                }

                updatePickUpDropOffArea.setLmouser(authDTO.getId());
                updatePickUpDropOffArea.setAreaName(updateDTO.getAreaName());
                updatePickUpDropOffArea.setCarrierName(carrierPtnamlc);
                updatePickUpDropOffArea.setCarrierCode(updateDTO.getCarrierCode());
                updatePickUpDropOffArea.setCustomerName(customerPtnamlc);
                updatePickUpDropOffArea.setCustomerCode(updateDTO.getCustomerCode());
                updatePickUpDropOffArea.setMasterAreaIdList(updateDTO.getMasterAreaIdList());

                PickUpDropOffAreaDTO.AreaResponse updatedDTO = pickUpDropOffAreaService.update(id, updatePickUpDropOffArea);
                return ResponseEntity.ok(updatedDTO);
            } else {
                log.info("updatePickUpDropOffArea : pickUpDropOffAreaAll not Found !!!");
                return ResponseEntity.badRequest().body("updatePickUpDropOffArea : pickUpDropOffAreaAll not Found !!!");
            }
        } catch (RuntimeException e) {
            log.info("updatePickUpDropOffArea : RuntimeException e -> {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/delete/{id}")
    @Operation(summary = "상하차지 권역정보 삭제")
    @Parameter(name = "id", description = "권역관리ID", required = true, example = "2")
    public ResponseEntity<?> deletePickUpDropOffArea(
                                                                                    @AuthUser final AuthDTO authDTO,
                                                                                    @PathVariable Long id) {
        try {
            log.info("deletePickUpDropOffArea : userasq->{}, partnerKey->{}", authDTO.getId(), authDTO.getPartnerKey());
            Optional<PickUpDropOffAreaDTO.PickUpDropOffAreaAll> pickUpDropOffAreaAll = pickUpDropOffAreaService.findByAreaIdAndDeletedDtIsNull(id);

            if(pickUpDropOffAreaAll.isPresent()) {
                PickUpDropOffAreaDTO.PickUpDropOffAreaAll deletePickUpDropOffArea = pickUpDropOffAreaAll.orElseThrow();

                String partnerKey = authDTO.getPartnerKey();
                if(!partnerKey.equals(deletePickUpDropOffArea.getCarrierCode())) {
                    log.info("deletePickUpDropOffArea : 운송사 일치하지 않음 !!! authDTO->{}, deletePickUpDropOffArea->{}", authDTO.getPartnerKey(), deletePickUpDropOffArea.getCarrierCode());
                    return ResponseEntity.badRequest().body("운송사 일치하지 않음!!");
                }

                deletePickUpDropOffArea.setLmouser(authDTO.getId());
                LocalDateTime currentDateTime = LocalDateTime.now();
                deletePickUpDropOffArea.setDeletedDt(currentDateTime);

                PickUpDropOffAreaDTO.AreaResponse deletedDTO = pickUpDropOffAreaService.delete(id, deletePickUpDropOffArea);
                return ResponseEntity.ok(deletedDTO);
            }else {
                log.info("deletePickUpDropOffArea : pickUpDropOffAreaAll not Found !!!");
                return ResponseEntity.badRequest().body("updatePickUpDropOffArea : pickUpDropOffAreaAll not Found !!!");
            }
        } catch (RuntimeException e) {
            log.info("deletePickUpDropOffArea : RuntimeException e -> {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/memo/{id}")
    @Operation(summary = "상하차지 권역정보 메모 입력")
    @Parameter(name = "id", description = "권역관리ID", required = true, example = "2")
    public ResponseEntity<?> updatePickUpDropOffAreaMemo(
                                                                                    @AuthUser final AuthDTO authDTO,
                                                                                    @PathVariable Long id,
                                                                                    @RequestBody PickUpDropOffAreaDTO.AreaMemo memoDTO) {
        try {
            log.info("updatePickUpDropOffAreaMemo : userasq->{}, partnerKey->{}", authDTO.getId(), authDTO.getPartnerKey());
            Optional<PickUpDropOffAreaDTO.PickUpDropOffAreaAll> pickUpDropOffAreaAll = pickUpDropOffAreaService.findByAreaIdAndDeletedDtIsNull(id);

            if(pickUpDropOffAreaAll.isPresent()) {
                PickUpDropOffAreaDTO.PickUpDropOffAreaAll updatePickUpDropOffArea = pickUpDropOffAreaAll.orElseThrow();

                String partnerKey = authDTO.getPartnerKey();
                if(!partnerKey.equals(updatePickUpDropOffArea.getCarrierCode())) {
                    log.info("updatePickUpDropOffAreaMemo : 운송사 일치하지 않음 !!! authDTO->{}, updatePickUpDropOffArea->{}", authDTO.getPartnerKey(), updatePickUpDropOffArea.getCarrierCode());
                    return ResponseEntity.badRequest().body("updatePickUpDropOffAreaMemo : 운송사 일치하지 않음!!!");
                }

                updatePickUpDropOffArea.setLmouser(authDTO.getId());
                updatePickUpDropOffArea.setAreaMemo(memoDTO.getAreaMemo());

                PickUpDropOffAreaDTO.PickUpDropOffAreaAll updatedDTO = pickUpDropOffAreaService.updateMemo(id, updatePickUpDropOffArea);
                return ResponseEntity.ok(updatedDTO);
            }else {
                log.info("updatePickUpDropOffAreaMemo : pickUpDropOffAreaAll not Found !!!");
                return ResponseEntity.badRequest().body("updatePickUpDropOffAreaMemo : pickUpDropOffAreaAll not Found !!!");
            }
        } catch (RuntimeException e) {
            log.info("updatePickUpDropOffAreaMemo : RuntimeException e -> {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }
}