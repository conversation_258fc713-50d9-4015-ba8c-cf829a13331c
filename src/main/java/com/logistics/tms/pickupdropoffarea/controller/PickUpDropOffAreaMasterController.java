package com.logistics.tms.pickupdropoffarea.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.configuration.security.AuthUser;
import com.logistics.tms.external.dto.MdesmaDTO;
import com.logistics.tms.external.dto.MdesmaDeliveryRspDTO;
import com.logistics.tms.external.dto.MdesmaRspDTO;
import com.logistics.tms.external.dto.SusrmaDTO;
import com.logistics.tms.external.service.MdesmaService;
import com.logistics.tms.external.service.SusrmaService;
import com.logistics.tms.pickupdropoffarea.dto.PickUpDropOffAreaDistrictDTO;
import com.logistics.tms.pickupdropoffarea.dto.PickUpDropOffAreaMasterDTO;
import com.logistics.tms.pickupdropoffarea.dto.PickUpDropOffAreaMasterDistrictDTO;
import com.logistics.tms.pickupdropoffarea.interfaces.PickUpDropOffAreaMasterSwagger;
import com.logistics.tms.pickupdropoffarea.service.PickUpDropOffAreaDistrictService;
import com.logistics.tms.pickupdropoffarea.service.PickUpDropOffAreaMasterDistrictService;
import com.logistics.tms.pickupdropoffarea.service.PickUpDropOffAreaMasterService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@RestController
@RequestMapping("/api/pickup-dropoff-area-masters")
@RequiredArgsConstructor
public class PickUpDropOffAreaMasterController implements PickUpDropOffAreaMasterSwagger {

    private final PickUpDropOffAreaMasterService pickUpDropOffAreaMasterService;
    private final PickUpDropOffAreaMasterDistrictService pickUpDropOffAreaMasterDistrictService;
    private final PickUpDropOffAreaDistrictService pickUpDropOffAreaDistrictService;
    private final SusrmaService susrmaService;
    private final MdesmaService mdesmaService;

    @Hidden
    @GetMapping("/list")
    public List<PickUpDropOffAreaMasterDTO.AreaMasterAll> getAllPickUpDropOffAreaMasters() {
        return pickUpDropOffAreaMasterService.findAll();
    }

    @Hidden
    @GetMapping("/detail/{id}")
    public ResponseEntity<PickUpDropOffAreaMasterDTO.AreaMasterAll> getPickUpDropOffAreaMasterById(@PathVariable Long id) {
        return pickUpDropOffAreaMasterService.findById(id)
                .map(dto -> ResponseEntity.ok().body(dto))
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/master/position")
    public PickUpDropOffAreaMasterDTO.AreaMasterResponse getMasterAreaByPosition(
            @RequestParam final BigDecimal destLat,
            @RequestParam final BigDecimal destLon
    ) {
        return pickUpDropOffAreaMasterService.findMasterAreaByPosition(destLat, destLon);
    }

    @GetMapping("/destination")
    public List<MdesmaDeliveryRspDTO> getDestinationAllDelivery(@AuthUser final AuthDTO authDTO) {
        log.info("getDestinationAllDelivery : id->{}, userType->{}, roleKey->{}", authDTO.getId(), authDTO.getUserType(), authDTO.getRoleKey());
        return mdesmaService.findAllDestinationsDelivery();
    }

    @Hidden
    @GetMapping("/destination/all")
    public List<MdesmaRspDTO> getDestinationAll(@AuthUser final AuthDTO authDTO) {
        log.info("getDestinationAll : id->{}, userType->{}, roleKey->{}", authDTO.getId(), authDTO.getUserType(), authDTO.getRoleKey());
        return mdesmaService.findAllDestinations();
    }

    @Hidden
    @GetMapping("/destination/routekey")
    public List<MdesmaRspDTO> getDestinationsByRouteKeyList(@RequestParam final List<String> routeKeyList) {
        return mdesmaService.findDestinationsByRouteKeyList(routeKeyList);
    }

    @GetMapping
    public List<PickUpDropOffAreaMasterDTO.AreaMasterResponse> getAllPickUpDropOffAreaMastersDeletedDtIsNull(@AuthUser final AuthDTO authDTO) throws JsonProcessingException {
        log.info("getAllPickUpDropOffAreaMastersDeletedDtIsNull : id->{}, userType->{}, roleKey->{}", authDTO.getId(), authDTO.getUserType(), authDTO.getRoleKey());
        return pickUpDropOffAreaMasterService.findAllByDeletedDtIsNull();
    }

    @GetMapping("/{id}")
    public PickUpDropOffAreaMasterDTO.AreaMasterResponse getPickUpDropOffAreaMasterByIdDeletedDtIsNull(@PathVariable Long id) throws JsonProcessingException {
        return pickUpDropOffAreaMasterService.findByMasterAreaIdAndDeletedDtIsNullResponse(id);
    }

    @GetMapping("/destinations/unregistered")
    public List<MdesmaRspDTO> getUnregisteredDestinations(@AuthUser final AuthDTO authDTO) {
        log.info("getUnregisteredDestinations : id->{}, userType->{}, roleKey->{}", authDTO.getId(), authDTO.getUserType(), authDTO.getRoleKey());
        return mdesmaService.findUnregisteredDestinations();
    }

    @GetMapping("/master/unregistered")
    public List<PickUpDropOffAreaMasterDTO.AreaMasterUnRegisteredResponse> getUnregisteredPickUpDropOffAreaMaster(@AuthUser final AuthDTO authDTO) {
        log.info("getUnregisteredPickUpDropOffAreaMaster : id->{}, userType->{}, roleKey->{}", authDTO.getId(), authDTO.getUserType(), authDTO.getRoleKey());
        return pickUpDropOffAreaMasterService.findUnregisteredPickUpDropOffAreaMaster();
    }

    @Operation(summary = "상하차지 소권역 정보 입력")
    @PostMapping
    public ResponseEntity<?> createPickUpDropOffAreaMaster(@AuthUser final AuthDTO authDTO,
                                                           @RequestBody PickUpDropOffAreaMasterDTO.AreaMasterAdd addDTO)
            throws JsonProcessingException {
        try {
            log.info("createPickUpDropOffAreaMaster : id->{}, userType->{}, roleKey->{}", authDTO.getId(), authDTO.getUserType(), authDTO.getRoleKey());

            if ((addDTO.getAreaNameList().size() != addDTO.getAreaCodeList().size())
                    || (addDTO.getAreaNameList().size() != addDTO.getAreaCentroidList().size())
                    || (addDTO.getAreaNameList().size() != addDTO.getPolygonPointList().size())
            ) {
                log.info("선택구역 리스트 갯수 불일치 !!!");
                return ResponseEntity.badRequest().body("선택구역 리스트 갯수 불일치 !!!");
            }

            List<String> updateCodeList = addDTO.getAreaCodeList();
            for (String updateCode : updateCodeList) {
                PickUpDropOffAreaMasterDistrictDTO.MasterDistrictMain masterDistrictMain =
                        pickUpDropOffAreaMasterDistrictService.findMasterDistrictByCode(updateCode);
                if (!ObjectUtils.isEmpty(masterDistrictMain)) {
                    String existCode = masterDistrictMain.getCode();
                    if (existCode != null) {
                        log.info("중복된 선택구역동코드 !!! updateCode -> {}, existCode -> {}", updateCode, existCode);
                        return ResponseEntity.badRequest().body("중복된 선택구역동코드 !!!");
                    }
                }
            }

            Long userasq = null;
            Optional<SusrmaDTO> susrmaDTO = susrmaService.findByUseract(authDTO.getId());
            if (susrmaDTO.isPresent()) {
                userasq = susrmaDTO.get().getUserasq();
            } else {
                return ResponseEntity.badRequest().body("유저를 찾을 수 없음!!");
            }

            PickUpDropOffAreaMasterDTO.AreaMasterAll areaMasterAll = new PickUpDropOffAreaMasterDTO.AreaMasterAll();
            PickUpDropOffAreaMasterDTO.AreaMasterDistrict areaMasterDistrict = new PickUpDropOffAreaMasterDTO.AreaMasterDistrict();
            areaMasterAll.setCompkey(authDTO.getCompany());
            areaMasterAll.setCreuser(authDTO.getId());
            areaMasterAll.setLmouser(authDTO.getId());

            areaMasterAll.setUpdtchk(addDTO.getUpdtchk());
            areaMasterAll.setUserasq(userasq);
            areaMasterAll.setUseract(authDTO.getId());

            areaMasterAll.setMasterAreaName(addDTO.getMasterAreaName());
            areaMasterAll.setAreaNameList(addDTO.getAreaNameList());
            areaMasterAll.setAreaCodeList(addDTO.getAreaCodeList());

            areaMasterDistrict.setPolygonPointList(addDTO.getPolygonPointList());
            areaMasterDistrict.setAreaCentroidList(addDTO.getAreaCentroidList());

            PickUpDropOffAreaMasterDTO.AreaMasterResponse createdDTO = pickUpDropOffAreaMasterService.save(areaMasterAll, areaMasterDistrict);

            return ResponseEntity.ok(createdDTO);
        } catch (RuntimeException | JsonProcessingException e) {
            log.info("createPickUpDropOffAreaMaster : RuntimeException e -> {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/modify/{id}")
    @Operation(summary = "상하차지 소권역 정보 수정")
    @Parameter(name = "id", description = "소권역관리ID", required = true, example = "2")
    public ResponseEntity<?> updatePickUpDropOffAreaMaster(@AuthUser final AuthDTO authDTO,
                                                           @PathVariable Long id,
                                                           @RequestBody PickUpDropOffAreaMasterDTO.AreaMasterUpdate updateDTO) {
        try {
            log.info("updatePickUpDropOffAreaMaster : id->{}, userType->{}, roleKey->{}", authDTO.getId(), authDTO.getUserType(), authDTO.getRoleKey());

            if((updateDTO.getAreaNameList().size() != updateDTO.getAreaCodeList().size())
                    || (updateDTO.getAreaNameList().size() != updateDTO.getAreaCentroidList().size())
                    || (updateDTO.getAreaNameList().size() != updateDTO.getPolygonPointList().size())
            ) {
                log.info("선택구역 리스트 갯수 불일치 !!!");
                return ResponseEntity.badRequest().body("선택구역 리스트 갯수 불일치 !!!");
            }

            List<Long> masterAreaIdList = new ArrayList<>();
            masterAreaIdList.add(id);
            List<PickUpDropOffAreaDistrictDTO.DistrictMain> districtMainList = pickUpDropOffAreaDistrictService.findDistrictListByMasterAreaId(masterAreaIdList);
            if(districtMainList != null && !districtMainList.isEmpty()) {
                log.info("중권역에서 사용중인 소권역은 수정할 수 없습니다.!!!");
                return ResponseEntity.badRequest().body("중권역에서 사용중인 소권역은 수정할 수 없습니다.!!!");
            }

            List<String> updateCodeList = updateDTO.getAreaCodeList();
            for (String updateCode : updateCodeList) {
                PickUpDropOffAreaMasterDistrictDTO.MasterDistrictMain masterDistrictMain =
                        pickUpDropOffAreaMasterDistrictService.findMasterDistrictByCode(updateCode);
                if (!ObjectUtils.isEmpty(masterDistrictMain)) {
                    String existCode = masterDistrictMain.getCode();
                    if (existCode != null && !id.equals(masterDistrictMain.getMasterAreaId())) {
                        log.info("중복된 선택구역동코드 !!! updateCode -> {}, existCode -> {}", updateCode, existCode);
                        return ResponseEntity.badRequest().body("중복된 선택구역동코드 !!!");
                    }
                }
            }

            Optional<PickUpDropOffAreaMasterDTO.AreaMasterAll> areaMasterAll = pickUpDropOffAreaMasterService.findByMasterAreaIdAndDeletedDtIsNull(id);
            PickUpDropOffAreaMasterDTO.AreaMasterDistrict areaMasterDistrict = new PickUpDropOffAreaMasterDTO.AreaMasterDistrict();

            if(areaMasterAll.isPresent()) {
                PickUpDropOffAreaMasterDTO.AreaMasterAll updateAreaMaster = areaMasterAll.orElseThrow();

                updateAreaMaster.setLmouser(authDTO.getId());
                updateAreaMaster.setMasterAreaName(updateDTO.getMasterAreaName());
                updateAreaMaster.setAreaNameList(updateDTO.getAreaNameList());
                updateAreaMaster.setAreaCodeList(updateDTO.getAreaCodeList());

                areaMasterDistrict.setPolygonPointList(updateDTO.getPolygonPointList());
                areaMasterDistrict.setAreaCentroidList(updateDTO.getAreaCentroidList());

                PickUpDropOffAreaMasterDTO.AreaMasterResponse updatedDTO = pickUpDropOffAreaMasterService.update(id, updateAreaMaster, areaMasterDistrict);
                return ResponseEntity.ok(updatedDTO);
            }
            log.info("updatePickUpDropOffAreaMaster : areaMasterAll not Found !!!");
            return ResponseEntity.badRequest().body("updatePickUpDropOffAreaMaster : areaMasterAll not Found !!!");
        } catch (RuntimeException | JsonProcessingException e) {
            log.info("updatePickUpDropOffAreaMaster : RuntimeException e -> {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/delete/{id}")
    @Operation(summary = "상하차지 소권역 정보 삭제")
    @Parameter(name = "id", description = "소권역관리ID", required = true, example = "2")
    public ResponseEntity<?> deletePickUpDropOffAreaMaster(
            @AuthUser final AuthDTO authDTO,
            @PathVariable Long id) {
        try {
            log.info("deletePickUpDropOffAreaMaster : id->{}, userType->{}, roleKey->{}", authDTO.getId(), authDTO.getUserType(), authDTO.getRoleKey());

            Optional<PickUpDropOffAreaMasterDTO.AreaMasterAll> areaMasterAll = pickUpDropOffAreaMasterService.findByMasterAreaIdAndDeletedDtIsNull(id);

            if(areaMasterAll.isPresent()) {
                PickUpDropOffAreaMasterDTO.AreaMasterAll deleteAreaMaster = areaMasterAll.orElseThrow();

                List<Long> masterAreaIdList = new ArrayList<>();
                masterAreaIdList.add(id);
                List<PickUpDropOffAreaDistrictDTO.DistrictMain> districtMainList = pickUpDropOffAreaDistrictService.findDistrictListByMasterAreaId(masterAreaIdList);
                if(districtMainList != null && !districtMainList.isEmpty()) {
                    log.info("중권역에서 사용중인 소권역은 삭제할 수 없습니다.!!!");
                    return ResponseEntity.badRequest().body("중권역에서 사용중인 소권역은 삭제할 수 없습니다.!!!");
                }

                deleteAreaMaster.setLmouser(authDTO.getId());
                LocalDateTime currentDateTime = LocalDateTime.now();
                deleteAreaMaster.setDeletedDt(currentDateTime);

                PickUpDropOffAreaMasterDTO.AreaMasterResponse deletedDTO = pickUpDropOffAreaMasterService.delete(id, deleteAreaMaster);
                return ResponseEntity.ok(deletedDTO);
            } else {
                log.info("deletePickUpDropOffAreaMaster : areaMasterAll not Found !!!");
                return ResponseEntity.badRequest().body("deletePickUpDropOffAreaMaster : areaMasterAll not Found !!!");
            }
        } catch (RuntimeException | JsonProcessingException e) {
            log.info("deletePickUpDropOffAreaMaster : RuntimeException e -> {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/destination/routekey/{id}")
    @Operation(summary = "상하차지 도착지 배송권역 Route 수정")
    @Parameter(name = "id", description = "도착지(상하차지) 일련번호", required = true, example = "2")
    public ResponseEntity<?> updateMdesmaRouteKey(
            @AuthUser final AuthDTO authDTO,
            @PathVariable Long id,
            @RequestParam @NotNull final String routeKey) {
        try {
            log.info("updateMdesmaRouteKey : id->{}, userType->{}, roleKey->{}", authDTO.getId(), authDTO.getUserType(), authDTO.getRoleKey());

            Optional<MdesmaDTO> mdesmaDto = mdesmaService.findById(id);

            if(mdesmaDto.isPresent()) {
                MdesmaDTO updateDTO = mdesmaDto.orElseThrow();
                updateDTO.setLmouser(authDTO.getId());
                updateDTO.setRouteky(routeKey);

                MdesmaRspDTO updateRspDTO = mdesmaService.update(id, updateDTO);
                return ResponseEntity.ok(updateRspDTO);
            }
            log.info("updateMdesmaRouteKey : mdesmaDto not Found !!!");
            return ResponseEntity.badRequest().body("updateMdesmaRouteKey : mdesmaDto not Found !!!");
        } catch (RuntimeException e) {
            log.info("updateMdesmaRouteKey : RuntimeException e -> {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }
}