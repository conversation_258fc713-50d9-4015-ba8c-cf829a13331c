package com.logistics.tms.pickupdropoffarea.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.logistics.tms.driver.dto.DriverHolidayInfoDTO;
import com.logistics.tms.driver.dto.DriverInfoDTO;
import com.logistics.tms.driver.service.DriverHolidayInfoService;
import com.logistics.tms.driver.service.DriverInfoCustomService;
import com.logistics.tms.external.dto.MptnmaCarrierDTO;
import com.logistics.tms.external.service.MptnmaService;
import com.logistics.tms.pickupdropoffarea.constant.PickUpDropOffAreaConstant;
import com.logistics.tms.pickupdropoffarea.dto.PickUpDropOffAreaDTO;
import com.logistics.tms.pickupdropoffarea.dto.PickUpDropOffAreaDistrictDTO;
import com.logistics.tms.pickupdropoffarea.dto.PickUpDropOffAreaDriverDTO;
import com.logistics.tms.pickupdropoffarea.mapper.PickUpDropOffAreaMapper;
import com.logistics.tms.pickupdropoffarea.model.PickUpDropOffArea;
import com.logistics.tms.pickupdropoffarea.model.PickUpDropOffAreaDistrict;
import com.logistics.tms.pickupdropoffarea.model.PickUpDropOffAreaDriver;
import com.logistics.tms.pickupdropoffarea.repository.PickUpDropOffAreaCustomRepository;
import com.logistics.tms.pickupdropoffarea.repository.PickUpDropOffAreaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;


import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class PickUpDropOffAreaService {

    private final PickUpDropOffAreaRepository pickUpDropOffAreaRepository;
    private final PickUpDropOffAreaCustomRepository pickUpDropOffAreaCustomRepository;
    private final PickUpDropOffAreaDriverService pickUpDropOffAreaDriverService;
    private final PickUpDropOffAreaDistrictService pickUpDropOffAreaDistrictService;
    private final DriverHolidayInfoService driverHolidayInfoService;
    private final DriverInfoCustomService driverInfoCustomService;
    private final MptnmaService mptnmaService;

    private final PickUpDropOffAreaMapper mapper;

    public List<PickUpDropOffAreaDTO.PickUpDropOffAreaAll> findAll() {
        return pickUpDropOffAreaRepository.findAll().stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    public Optional<PickUpDropOffAreaDTO.PickUpDropOffAreaAll> findById(Long id) {
        return pickUpDropOffAreaRepository.findById(id)
                .map(mapper::toDto);
    }

    public Optional<PickUpDropOffAreaDTO.PickUpDropOffAreaAll> findByAreaIdAndDeletedDtIsNull(Long id) {
        return pickUpDropOffAreaRepository.findByAreaIdAndDeletedDtIsNull(id)
                .map(mapper::toDto);
    }

    public List<PickUpDropOffAreaDTO.AreaShipmentResponse> findDestinationPickDropAreaByLoadKeyAndCustKey(String loadKey, String custKey, String ptnrkey, LocalDate requestDate) {
        log.info("findDestinationPickDropAreaByLoadKeyAndCustKey : loadKey -> {}, custKey -> {}, ptnrkey -> {}, requestDate -> {}", loadKey, custKey, ptnrkey, requestDate);
        List<PickUpDropOffAreaDTO.AreaShipmentResponse> areaShipmentResponseList = pickUpDropOffAreaCustomRepository.findDestinationPickDropAreaByLoadKeyAndCustKey(loadKey, custKey, ptnrkey);

        for(PickUpDropOffAreaDTO.AreaShipmentResponse areaShipmentResponse : areaShipmentResponseList) {
            List<DriverHolidayInfoDTO.DriverHolidayInfoMain> driverHolidayInfos = driverHolidayInfoService.findByUserasq(areaShipmentResponse.getUserasq());
            boolean isHoliday = false;
            for(DriverHolidayInfoDTO.DriverHolidayInfoMain holidayInfo : driverHolidayInfos) {
                if(requestDate.isAfter(holidayInfo.getStartDt().minusDays(1))
                        && requestDate.isBefore(holidayInfo.getEndDt().plusDays(1))) {
                    isHoliday = true;
                    break;
                }
            }
            log.info("findDestinationPickDropAreaByLoadKeyAndCustKey : getUserasq -> {}, getUseract -> {}, isHoliday -> {}", areaShipmentResponse.getUserasq(), areaShipmentResponse.getUseract(), isHoliday);
            areaShipmentResponse.setIsHoliday(isHoliday);
        }

        return areaShipmentResponseList;
    }

    public List<PickUpDropOffAreaDTO.AreaShipmentResponse> findDestination2PickDropAreaByLoadKeyAndCustKey(String loadKey, String custKey, String ptnrkey) {
        //delete log log.info("findDestination2PickDropAreaByLoadKeyAndCustKey : loadKey -> {}, custKey -> {}, ptnrkey -> {}", loadKey, custKey, ptnrkey);
        return pickUpDropOffAreaCustomRepository.findDestination2PickDropAreaByLoadKeyAndCustKey(loadKey, custKey, ptnrkey);
    }

    public List<PickUpDropOffAreaDTO.AreaDeliveryCount> findByPtnrkeyFromOetmhd(String ptnrKey) {
        LocalDate currentDate = LocalDate.now();
        LocalDate startDate = currentDate.minusDays(30);

        log.info("findByPtnrkeyFromOetmhd : currentDate -> {}, startDate -> {}", currentDate, startDate);

        return pickUpDropOffAreaCustomRepository.findByPtnrkeyFromOetmhd(ptnrKey, currentDate, startDate);
    }

    public List<PickUpDropOffAreaDTO.AreaCustomerResponse> findCustomerListByCarrierCode(String carrierCode) {
        List<MptnmaCarrierDTO> customerList = mptnmaService.findCustomerByCarrierKey(carrierCode);
        List<PickUpDropOffAreaDTO.AreaCustomerResponse> areaCustomerList = new ArrayList<>();
        for(MptnmaCarrierDTO customer : customerList) {
            List<PickUpDropOffArea> pickUpDropOffAreas = pickUpDropOffAreaRepository.findByCarrierCodeAndCustomerCodeAndDeletedDtIsNull(carrierCode, customer.getPtnrkey());

            List<Long> areaIds = new ArrayList<>();
            for(PickUpDropOffArea area : pickUpDropOffAreas) {
                areaIds.add(area.getAreaId());
            }

            PickUpDropOffAreaDTO.AreaCustomerResponse areaCustomerResponse = new PickUpDropOffAreaDTO.AreaCustomerResponse();
            areaCustomerResponse.setPtnrkey(customer.getPtnrkey());
            areaCustomerResponse.setPtnrtyp(customer.getPtnrtyp());
            areaCustomerResponse.setPtnamlc(customer.getPtnamlc());
            areaCustomerResponse.setCarrkey(customer.getCarrkey());


            List<PickUpDropOffAreaDriverDTO.DriverResponse> driverMainList = pickUpDropOffAreaCustomRepository.findDriverListByAreaId(areaIds);
            List<Long> driverIds = new ArrayList<>();
            for(PickUpDropOffAreaDriverDTO.DriverResponse driverResponse : driverMainList) {
                driverIds.add(driverResponse.getAreaDriverId());
            }

            List<PickUpDropOffAreaDistrictDTO.DistrictMain> districtMainList = pickUpDropOffAreaCustomRepository.findDistrictListByAreaId(areaIds);
            List<Long> districtIds = new ArrayList<>();
            for(PickUpDropOffAreaDistrictDTO.DistrictMain districtMain : districtMainList) {
                districtIds.add(districtMain.getAreaDistrictId());
            }

            if(!driverIds.isEmpty()) {
                areaCustomerResponse.setDriverIdList(driverIds);
            }
            if(!districtIds.isEmpty()) {
                areaCustomerResponse.setDistrictIdList(districtIds);
            }

            areaCustomerList.add(areaCustomerResponse);
        }

        return areaCustomerList;
    }

    public PickUpDropOffAreaDTO.AreaResponse convertEntityToAreaResponse(PickUpDropOffArea entity) throws JsonProcessingException {
        PickUpDropOffAreaDTO.AreaResponse areaResponse = new PickUpDropOffAreaDTO.AreaResponse();

        areaResponse.setAreaId(entity.getAreaId());
        areaResponse.setUserasq(entity.getUserasq());
        areaResponse.setUseract(entity.getUseract());
        areaResponse.setAreaName(entity.getAreaName());
        areaResponse.setCarrierName(entity.getCarrierName());
        areaResponse.setCarrierCode(entity.getCarrierCode());
        areaResponse.setCustomerName(entity.getCustomerName());
        areaResponse.setCustomerCode(entity.getCustomerCode());
        areaResponse.setMasterAreaIdList(entity.getMasterAreaIdList());
        areaResponse.setDriverList(entity.getDriverList());
        areaResponse.setAreaMemo(entity.getAreaMemo());

        if (!ObjectUtils.isEmpty(entity.getDriverList())) {
            List<PickUpDropOffAreaDriverDTO.DriverResponse> driverResponseList = new ArrayList<>();
            List<DriverInfoDTO.DriverSimpleInfo> driverList = driverInfoCustomService.findByPartnerKeyAndUserActList(entity.getCarrierCode(), entity.getDriverList());
            for (DriverInfoDTO.DriverSimpleInfo driverInfo : driverList) {
                for (PickUpDropOffAreaDriver pickupDriver : entity.getPickUpDropOffAreaDrivers()) {
                    if(driverInfo.getUseract().equals(pickupDriver.getDriverUseract())) {
                        PickUpDropOffAreaDriverDTO.DriverResponse driverResponse = pickUpDropOffAreaDriverService.convertEntityToDriverMainDto(pickupDriver, driverInfo);
                        driverResponseList.add(driverResponse);
                        break;
                    }
                }
            }
            areaResponse.setPickUpDropOffAreaDriverList(driverResponseList);
        }

        if (!ObjectUtils.isEmpty(entity.getPickUpDropOffAreaDistricts())) {
            List<PickUpDropOffAreaDistrictDTO.DistrictResponse> districtResponseList = new ArrayList<>();
            for (PickUpDropOffAreaDistrict district : entity.getPickUpDropOffAreaDistricts()) {
                PickUpDropOffAreaDistrictDTO.DistrictResponse districtResponse = pickUpDropOffAreaDistrictService.convertEntityToDistrictResponseDto(district);
                if(districtResponse != null) {
                    districtResponseList.add(districtResponse);
                }
            }
            areaResponse.setPickUpDropOffAreaDistrictList(districtResponseList);
        }

        return areaResponse;
    }

    public List<PickUpDropOffAreaDTO.AreaResponse> findByCarrierCode(String carrierCode) throws JsonProcessingException {
        List<PickUpDropOffArea> pickUpDropOffAreas = pickUpDropOffAreaRepository.findByCarrierCodeAndDeletedDtIsNull(carrierCode);

        List<PickUpDropOffAreaDTO.AreaResponse> areaResponseList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pickUpDropOffAreas)) {
            for(PickUpDropOffArea area : pickUpDropOffAreas) {
                PickUpDropOffAreaDTO.AreaResponse areaResponse = convertEntityToAreaResponse(area);
                areaResponseList.add(areaResponse);
            }
        }

        return areaResponseList;
    }

    public List<PickUpDropOffAreaDTO.AreaResponse> findByCarrierCodeAndCustomerCode(String carrierCode, String customerCode) throws JsonProcessingException {

        List<PickUpDropOffArea> pickUpDropOffAreas = pickUpDropOffAreaRepository.findByCarrierCodeAndCustomerCodeAndDeletedDtIsNull(carrierCode, customerCode);

        List<PickUpDropOffAreaDTO.AreaResponse> areaResponseList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pickUpDropOffAreas)) {
            for(PickUpDropOffArea area : pickUpDropOffAreas) {
                PickUpDropOffAreaDTO.AreaResponse areaResponse = convertEntityToAreaResponse(area);
                areaResponseList.add(areaResponse);
            }
        }

        return areaResponseList;
    }

    @Transactional
    public PickUpDropOffAreaDTO.AreaResponse save(PickUpDropOffAreaDTO.PickUpDropOffAreaAll areaAllDTO) throws JsonProcessingException {
        PickUpDropOffArea entity = mapper.toEntity(areaAllDTO);

        List<PickUpDropOffAreaDistrict> pickUpDropOffAreaDistricts = pickUpDropOffAreaDistrictService.setPickUpDropOffAreaDistricts(areaAllDTO);

        PickUpDropOffAreaDriver pickUpDropOffAreaDriver = new PickUpDropOffAreaDriver();
        pickUpDropOffAreaDriver.setCompkey(areaAllDTO.getCompkey());
        pickUpDropOffAreaDriver.setCreuser(areaAllDTO.getCreuser());
        pickUpDropOffAreaDriver.setLmouser(areaAllDTO.getLmouser());
        pickUpDropOffAreaDriver.setUpdtchk(areaAllDTO.getUpdtchk());
        pickUpDropOffAreaDriver.setUserasq(areaAllDTO.getUserasq());
        pickUpDropOffAreaDriver.setUseract(areaAllDTO.getUseract());
        pickUpDropOffAreaDriver.setDriverUseract(PickUpDropOffAreaConstant.PICK_DROP_DRIVER_TEMP);

        pickUpDropOffAreaDriver.setAreaName(areaAllDTO.getAreaName());

        entity.addPickUpDropOffAreaDriver(pickUpDropOffAreaDriver);

        if(!ObjectUtils.isEmpty(pickUpDropOffAreaDistricts)) {
            for (int index = 0; index < pickUpDropOffAreaDistricts.size(); index++) {
                entity.addPickUpDropOffAreaDistrict(pickUpDropOffAreaDistricts.get(index));
            }
        }

        PickUpDropOffArea savedEntity = pickUpDropOffAreaRepository.save(entity);

        return convertEntityToAreaResponse(savedEntity);
    }

    @Transactional
    public PickUpDropOffAreaDTO.AreaResponse addDriver(Long id,
                                                            PickUpDropOffAreaDTO.PickUpDropOffAreaAll areaAllDTO) {
        return pickUpDropOffAreaRepository.findById(id)
                .map(existingPickUpDropOffArea -> {
                    try {
                        if(!ObjectUtils.isEmpty(areaAllDTO.getPickUpDropOffAreaDrivers())) {
                            areaAllDTO.setPickUpDropOffAreaDrivers(null);
                        }

                        PickUpDropOffArea updateEntity = mapper.toEntity(areaAllDTO);
                        updateEntity.setAreaId(id);

                        List<PickUpDropOffAreaDriver> pickUpDropOffAreaDrivers = pickUpDropOffAreaDriverService.setPickUpDropOffAreaDrivers(areaAllDTO);
                        if (!ObjectUtils.isEmpty(pickUpDropOffAreaDrivers)) {
                            for (int index = 0; index < pickUpDropOffAreaDrivers.size(); index++) {
                                updateEntity.addPickUpDropOffAreaDriver(pickUpDropOffAreaDrivers.get(index));
                            }
                        }

                        List<PickUpDropOffAreaDistrict> pickUpDropOffAreaDistricts = existingPickUpDropOffArea.getPickUpDropOffAreaDistricts();
                        if(!ObjectUtils.isEmpty(pickUpDropOffAreaDistricts)) {
                            for (int index = 0; index < pickUpDropOffAreaDistricts.size(); index++) {
                                updateEntity.addPickUpDropOffAreaDistrict(pickUpDropOffAreaDistricts.get(index));
                            }
                        }

                        PickUpDropOffArea savedEntity = pickUpDropOffAreaRepository.save(updateEntity);

                        return convertEntityToAreaResponse(savedEntity);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                })
                .orElseThrow(() -> new RuntimeException("PickUpDropOffAreaInfo not found with id " + id));
    }

    @Transactional
    public PickUpDropOffAreaDTO.AreaResponse update(Long id,
                                            PickUpDropOffAreaDTO.PickUpDropOffAreaAll areaAllDTO) {
        return pickUpDropOffAreaRepository.findById(id)
                .map(existingPickUpDropOffArea -> {
                    try {
                        if(!ObjectUtils.isEmpty(areaAllDTO.getPickUpDropOffAreaDistricts())) {
                            areaAllDTO.setPickUpDropOffAreaDistricts(null);
                        }

                        PickUpDropOffArea updateEntity = mapper.toEntity(areaAllDTO);
                        updateEntity.setAreaId(id);

                        List<PickUpDropOffAreaDriver> pickUpDropOffAreaDrivers = existingPickUpDropOffArea.getPickUpDropOffAreaDrivers();
                        if (!ObjectUtils.isEmpty(pickUpDropOffAreaDrivers)) {
                            for (int index = 0; index < pickUpDropOffAreaDrivers.size(); index++) {
                                pickUpDropOffAreaDrivers.get(index).setAreaName(updateEntity.getAreaName());
                                updateEntity.addPickUpDropOffAreaDriver(pickUpDropOffAreaDrivers.get(index));
                            }
                        }

                        List<PickUpDropOffAreaDistrict> pickUpDropOffAreaDistricts = pickUpDropOffAreaDistrictService.setPickUpDropOffAreaDistricts(areaAllDTO);
                        if(!ObjectUtils.isEmpty(pickUpDropOffAreaDistricts)) {
                            for (int index = 0; index < pickUpDropOffAreaDistricts.size(); index++) {
                                updateEntity.addPickUpDropOffAreaDistrict(pickUpDropOffAreaDistricts.get(index));
                            }
                        }

                        PickUpDropOffArea savedEntity = pickUpDropOffAreaRepository.save(updateEntity);

                        return convertEntityToAreaResponse(savedEntity);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                })
                .orElseThrow(() -> new RuntimeException("PickUpDropOffAreaInfo not found with id " + id));
    }

    @Transactional
    public PickUpDropOffAreaDTO.AreaResponse delete(Long id,
                                                            PickUpDropOffAreaDTO.PickUpDropOffAreaAll areaAllDTO) {
        return pickUpDropOffAreaRepository.findById(id)
                .map(existingPickUpDropOffArea -> {
                    try {
                        PickUpDropOffArea deleteEntity = mapper.toEntity(areaAllDTO);
                        deleteEntity.setAreaId(id);

                        LocalDateTime currentDateTime = LocalDateTime.now();

                        List<PickUpDropOffAreaDriver> pickUpDropOffAreaDrivers = existingPickUpDropOffArea.getPickUpDropOffAreaDrivers();
                        if (!ObjectUtils.isEmpty(pickUpDropOffAreaDrivers)) {
                            for (int index = 0; index < pickUpDropOffAreaDrivers.size(); index++) {
                                pickUpDropOffAreaDrivers.get(index).setDeletedDt(currentDateTime);
                                deleteEntity.addPickUpDropOffAreaDriver(pickUpDropOffAreaDrivers.get(index));
                            }
                        }

                        List<PickUpDropOffAreaDistrict> pickUpDropOffAreaDistricts = existingPickUpDropOffArea.getPickUpDropOffAreaDistricts();
                        if(!ObjectUtils.isEmpty(pickUpDropOffAreaDistricts)) {
                            for (int index = 0; index < pickUpDropOffAreaDistricts.size(); index++) {
                                pickUpDropOffAreaDistricts.get(index).setDeletedDt(currentDateTime);
                                deleteEntity.addPickUpDropOffAreaDistrict(pickUpDropOffAreaDistricts.get(index));
                            }
                        }

                        PickUpDropOffArea savedEntity = pickUpDropOffAreaRepository.save(deleteEntity);
                        return convertEntityToAreaResponse(savedEntity);
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                })
                .orElseThrow(() -> new RuntimeException("PickUpDropOffAreaInfo not found with id " + id));
    }

    @Transactional
    public PickUpDropOffAreaDTO.PickUpDropOffAreaAll updateMemo(Long id, PickUpDropOffAreaDTO.PickUpDropOffAreaAll areaAllDTO) {
        return pickUpDropOffAreaRepository.findById(id)
                .map(existingPickUpDropOffArea -> {
                    PickUpDropOffArea updateEntity = mapper.toEntity(areaAllDTO);
                    updateEntity.setAreaId(id);

                    if(!ObjectUtils.isEmpty(existingPickUpDropOffArea.getPickUpDropOffAreaDrivers())) {
                        for(int index = 0; index < existingPickUpDropOffArea.getPickUpDropOffAreaDrivers().size(); index++) {
                            updateEntity.addPickUpDropOffAreaDriver(existingPickUpDropOffArea.getPickUpDropOffAreaDrivers().get(index));                        }
                    }

                    if(!ObjectUtils.isEmpty(existingPickUpDropOffArea.getPickUpDropOffAreaDistricts())) {
                        for(int index = 0; index < existingPickUpDropOffArea.getPickUpDropOffAreaDistricts().size(); index++) {
                            updateEntity.addPickUpDropOffAreaDistrict(existingPickUpDropOffArea.getPickUpDropOffAreaDistricts().get(index));
                        }
                    }

                    return mapper.toDto(pickUpDropOffAreaRepository.save(updateEntity));
                })
                .orElseThrow(() -> new RuntimeException("PickUpDropOffAreaInfo not found with id " + id));
    }
}