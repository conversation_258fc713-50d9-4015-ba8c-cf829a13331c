package com.logistics.tms.pickupdropoffarea.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.logistics.tms.pickupdropoffarea.dto.PickUpDropOffAreaDTO;
import com.logistics.tms.pickupdropoffarea.dto.PickUpDropOffAreaDistrictDTO;
import com.logistics.tms.pickupdropoffarea.dto.PickUpDropOffAreaMasterDTO;
import com.logistics.tms.pickupdropoffarea.model.PickUpDropOffAreaDistrict;
import com.logistics.tms.pickupdropoffarea.repository.PickUpDropOffAreaCustomRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class PickUpDropOffAreaDistrictService {
    private final PickUpDropOffAreaMasterService pickUpDropOffAreaMasterService;
    private final PickUpDropOffAreaCustomRepository pickUpDropOffAreaCustomRepository;

    public List<PickUpDropOffAreaDistrictDTO.DistrictMain> findDistrictListByMasterAreaId(@RequestParam final List<Long> masterAreaId) {
        return pickUpDropOffAreaCustomRepository.findDistrictListByMasterAreaId(masterAreaId);
    }

    public List<PickUpDropOffAreaDistrict> setPickUpDropOffAreaDistricts
            (PickUpDropOffAreaDTO.PickUpDropOffAreaAll pickUpDropOffAreaDTO) {
        List<PickUpDropOffAreaDistrict> pickUpDropOffAreaDistricts = new ArrayList<>();
        List<String> masterAreaIds = pickUpDropOffAreaDTO.getMasterAreaIdList();

        if(!masterAreaIds.isEmpty()) {
            for (int index = 0; index < masterAreaIds.size(); index++) {
                PickUpDropOffAreaDistrict pickUpDropOffAreaDistrict = new PickUpDropOffAreaDistrict();
                pickUpDropOffAreaDistrict.setCompkey(pickUpDropOffAreaDTO.getCompkey());
                pickUpDropOffAreaDistrict.setCreuser(pickUpDropOffAreaDTO.getCreuser());
                pickUpDropOffAreaDistrict.setLmouser(pickUpDropOffAreaDTO.getLmouser());
                pickUpDropOffAreaDistrict.setUpdtchk(pickUpDropOffAreaDTO.getUpdtchk());
                pickUpDropOffAreaDistrict.setUserasq(pickUpDropOffAreaDTO.getUserasq());
                pickUpDropOffAreaDistrict.setUseract(pickUpDropOffAreaDTO.getUseract());

                pickUpDropOffAreaDistrict.setMasterAreaId(Long.valueOf(masterAreaIds.get(index)));

                pickUpDropOffAreaDistricts.add(pickUpDropOffAreaDistrict);
            }
        }

        return pickUpDropOffAreaDistricts;
    }

    public PickUpDropOffAreaDistrictDTO.DistrictResponse convertEntityToDistrictResponseDto
            (PickUpDropOffAreaDistrict districtEntity) throws JsonProcessingException {
        PickUpDropOffAreaDistrictDTO.DistrictResponse districtResponse = new PickUpDropOffAreaDistrictDTO.DistrictResponse();

        districtResponse.setAreaDistrictId(districtEntity.getAreaDistrictId());
        districtResponse.setAreaId(districtEntity.getPickUpDropOffArea().getAreaId());
        districtResponse.setUserasq(districtEntity.getUserasq());
        districtResponse.setUseract(districtEntity.getUseract());
        districtResponse.setMasterAreaId(districtEntity.getMasterAreaId());

        PickUpDropOffAreaMasterDTO.AreaMasterResponse areaMasterResponse =
                pickUpDropOffAreaMasterService.findByMasterAreaIdAndDeletedDtIsNullResponse(districtEntity.getMasterAreaId());

        if(areaMasterResponse!= null) {
            if(areaMasterResponse.getPickUpDropOffAreaMasterDistrictList() != null && !areaMasterResponse.getPickUpDropOffAreaMasterDistrictList().isEmpty()) {
                districtResponse.setMasterAreaName(areaMasterResponse.getMasterAreaName());
                districtResponse.setMasterDistrictList(areaMasterResponse.getPickUpDropOffAreaMasterDistrictList());
            }
        } else {
            return null;
        }

        return districtResponse;
    }
}