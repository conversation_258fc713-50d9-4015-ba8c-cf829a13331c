package com.logistics.tms.pickupdropoffarea.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.logistics.tms.external.dto.MdesmaDTO;
import com.logistics.tms.external.dto.MdesmaRspDTO;
import com.logistics.tms.external.service.MdesmaService;
import com.logistics.tms.pickupdropoffarea.dto.*;
import com.logistics.tms.pickupdropoffarea.mapper.PickUpDropOffAreaMasterMapper;
import com.logistics.tms.pickupdropoffarea.model.*;
import com.logistics.tms.pickupdropoffarea.repository.PickUpDropOffAreaCustomRepository;
import com.logistics.tms.pickupdropoffarea.repository.PickUpDropOffAreaMasterRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.awt.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class PickUpDropOffAreaMasterService {

    private final PickUpDropOffAreaMasterRepository pickUpDropOffAreaMasterRepository;
    private final PickUpDropOffAreaMasterDistrictService pickUpDropOffAreaMasterDistrictService;
    private final PickUpDropOffAreaCustomRepository pickUpDropOffAreaCustomRepository;
    private final PickUpDropOffAreaMasterMapper mapper;
    private final MdesmaService mdesmaService;

    public List<PickUpDropOffAreaMasterDTO.AreaMasterAll> findAll() {
        return pickUpDropOffAreaMasterRepository.findAll().stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    public Optional<PickUpDropOffAreaMasterDTO.AreaMasterAll> findById(Long id) {
        return pickUpDropOffAreaMasterRepository.findById(id)
                .map(mapper::toDto);
    }

    public List<PickUpDropOffAreaMasterDTO.AreaMasterResponse> findAllByDeletedDtIsNull() throws JsonProcessingException {
        List<PickUpDropOffAreaMaster> areaMasterList = pickUpDropOffAreaMasterRepository.findAllByDeletedDtIsNull();

        List<PickUpDropOffAreaMasterDTO.AreaMasterResponse> areaMasterResponseList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(areaMasterList)) {
            for (PickUpDropOffAreaMaster areaMaster : areaMasterList) {
                PickUpDropOffAreaMasterDTO.AreaMasterResponse areaMasterResponse = convertEntityToAreaMasterResponse(areaMaster, false);

                areaMasterResponseList.add(areaMasterResponse);
            }
        }

        return areaMasterResponseList;
    }

    public PickUpDropOffAreaMasterDTO.AreaMasterResponse findByMasterAreaIdAndDeletedDtIsNullResponse(Long id) throws JsonProcessingException {

        Optional<PickUpDropOffAreaMaster> areaMaster = pickUpDropOffAreaMasterRepository.findByMasterAreaIdAndDeletedDtIsNull(id);

        PickUpDropOffAreaMasterDTO.AreaMasterResponse areaMasterResponse = new PickUpDropOffAreaMasterDTO.AreaMasterResponse();
        if(areaMaster.isPresent()) {
            areaMasterResponse = convertEntityToAreaMasterResponse(areaMaster.orElseThrow(), false);
        } else {
            return null;
        }

        List<String> routeKeyList = new ArrayList<>();
        routeKeyList.add(areaMasterResponse.getMasterAreaId().toString());

        List<MdesmaRspDTO> existMdesmaList = mdesmaService.findDestinationsByRouteKeyList(routeKeyList);
        if(!ObjectUtils.isEmpty(existMdesmaList)) {
            areaMasterResponse.setMdesmaRspList(existMdesmaList);
        }

        return areaMasterResponse;
    }

    public List<PickUpDropOffAreaMasterDTO.AreaMasterUnRegisteredResponse> findUnregisteredPickUpDropOffAreaMaster() {
        List<PickUpDropOffAreaDistrictDTO.DistrictMain> districtMainList = pickUpDropOffAreaCustomRepository.findAllDistrictList();

        List<PickUpDropOffAreaMasterDTO.AreaMasterUnRegisteredResponse> areaMasterList = new ArrayList<>();
        if(!districtMainList.isEmpty()) {
            List<Long> masterAreaIdList = new ArrayList<>();
            for(PickUpDropOffAreaDistrictDTO.DistrictMain districtMain : districtMainList) {
                masterAreaIdList.add(districtMain.getMasterAreaId());
            }
            areaMasterList = pickUpDropOffAreaCustomRepository.findUnregisteredPickUpDropOffAreaMaster(masterAreaIdList);
        }

        return areaMasterList;
    }

    public Optional<PickUpDropOffAreaMasterDTO.AreaMasterAll> findByMasterAreaIdAndDeletedDtIsNull(Long id) throws JsonProcessingException {
        return pickUpDropOffAreaMasterRepository.findByMasterAreaIdAndDeletedDtIsNull(id).map(mapper::toDto);
    }

    public PickUpDropOffAreaMasterDTO.AreaMasterResponse convertEntityToAreaMasterResponse(PickUpDropOffAreaMaster entity, boolean isDelete) throws JsonProcessingException {
        PickUpDropOffAreaMasterDTO.AreaMasterResponse areaMasterResponse = new PickUpDropOffAreaMasterDTO.AreaMasterResponse();

        areaMasterResponse.setMasterAreaId(entity.getMasterAreaId());
        areaMasterResponse.setUserasq(entity.getUserasq());
        areaMasterResponse.setUseract(entity.getUseract());
        areaMasterResponse.setMasterAreaName(entity.getMasterAreaName());
        areaMasterResponse.setAreaNameList(entity.getAreaNameList());
        areaMasterResponse.setAreaCodeList(entity.getAreaCodeList());

        if(!isDelete) {
            if (!ObjectUtils.isEmpty(entity.getPickUpDropOffAreaMasterDistricts())) {
                List<PickUpDropOffAreaMasterDistrictDTO.MasterDistrictResponse> masterDistrictResponseList = new ArrayList<>();
                for (PickUpDropOffAreaMasterDistrict masterDistrict : entity.getPickUpDropOffAreaMasterDistricts()) {
                    PickUpDropOffAreaMasterDistrictDTO.MasterDistrictResponse masterDistrictResponse = pickUpDropOffAreaMasterDistrictService.convertEntityToDistrictResponseDto(masterDistrict);
                    masterDistrictResponseList.add(masterDistrictResponse);
                }
                areaMasterResponse.setPickUpDropOffAreaMasterDistrictList(masterDistrictResponseList);
            }
        }

        return areaMasterResponse;
    }

    public List<MdesmaRspDTO> findDestinationsInMasterArea(PickUpDropOffAreaMasterDTO.AreaMasterResponse areaMasterResponse, String lmouser) {
        List<MdesmaRspDTO> mdesmaResponseList = new ArrayList<>();

        log.info("findDestinationsInMasterArea : mdesmaService.findAllDestinations");
        List<MdesmaRspDTO> mdesmaRspDTOList = mdesmaService.findAllDestinations();
        for(PickUpDropOffAreaMasterDistrictDTO.MasterDistrictResponse masterDistrictResponse : areaMasterResponse.getPickUpDropOffAreaMasterDistrictList()){
            for(int i = 0; i < masterDistrictResponse.getPolygons().size(); i++){
                Polygon areaPolygon = new Polygon();

                ObjectMapper objectMapper = new ObjectMapper();
                PickUpDropOffAreaMasterDistrictDTO.MasterDistrictPolygon masterDistrictPolygon = objectMapper.convertValue(
                        masterDistrictResponse.getPolygons().get(i),
                        PickUpDropOffAreaMasterDistrictDTO.MasterDistrictPolygon.class
                );
                for(PickUpDropOffAreaMasterDistrictDTO.MasterDistrictLatlngs masterDistrictLatlngs : masterDistrictPolygon.getLatlngs()) {
                    double lat = masterDistrictLatlngs.getLat().doubleValue() * 1000000;
                    double lng = masterDistrictLatlngs.getLng().doubleValue() * 1000000;
                    areaPolygon.addPoint((int) lat, (int) lng);
                }

                for(MdesmaRspDTO mdesmaRspDTO : mdesmaRspDTOList) {
                    double destLat = mdesmaRspDTO.getDestlat().doubleValue() * 1000000;
                    double destLon = mdesmaRspDTO.getDestlon().doubleValue() * 1000000;

                    boolean isContain = areaPolygon.contains((int) destLat, (int) destLon);
                    if(isContain) {
                        Optional<MdesmaDTO> mdesmaDto = mdesmaService.findById(mdesmaRspDTO.getDesmasq().longValue());

                        if(mdesmaDto.isPresent()) {
                            MdesmaDTO updateDTO = mdesmaDto.orElseThrow();
                            updateDTO.setLmouser(lmouser);
                            updateDTO.setRouteky(areaMasterResponse.getMasterAreaId().toString());

                            log.info("findDestinationsInMasterArea : mdesmaService.update");
                            MdesmaRspDTO updateRspDTO = mdesmaService.update(mdesmaRspDTO.getDesmasq().longValue(), updateDTO);
                            log.info("Find Destination {}, {}, {}, {}, {}, {}", updateRspDTO.getDesmasq(), updateRspDTO.getPtnrkey(),
                                    updateRspDTO.getDenamlc(), updateRspDTO.getDestlon(),
                                    updateRspDTO.getDestlat(), updateRspDTO.getRouteky());
                            mdesmaResponseList.add(updateRspDTO);
                        }
                    }
                }
            }
        }

        return mdesmaResponseList;
    }

    public PickUpDropOffAreaMasterDTO.AreaMasterResponse findMasterAreaByPosition(BigDecimal destLat, BigDecimal destLon) {
        log.info("findMasterAreaByPosition : destLat -> {}, destLon -> {}", destLat, destLon);

        double dbDestLat = destLat.doubleValue() * 1000000;
        double dbDestLon = destLon.doubleValue() * 1000000;

        PickUpDropOffAreaMasterDTO.AreaMasterResponse areaMasterResponse = new PickUpDropOffAreaMasterDTO.AreaMasterResponse();

        List<PickUpDropOffAreaMasterDistrictDTO.MasterDistrictResponse> districtList =  pickUpDropOffAreaMasterDistrictService.findMasterDistrictAll();

        for(PickUpDropOffAreaMasterDistrictDTO.MasterDistrictResponse districtResponse : districtList) {
            for(int index = 0; index < districtResponse.getPolygons().size(); index++) {
                Polygon areaPolygon = new Polygon();

                ObjectMapper objectMapper = new ObjectMapper();
                PickUpDropOffAreaMasterDistrictDTO.MasterDistrictPolygon masterDistrictPolygon = objectMapper.convertValue(
                        districtResponse.getPolygons().get(index),
                        PickUpDropOffAreaMasterDistrictDTO.MasterDistrictPolygon.class
                );

                for(PickUpDropOffAreaMasterDistrictDTO.MasterDistrictLatlngs masterDistrictLatlngs : masterDistrictPolygon.getLatlngs()) {
                    double lat = masterDistrictLatlngs.getLat().doubleValue() * 1000000;
                    double lng = masterDistrictLatlngs.getLng().doubleValue() * 1000000;
                    areaPolygon.addPoint((int) lat, (int) lng);
                }

                boolean isContain = areaPolygon.contains((int) dbDestLat, (int) dbDestLon);
                if(isContain) {
                    areaMasterResponse.setMasterAreaId(districtResponse.getMasterAreaId());

                    log.info("findMasterAreaByPosition : isContain -> {}, masterAreaId -> {}", isContain, areaMasterResponse.getMasterAreaId());
                    return areaMasterResponse;
                }
            }
        }

        log.info("findMasterAreaByPosition : not contained !!");
        return areaMasterResponse;
    }

    public void deleteMasterAreaInDestination(PickUpDropOffAreaMasterDTO.AreaMasterResponse areaMasterResponse, String lmouser) {
        List<String> routeKeyList = new ArrayList<>();
        routeKeyList.add(areaMasterResponse.getMasterAreaId().toString());

        List<MdesmaRspDTO> existMdesmaList = mdesmaService.findDestinationsByRouteKeyList(routeKeyList);
        for(MdesmaRspDTO mdesmaRspDTO : existMdesmaList) {
            Optional<MdesmaDTO> mdesmaDto = mdesmaService.findById(mdesmaRspDTO.getDesmasq().longValue());

            if(mdesmaDto.isPresent()) {
                MdesmaDTO updateDTO = mdesmaDto.orElseThrow();
                updateDTO.setLmouser(lmouser);
                updateDTO.setRouteky(null);

                log.info("deleteMasterAreaInDestination : mdesmaService.update");
                MdesmaRspDTO updateRspDTO = mdesmaService.update(mdesmaRspDTO.getDesmasq().longValue(), updateDTO);
                log.info("Delete Destination {}, {}, {}, {}, {}, {}", updateRspDTO.getDesmasq(), updateRspDTO.getPtnrkey(),
                        updateRspDTO.getDenamlc(), updateRspDTO.getDestlon(),
                        updateRspDTO.getDestlat(), updateRspDTO.getRouteky());
            }
        }
    }

    @Transactional
    public PickUpDropOffAreaMasterDTO.AreaMasterResponse save(
                PickUpDropOffAreaMasterDTO.AreaMasterAll areaMasterAll,
                PickUpDropOffAreaMasterDTO.AreaMasterDistrict areaMasterDistrict
    ) throws JsonProcessingException {
        PickUpDropOffAreaMaster entity = mapper.toEntity(areaMasterAll);

        List<PickUpDropOffAreaMasterDistrict> pickUpDropOffAreaMasterDistricts = pickUpDropOffAreaMasterDistrictService.setPickUpDropOffAreaMasterDistricts(areaMasterAll, areaMasterDistrict);

        if(!ObjectUtils.isEmpty(pickUpDropOffAreaMasterDistricts)) {
            for (int index = 0; index < pickUpDropOffAreaMasterDistricts.size(); index++) {
                entity.addPickUpDropOffAreaMasterDistrict(pickUpDropOffAreaMasterDistricts.get(index));
            }
        }

        PickUpDropOffAreaMaster savedEntity = pickUpDropOffAreaMasterRepository.save(entity);
        PickUpDropOffAreaMasterDTO.AreaMasterResponse areaMasterResponse = convertEntityToAreaMasterResponse(savedEntity, false);

        log.info("PickUpDropOffAreaMaster Add masterAreaId -> {}", savedEntity.getMasterAreaId());
        List<MdesmaRspDTO> findMdesmaList = findDestinationsInMasterArea(areaMasterResponse, savedEntity.getLmouser());
        if(!ObjectUtils.isEmpty(findMdesmaList)) {
            areaMasterResponse.setMdesmaRspList(findMdesmaList);
        }

        return areaMasterResponse;
    }

    @Transactional
    public PickUpDropOffAreaMasterDTO.AreaMasterResponse update(Long id,
                                                           PickUpDropOffAreaMasterDTO.AreaMasterAll areaMasterAll,
                                                           PickUpDropOffAreaMasterDTO.AreaMasterDistrict areaMasterDistrict
    )throws JsonProcessingException {
        return pickUpDropOffAreaMasterRepository.findById(id)
                .map(existingPickUpDropOffAreaMaster -> {
                    try {
                        if(!ObjectUtils.isEmpty(areaMasterAll.getPickUpDropOffAreaMasterDistricts())) {
                            areaMasterAll.setPickUpDropOffAreaMasterDistricts(null);
                        }

                        PickUpDropOffAreaMaster updateEntity = mapper.toEntity(areaMasterAll);
                        updateEntity.setMasterAreaId(id);

                        List<PickUpDropOffAreaMasterDistrict> pickUpDropOffAreaMasterDistricts = pickUpDropOffAreaMasterDistrictService.setPickUpDropOffAreaMasterDistricts(areaMasterAll, areaMasterDistrict);
                        if(!ObjectUtils.isEmpty(pickUpDropOffAreaMasterDistricts)) {
                            for (int index = 0; index < pickUpDropOffAreaMasterDistricts.size(); index++) {
                                updateEntity.addPickUpDropOffAreaMasterDistrict(pickUpDropOffAreaMasterDistricts.get(index));
                            }
                        }

                        PickUpDropOffAreaMaster savedEntity = pickUpDropOffAreaMasterRepository.save(updateEntity);
                        PickUpDropOffAreaMasterDTO.AreaMasterResponse areaMasterResponse = convertEntityToAreaMasterResponse(savedEntity, false);

                        log.info("PickUpDropOffAreaMaster Update masterAreaId -> {}", savedEntity.getMasterAreaId());
                        deleteMasterAreaInDestination(areaMasterResponse, savedEntity.getLmouser());
                        List<MdesmaRspDTO> findMdesmaList = findDestinationsInMasterArea(areaMasterResponse, savedEntity.getLmouser());
                        if(!ObjectUtils.isEmpty(findMdesmaList)) {
                            areaMasterResponse.setMdesmaRspList(findMdesmaList);
                        }

                        return areaMasterResponse;
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                })
                .orElseThrow(() -> new RuntimeException("PickUpDropOffAreaMasterInfo not found with id " + id));
    }

    @Transactional
    public PickUpDropOffAreaMasterDTO.AreaMasterResponse delete(Long id,
                                                                PickUpDropOffAreaMasterDTO.AreaMasterAll areaMasterAll
    )throws JsonProcessingException {
        return pickUpDropOffAreaMasterRepository.findById(id)
                .map(existingPickUpDropOffAreaMaster -> {
                    try {

                        PickUpDropOffAreaMaster deleteEntity = mapper.toEntity(areaMasterAll);
                        deleteEntity.setMasterAreaId(id);

                        LocalDateTime currentDateTime = LocalDateTime.now();

                        List<PickUpDropOffAreaMasterDistrict> pickUpDropOffAreaMasterDistricts = existingPickUpDropOffAreaMaster.getPickUpDropOffAreaMasterDistricts();
                        if (!ObjectUtils.isEmpty(pickUpDropOffAreaMasterDistricts)) {
                            for (int index = 0; index < pickUpDropOffAreaMasterDistricts.size(); index++) {
                                pickUpDropOffAreaMasterDistricts.get(index).setDeletedDt(currentDateTime);
                                deleteEntity.addPickUpDropOffAreaMasterDistrict(pickUpDropOffAreaMasterDistricts.get(index));
                            }
                        }

                        PickUpDropOffAreaMaster savedEntity = pickUpDropOffAreaMasterRepository.save(deleteEntity);
                        PickUpDropOffAreaMasterDTO.AreaMasterResponse areaMasterResponse = convertEntityToAreaMasterResponse(savedEntity, true);

                        log.info("PickUpDropOffAreaMaster Delete masterAreaId -> {}", savedEntity.getMasterAreaId());
                        deleteMasterAreaInDestination(areaMasterResponse, savedEntity.getLmouser());

                        return areaMasterResponse;

                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                })
                .orElseThrow(() -> new RuntimeException("PickUpDropOffAreaMasterInfo not found with id " + id));
    }
}