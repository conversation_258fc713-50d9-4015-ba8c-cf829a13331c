package com.logistics.tms.dispatch.dto;

import com.logistics.tms.common.enumeration.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.Point;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

public class DispatchResponseDTO
{
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class DispatchDateRoundDTO {
        @Schema(description = "요청 일자", example = "2024-10-08")
        private LocalDate requestDate;

        @Schema(description = "운송 회차", example = "1")
        private int transportRound;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class DispatchSummaryDTO {
        @Schema(description = "하차 상호명", example = "삼성중공업")
        private String dropOffCompanyName;

        @Schema(description = "전체 대수")
        private int totalVehicle;

        @Schema(description = "미배차 대수")
        private int unDispatchVehicle;

        @Schema(description = "배차 대수")
        private int dispatchVehicle;

        @Schema(description = "단독 대수")
        private int ftlVehicle;

        @Schema(description = "혼적 대수")
        private int ltlVehicle;

        @Schema(description = "미배차 정보")
        private List<UnDispatchInfo> unDispatchInfo;

        @Schema(description = "배차 정보")
        private List<DispatchInfo> dispatchInfo;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class UnDispatchInfo {
        @Schema(description = "배차 ID", example = "1")
        private Long dispatchPlanId;

        @Schema(description = "Shipment No.", example = "S123456789")
        private String shipmentKey;

        @Schema(description = "제약 사항", example = "크레인 상차, 윙바디 불가")
        private String restriction;

        @Schema(description = "상차 제약 사항", example = "크레인 상차, 윙바디 불가")
        private String pickupRestriction;

        @Schema(description = "하차 제약 사항", example = "크레인 상차, 윙바디 불가")
        private String dropOffRestriction;

        @Schema(description = "대체 차량 톤수: T01(1톤), T025(2.5톤), T05(5톤), T11(11톤)", example = "T025")
        private ShipmentVehicleTonEnum replaceWeight;

        @Schema(description = "FTL(단독)/LTL(혼적)", example = "단독")
        private TruckLoadTypeEnum category;

        @Schema(description = "기사 차량 정보")
        private DispatchDriverVehicleInfo driverVehicleInfo;

        @Schema(description = "예상 운임비")
        private Integer estimatedFee;

        @Schema(description = "적재율")
        private Integer loadrat;

        @Schema(description = "적재 정보")
        private List<LoadingInfo> loadingInfoList;

        @Schema(description = "timeLine 배열")
        private List<TimeLineDTO> timeLineList;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class DispatchInfo {
        @Schema(description = "Shipment No.", example = "S123456789")
        private String shipmentKey;

        @Schema(description = "기사 차량 정보")
        private DispatchDriverVehicleInfo driverVehicleInfo;

        @Schema(description = "timeLine 배열")
        private List<TimeLineDTO> timeLineList;

        @Schema(description = "예상 운임비")
        private Integer estimatedFee;

        @Schema(description = "적재율")
        private Integer loadrat;

        @Schema(description = "적재 정보")
        private List<LoadingInfo> loadingInfoList;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class DispatchDriverVehicleInfo {
        @Schema(description = "차량 ID", example = "1")
        private Long vehicleId;

        @Schema(description = "사용ID 일련번호 JPA", example = "17")
        private Long userasq;

        @Schema(description = "사용자ID", example = "driver02")
        private String useract; // 사용자ID

        @Schema(description = "기사 이름", example = "홍길동")
        private String username;

        @Schema(description = "휴대전화번호")
        private String telphnm;

        @Schema(description = VehicleBusinessTypeEnum.COLUMN_COMMENT, example = VehicleBusinessTypeEnum.EXAMPLE)
        private VehicleBusinessTypeEnum vehicleBusinessType;

        @Schema(description = VehicleTypeEnum.COLUMN_COMMENT, example = VehicleTypeEnum.EXAMPLE)
        private VehicleTypeEnum vehicleType;

        @Schema(description = VehicleWeightTypeEnum.COLUMN_COMMENT, example = VehicleWeightTypeEnum.EXAMPLE)
        private VehicleWeightTypeEnum vehicleWeight; // 차량톤수: T01(1톤), T025(2.5톤), T05(5톤), T11(11톤), T25(25톤)

        @Schema(description = "차량등록번호", example = "서울11가1112")
        private String registrationNumber;

        @Schema(description = "휴무 여부")
        private Boolean isHoliday;

        @Schema(description = "대체 여부")
        private Boolean isReplacement;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class TimeLineDTO {
        @Schema(description = "운행 순서")
        private Integer visitOrder;

        @Schema(description = "상/하차")
        private VisitTypeEnum visitType;

        @Schema(description = "요청 일자", example = "2024-10-08")
        private LocalDate requestDate;

        @Schema(description = "요청 시간", example = "08:00:00")
        private LocalTime requestTime;

        @Schema(description = "소요 시간(분)", example = "60")
        private int requiredTime;

        @Schema(description = "예상 도착 일자", example = "2024-10-08")
        private LocalDate estimatedDate;

        @Schema(description = "예상 도착 시간", example = "08:00:00")
        private LocalTime estimatedTime;

        @Schema(description = "운송지 상호명", example = "세광중공업")
        private String companyName;

        @Schema(description = "조선소 하차지", example = "제 1 적치장")
        private String dropOffArea;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class DispatchDetailDTO {
        @Schema(description = "배차계획 ID", example = "1")
        private Long dispatchPlanId;

        @Schema(description = "요청 일자.", example = "2025-10-08")
        private LocalDate requestDate;

        @Schema(description = "운송 회차", example = "1 or 2")
        private int transportRound;

        @Schema(description = "Shipment No.", example = "S123456789")
        private String shipmentKey;

        @Schema(description = "FTL(단독)/LTL(혼적)", example = "단독")
        private TruckLoadTypeEnum category;

        @Schema(description = "기사 차량 정보")
        private DispatchDriverVehicleInfo driverVehicleInfo;

        @Schema(description = "총 소요 시간(분)", example = "60")
        private int totalRequiredTime;

        @Schema(description = "timeLine 배열")
        private List<TimeLineDTO> timeLineList;

        @Schema(description = "예상 운임비")
        private Integer estimatedFee;

        @Schema(description = "적재율")
        private Integer loadrat;

        @Schema(description = "적재 정보")
        private List<LoadingInfo> loadingInfoList;

        @Schema(description = "배송 정보")
        private List<DispatchDeliveryDTO> deliveryDTOList;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class LoadingInfo {
        @Schema(description = "포장형태")
        private PackageCategoryEnum opkcate;

        @Schema(description = "포장타입")
        private PackageTypeEnum opktype;

        @Schema(description = "팔레트 박스 수량")
        private Integer opakqty;

        @Schema(description = "가로")
        private Integer pakwidh;

        @Schema(description = "세로")
        private Integer pakdept;

        @Schema(description = "높이")
        private Integer pakheig;

        @Schema(description = "중량")
        private BigDecimal pkrweig;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class DispatchDeliveryDTO {
        @Schema(description = "운송주문번호", example = "3000046200")
        private String oetmsky;

        @Schema(description = "운송 순서", example = "1, 2, 3, 4, ...")
        private int transportOrder;

        @Schema(description = "적재 정보")
        private LoadingInfo loadingInfo;

        @Schema(description = "FTL(단독)/LTL(혼적)", example = "단독")
        private TruckLoadTypeEnum category;

        @Schema(description = "상차 상호명", example = "세광중공업")
        private String pickupCompanyName;

        @Schema(description = "상차 요청 일자", example = "2024-10-08")
        private LocalDate pickupRequestDate;

        @Schema(description = "상차 요청 시간", example = "08:00:00")
        private LocalTime pickupRequestTime;

        @Schema(description = "하차 상호명", example = "삼성중공업")
        private String dropOffCompanyName;

        @Schema(description = "하차 요청 일자", example = "2024-10-08")
        private LocalDate dropOffRequestDate;

        @Schema(description = "하차 요청 시간", example = "08:00:00")
        private LocalTime dropOffRequestTime;

        @Schema(description = "제약 사항", example = "크레인 상차, 윙바디 불가")
        private String restriction;

        @Schema(description = "상차 제약 사항", example = "크레인 상차, 윙바디 불가")
        private String pickupRestriction;

        @Schema(description = "하차 제약 사항", example = "크레인 상차, 윙바디 불가")
        private String dropOffRestriction;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class RouteSummaryDTO {
        @Schema(description = "Shipment No.", example = "S123456789")
        private String shipmentKey;

        @Schema(description = "경로용 배차 정보")
        private RouteDispatchDTO routeDispatch;

        @Schema(description = "경로 순서 정보")
        private List<RouteOrderDTO> routeOrderList;

        @Schema(description = "운행 경로")
        private LineString route;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class RouteDispatchDTO {
        @Schema(description = "배차 확정 여부", example = "false")
        private Boolean isConfirmed;

        @Schema(description = "배차 여부", example = "false")
        private Boolean isDispatched;

        @Schema(description = "FTL(단독)/LTL(혼적)", example = "단독")
        private TruckLoadTypeEnum category;

        @Schema(description = "기사 차량 정보")
        private DispatchDriverVehicleInfo driverVehicleInfo;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class RouteOrderDTO {
        @Schema(description = "운행 순서")
        private Integer visitOrder;

        @Schema(description = "상/하차")
        private VisitTypeEnum visitType;

        @Schema(description = "운송주문번호", example = "3000046200")
        private String oetmsky;

        @Schema(description = "운송지 상호명", example = "세광중공업")
        private String companyName;

        @Schema(description = "운송지 주소", example = "울산시 동구 방어동175-1번지")
        private String companyAddress;

        @Schema(description = "운송지 좌표", implementation = org.springframework.data.geo.Point.class)
        private Point companyPoint;

        @Schema(description = "조선소 하차지", example = "제 1 적치장")
        private String dropOffArea;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class RequestDateDriverDTO {
        @Schema(description = "기사 ID", example = "1")
        private Long driverId;  // FK for susrma.USERASQ

        @Schema(description = "차량 ID", example = "1")
        private Long vehicleId;

        @Schema(description = "요청 일자", example = "2024-10-08")
        private LocalDate requestDate;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class DispatchDeletedDTO {
        @Schema(description = "message")
        private String message;

        private Integer errorCode;

        private LocalDate uldrqdt;

        private Integer roundno;

        private String shpplky;

    }
}
