package com.logistics.tms.dispatch.service;

import com.logistics.tms.callroute.dto.CallRouteCustomDTO;
import com.logistics.tms.callroute.service.CallRouteService;
import com.logistics.tms.common.enumeration.PartnerTypeEnum;
import com.logistics.tms.common.enumeration.ShipmentVehicleTonEnum;
import com.logistics.tms.common.enumeration.TruckLoadTypeEnum;
import com.logistics.tms.common.enumeration.VisitTypeEnum;
import com.logistics.tms.common.util.AuthUtils;
import com.logistics.tms.common.util.GeometryUtils;
import com.logistics.tms.common.util.NumberUtils;
import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.dispatch.dto.DispatchAddDTO;
import com.logistics.tms.dispatch.dto.DispatchPlanDTO;
import com.logistics.tms.dispatch.dto.DispatchResponseDTO;
import com.logistics.tms.dispatch.entity.DispatchPlan;
import com.logistics.tms.dispatch.mapper.DispatchPlanMapper;
import com.logistics.tms.dispatch.repository.DispatchPlanRepository;
import com.logistics.tms.dispatch.repository.QDispatchPlanRepository;
import com.logistics.tms.driver.dto.DriverHolidayInfoDTO;
import com.logistics.tms.driver.dto.DriverInfoDTO;
import com.logistics.tms.driver.service.DriverInfoCustomService;
import com.logistics.tms.external.service.McodemService;
import com.logistics.tms.lbs.constant.LbsConstant;
import com.logistics.tms.lbs.domain.route.LbsRouteDestination;
import com.logistics.tms.lbs.domain.route.LbsRouteOnDemandRequest;
import com.logistics.tms.lbs.domain.route.LbsRouteOnDemandResponse;
import com.logistics.tms.lbs.service.LbsRouteService;
import com.logistics.tms.push.service.PushService;
import com.logistics.tms.shipment.constant.ShipmentUtils;
import com.logistics.tms.shipment.dto.ShipmentDTO;
import com.logistics.tms.shipment.dto.ShipmentSectionDTO;
import com.logistics.tms.shipment.service.ShipmentSectionService;
import com.logistics.tms.shipment.service.ShipmentService;
import com.logistics.tms.transport.dto.TransportInfoUpdateByDispatch;
import com.logistics.tms.transport.service.TmsToVmsService;
import com.logistics.tms.transport.service.TransportInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Validated
@RequiredArgsConstructor
@Transactional(readOnly = true)
@Service
public class DispatchPlanService {

    private final DispatchPlanRepository dispatchPlanRepository;
    private final QDispatchPlanRepository qDispatchPlanRepository;
    private final DispatchPlanMapper dispatchPlanMapper;
    private final ShipmentSectionService shipmentSectionService;
    private final ShipmentService shipmentService;
    private final DriverInfoCustomService driverInfoCustomService;
    private final CallRouteService callRouteService;
    private final TransportInfoService transportInfoService;
    private final PushService pushService;
    private final TmsToVmsService tmsToVmsService;
    private final McodemService mcodemService;

    @Autowired
    private LbsRouteService lbsRouteService;

    public Optional<DispatchPlanDTO> findById(final Long id) {

        return dispatchPlanRepository.findById(id)
                .map(dispatchPlanMapper::toDto);
    }

    @Transactional
    public int saveByShipment(List<DispatchAddDTO.ShipmentDTO> addDTOList) {
        List<DispatchPlan> entities = new ArrayList<>();
        for (DispatchAddDTO.ShipmentDTO addDTO : addDTOList) {
            DispatchPlanDTO dto = new DispatchPlanDTO(addDTO);

            DispatchPlan existEntity = dispatchPlanRepository.findByShipmentKeyAndIsManual(addDTO.getShipmentKey(), false);
            if (Objects.nonNull(existEntity)) {
                log.info("saveByShipment : Skip because {} already exists.", addDTO.getShipmentKey());
                continue;
            }

            final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
            if (Objects.nonNull(authDTO)) {
                dto.setCreuser(authDTO.getId());
            }

            DispatchPlan entity = dispatchPlanMapper.toEntity(dto);
            entities.add(entity);
        }
        dispatchPlanRepository.saveAll(entities);

        return entities.size();
    }

    public List<DispatchResponseDTO.DispatchDateRoundDTO> getDateRound(LocalDate today) {
        LocalDate endDate = today.plusDays(2);
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        Long limitDate = mcodemService.getLimitDate();
        LocalDate startDate = today.minusDays(limitDate);
        return qDispatchPlanRepository.findDistinctRequestDateAndTransportRound(startDate, endDate, Objects.requireNonNull(authDTO).getPartnerKey());
    }

    public List<DispatchResponseDTO.DispatchSummaryDTO> getSummary(LocalDate requestDate, int transportRound, boolean mobileWeb) {
        List<DispatchResponseDTO.DispatchSummaryDTO> summaryList = new ArrayList<>();

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        List<DispatchPlanDTO> dispatchInfoList = dispatchPlanRepository.findByRequestDateAndTransportRound(requestDate, transportRound)
                .stream()
                .map(dispatchPlanMapper::toDto)
                .filter(d -> d.getIsManual().equals(false))
                .filter(d -> d.getPartnerKey().equals(Objects.requireNonNull(authDTO).getPartnerKey()))
                .toList();
        if (dispatchInfoList.isEmpty())
            return summaryList;

        List<List<DispatchPlanDTO>> groupList = dispatchInfoList.stream()
                .collect(Collectors.groupingBy(DispatchPlanDTO::getDropOffCompanyName))
                .values().stream()
                .toList();
        for (List<DispatchPlanDTO> dtoList : groupList) {
            DispatchResponseDTO.DispatchSummaryDTO summaryDTO = new DispatchResponseDTO.DispatchSummaryDTO();

            List<DispatchPlanDTO> dispatchList = dtoList.stream()
                    .filter(DispatchPlanDTO::getIsConfirmed)
                    .toList();
            List<DispatchPlanDTO> unDispatchList = dtoList.stream()
                    .filter(dto -> !dto.getIsConfirmed())
                    .toList();
            int FTLVehicle = (int) unDispatchList.stream()
                            .filter(dto -> dto.getCargoty().equals(TruckLoadTypeEnum.FTL)).count();
            int LTLVehicle = (int) unDispatchList.stream()
                    .filter(dto -> dto.getCargoty().equals(TruckLoadTypeEnum.LTL)).count();

            summaryDTO.setDropOffCompanyName(dtoList.get(0).getDropOffCompanyName());
            summaryDTO.setTotalVehicle(dtoList.size());
            summaryDTO.setUnDispatchVehicle(unDispatchList.size());
            summaryDTO.setDispatchVehicle(dispatchList.size());
            summaryDTO.setFtlVehicle(FTLVehicle);
            summaryDTO.setLtlVehicle(LTLVehicle);

            List<DispatchResponseDTO.UnDispatchInfo> unDispatchInfos = getUnDispatchInfo(unDispatchList, mobileWeb);
            List<DispatchResponseDTO.DispatchInfo> dispatchInfos = getDispatchInfo(dispatchList, mobileWeb);
            if (unDispatchInfos.isEmpty() && dispatchInfos.isEmpty())
                continue;

            summaryDTO.setUnDispatchInfo(unDispatchInfos);
            summaryDTO.setDispatchInfo(dispatchInfos);

            summaryList.add(summaryDTO);
        }

        int unDispatchCount = summaryList.stream()
                .mapToInt(DispatchResponseDTO.DispatchSummaryDTO::getUnDispatchVehicle)
                .sum();
        if (unDispatchCount == 0) {
            summaryList.clear();
        }
        return summaryList;
    }

    public List<DispatchResponseDTO.UnDispatchInfo> getUnDispatchInfo(List<DispatchPlanDTO> unDispatchList, boolean mobileWeb) {
        List<DispatchResponseDTO.UnDispatchInfo> unDispatchInfoList = new ArrayList<>();

        for (DispatchPlanDTO dto : unDispatchList) {
            DispatchResponseDTO.UnDispatchInfo unDispatchInfo = new DispatchResponseDTO.UnDispatchInfo();

            DispatchResponseDTO.DispatchDriverVehicleInfo driverVehicleInfo = getDriverVehicleInfo(dto);
            List<ShipmentSectionDTO> shipList = shipmentSectionService.getShipmentSectionListByShipment(dto.getCompkey(), dto.getShipmentKey());
            String concatenatedString = shipList.stream()
                    .map(ShipmentSectionDTO::getUnavat1)
                    .map(ShipmentUtils::convertUnavat1Code)
                    .filter(s -> !s.isEmpty())
                    .collect(Collectors.joining(", "));
            if (!concatenatedString.isEmpty())
                unDispatchInfo.setRestriction(concatenatedString);
            List<String> pickupUnavat1List = shipList.stream()
                    .filter(shipment -> shipment.getVsttype() == VisitTypeEnum.PICKUP)
                    .map(ShipmentSectionDTO::getUnavat1)
                    .toList();
            List<String> dropOffUnavat1List = shipList.stream()
                    .filter(shipment -> shipment.getVsttype() == VisitTypeEnum.DROPOFF)
                    .map(ShipmentSectionDTO::getUnavat1)
                    .toList();
            if (!pickupUnavat1List.isEmpty())
                unDispatchInfo.setPickupRestriction(ShipmentUtils.convertUnavat1Code(pickupUnavat1List.get(0)));
            if (!dropOffUnavat1List.isEmpty())
                unDispatchInfo.setDropOffRestriction(ShipmentUtils.convertUnavat1Code(dropOffUnavat1List.get(0)));

            unDispatchInfo.setDispatchPlanId(dto.getDispatchPlanId());
            unDispatchInfo.setShipmentKey(dto.getShipmentKey());
            unDispatchInfo.setCategory(dto.getCargoty());
            unDispatchInfo.setDriverVehicleInfo(driverVehicleInfo);
            if (shipList.isEmpty())
                continue;
            else
                unDispatchInfo.setReplaceWeight(shipList.get(0).getShipment().getRepVhctn());

            if (mobileWeb) {
                Optional<ShipmentDTO> shipmentDTO = shipmentService.getShipment2(dto.getCompkey(), dto.getShipmentKey());
                shipmentDTO.ifPresent(s -> {
                    unDispatchInfo.setLoadrat(s.getLoadrat());
                    unDispatchInfo.setEstimatedFee(s.getEstimatedfee());
                });

                List<DispatchResponseDTO.LoadingInfo> loadingInfoList = new ArrayList<>();

                for (ShipmentSectionDTO pickup : shipList) {
                    if (pickup.getVsttype() == VisitTypeEnum.PICKUP) {
                        DispatchResponseDTO.LoadingInfo loadingInfo = getLoadingInfo(pickup);

                        loadingInfoList.add(loadingInfo);
                    }
                }

                unDispatchInfo.setLoadingInfoList(loadingInfoList);
                List<DispatchResponseDTO.TimeLineDTO> timeLineList = getTimeLineList(dto);

                unDispatchInfo.setTimeLineList(timeLineList);
            }

            unDispatchInfoList.add(unDispatchInfo);
        }

        return unDispatchInfoList;
    }

    public DispatchResponseDTO.DispatchDriverVehicleInfo getDriverVehicleInfo(DispatchPlanDTO dto) {
        DispatchResponseDTO.DispatchDriverVehicleInfo driverVehicleInfo = new DispatchResponseDTO.DispatchDriverVehicleInfo();

        if (dto.getVehicleId() != null) {
            driverVehicleInfo.setVehicleId(dto.getVehicleId());
            driverVehicleInfo.setUserasq(dto.getDriverId());

            Optional<DriverInfoDTO.DriverDetailInfo> driverDetailInfo =
                    driverInfoCustomService.findByUserasqWithDriverHolidayInfo(Objects.requireNonNull(dto).getDriverId());

            LocalDate requestDate = dto.getRequestDate();
            driverDetailInfo.ifPresent(s -> {
                driverVehicleInfo.setUsername(s.getDriverAllInfo().getUsernam());
                driverVehicleInfo.setTelphnm(s.getDriverAllInfo().getTelphnm());
                driverVehicleInfo.setVehicleBusinessType(s.getDriverAllInfo().getVehicleBusinessType());
                driverVehicleInfo.setVehicleType(s.getDriverAllInfo().getVehicleType());
                driverVehicleInfo.setVehicleWeight(s.getDriverAllInfo().getVehicleWeight());
                driverVehicleInfo.setRegistrationNumber(s.getDriverAllInfo().getRegistrationNumber());

                List<DriverHolidayInfoDTO.DriverHolidayInfoMain> holidayList = s.getDriverHolidayInfoDTOList();
                boolean isHoliday = false;
                for(DriverHolidayInfoDTO.DriverHolidayInfoMain holidayInfo : holidayList) {
                    if((requestDate.isAfter(holidayInfo.getStartDt()) || requestDate.isEqual(holidayInfo.getStartDt()))
                            && (requestDate.isBefore(holidayInfo.getEndDt()) || requestDate.isEqual(holidayInfo.getEndDt()))) {
                        isHoliday = true;
                        break;
                    }
                }

                driverVehicleInfo.setIsHoliday(isHoliday);
                driverVehicleInfo.setIsReplacement(false);
                Optional<ShipmentDTO> shipmentDTO = shipmentService.getShipment2(dto.getCompkey(), dto.getShipmentKey());

                shipmentDTO.ifPresent(d -> {
                    if (Objects.nonNull(d.getRepVhctn()) && Objects.nonNull(s.getDriverAllInfo().getVehicleWeight())) {
                        if (s.getDriverAllInfo().getVehicleWeight().getEnglish().equals(d.getRepVhctn().getEnglish())) {
                            driverVehicleInfo.setIsReplacement(true);
                        }
                    }
                });
            });
        } else {
            return null;
        }

        return driverVehicleInfo;
    }

    public List<DispatchResponseDTO.DispatchDriverVehicleInfo> getDriverVehicleInfoList(String partnerKey, LocalDate requestDate, int transportRound, String shipmentKey) {
        List<DispatchResponseDTO.DispatchDriverVehicleInfo> driverVehicleInfos = new ArrayList<>();

        List<DispatchResponseDTO.RequestDateDriverDTO> requestDateDrivers = qDispatchPlanRepository.findDriverIdByRequestDate(requestDate, transportRound);
        List<Long> driverIds = new ArrayList<>();
        for(DispatchResponseDTO.RequestDateDriverDTO requestDateDriver : requestDateDrivers) {
            driverIds.add(requestDateDriver.getDriverId());
        }

        List<DriverInfoDTO.DriverDetailInfo> driverDetailInfos = driverInfoCustomService.findByPartnerKeyWithHoliday(partnerKey, driverIds);

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        String replaceWeight = null;
        String vehicleTon = ShipmentVehicleTonEnum.T11.getEnglish();
        if (Objects.nonNull(authDTO)) {
            Optional<ShipmentDTO> shipmentDTO = shipmentService.getShipment2(authDTO.getCompany(), shipmentKey);
            if (shipmentDTO.isPresent()) {
                if (Objects.nonNull(shipmentDTO.get().getRepVhctn()))
                    replaceWeight = shipmentDTO.get().getRepVhctn().getEnglish();
                vehicleTon = shipmentDTO.get().getVhctncd().getEnglish();
            }
        }
        for(DriverInfoDTO.DriverDetailInfo driverDetailInfo : driverDetailInfos) {
            DispatchResponseDTO.DispatchDriverVehicleInfo driverVehicleInfo = getDriverDTO(driverDetailInfo, requestDate, replaceWeight);

            if (Objects.isNull(driverDetailInfo.getDriverAllInfo().getVehicleWeight()))
                continue;
            if (!Objects.equals(driverDetailInfo.getDriverAllInfo().getVehicleWeight().getEnglish(), vehicleTon) && !driverVehicleInfo.getIsReplacement())
                continue;

            driverVehicleInfos.add(driverVehicleInfo);
        }

        return driverVehicleInfos;
    }

    public DispatchResponseDTO.DispatchDriverVehicleInfo getDriverDTO(DriverInfoDTO.DriverDetailInfo driverDetailInfo, LocalDate requestDate, String replaceWeight) {
        DispatchResponseDTO.DispatchDriverVehicleInfo driverVehicleInfo = new DispatchResponseDTO.DispatchDriverVehicleInfo();

        driverVehicleInfo.setVehicleId(driverDetailInfo.getDriverAllInfo().getVehicleId());
        driverVehicleInfo.setUserasq(driverDetailInfo.getDriverAllInfo().getUserasq());
        driverVehicleInfo.setUseract(driverDetailInfo.getDriverAllInfo().getUseract());
        driverVehicleInfo.setUsername(driverDetailInfo.getDriverAllInfo().getUsernam());
        driverVehicleInfo.setTelphnm(driverDetailInfo.getDriverAllInfo().getTelphnm());
        driverVehicleInfo.setVehicleBusinessType(driverDetailInfo.getDriverAllInfo().getVehicleBusinessType());
        driverVehicleInfo.setVehicleType(driverDetailInfo.getDriverAllInfo().getVehicleType());
        driverVehicleInfo.setVehicleWeight(driverDetailInfo.getDriverAllInfo().getVehicleWeight());
        driverVehicleInfo.setRegistrationNumber(driverDetailInfo.getDriverAllInfo().getRegistrationNumber());


        List<DriverHolidayInfoDTO.DriverHolidayInfoMain> holidayList = driverDetailInfo.getDriverHolidayInfoDTOList();
        boolean isHoliday = false;
        for(DriverHolidayInfoDTO.DriverHolidayInfoMain holidayInfo : holidayList) {
            if(requestDate.isAfter(holidayInfo.getStartDt().minusDays(1))
                    && requestDate.isBefore(holidayInfo.getEndDt().plusDays(1))) {
                isHoliday = true;
                break;
            }
        }
        driverVehicleInfo.setIsHoliday(isHoliday);
        driverVehicleInfo.setIsReplacement(false);
        if (Objects.nonNull(replaceWeight) && Objects.nonNull(driverVehicleInfo.getVehicleWeight())) {
            if (driverVehicleInfo.getVehicleWeight().getEnglish().equals(replaceWeight)) {
                driverVehicleInfo.setIsReplacement(true);
            }
        }

        return driverVehicleInfo;
    }

    public List<DispatchResponseDTO.DispatchInfo> getDispatchInfo(List<DispatchPlanDTO> dispatchList, boolean mobileWeb) {
        List<DispatchResponseDTO.DispatchInfo> dispatchInfoList = new ArrayList<>();

        for (DispatchPlanDTO dto : dispatchList) {
            DispatchResponseDTO.DispatchInfo dispatchInfo = new DispatchResponseDTO.DispatchInfo();

            DispatchResponseDTO.DispatchDriverVehicleInfo driverVehicleInfo = getDriverVehicleInfo(dto);
            List<DispatchResponseDTO.TimeLineDTO> timeLineList = getTimeLineList(dto);

            dispatchInfo.setShipmentKey(dto.getShipmentKey());
            dispatchInfo.setDriverVehicleInfo(driverVehicleInfo);
            dispatchInfo.setTimeLineList(timeLineList);

            if (mobileWeb) {
                Optional<ShipmentDTO> shipmentDTO = shipmentService.getShipment2(dto.getCompkey(), dto.getShipmentKey());
                shipmentDTO.ifPresent(s -> {
                    dispatchInfo.setLoadrat(s.getLoadrat());
                    dispatchInfo.setEstimatedFee(s.getEstimatedfee());
                });

                List<DispatchResponseDTO.LoadingInfo> loadingInfoList = new ArrayList<>();

                List<ShipmentSectionDTO> shipList = shipmentSectionService.getShipmentSectionListByShipment(dto.getCompkey(), dto.getShipmentKey());
                for (ShipmentSectionDTO pickup : shipList) {
                    if (pickup.getVsttype() == VisitTypeEnum.PICKUP) {
                        DispatchResponseDTO.LoadingInfo loadingInfo = getLoadingInfo(pickup);

                        loadingInfoList.add(loadingInfo);
                    }
                }

                dispatchInfo.setLoadingInfoList(loadingInfoList);
            }

            dispatchInfoList.add(dispatchInfo);
        }

        return dispatchInfoList;
    }

    public List<ShipmentSectionDTO> combineProcess(List<ShipmentSectionDTO> shipList) {
        List<ShipmentSectionDTO> resultList = new ArrayList<>();
        if (shipList == null || shipList.isEmpty()) {
            return resultList;
        }
        ShipmentSectionDTO prevInfo = null;

        for (ShipmentSectionDTO currentInfo : shipList) {
            if (prevInfo == null ||
                    !prevInfo.getPtnamlc().equals(currentInfo.getPtnamlc()) ||
                    prevInfo.getVstordr() + 1 != currentInfo.getVstordr()) {
                resultList.add(currentInfo);
            }
            prevInfo = currentInfo;
        }

        return resultList;
    }

    public List<DispatchResponseDTO.TimeLineDTO> getTimeLineList(DispatchPlanDTO dto) {
        List<DispatchResponseDTO.TimeLineDTO> timeLineDTOList = new ArrayList<>();

        List<ShipmentSectionDTO> shipList = shipmentSectionService.getShipmentSectionListByShipment(dto.getCompkey(), dto.getShipmentKey());
        List<ShipmentSectionDTO> filteredList = combineProcess(shipList).stream()
                .sorted(Comparator.comparing(ShipmentSectionDTO::getVstordr))
                .toList();

        LocalDateTime estimatedDate = null;
        for (ShipmentSectionDTO ship : filteredList) {
            DispatchResponseDTO.TimeLineDTO timeLineDTO = new DispatchResponseDTO.TimeLineDTO();

            timeLineDTO.setVisitOrder(ship.getVstordr());
            timeLineDTO.setVisitType(ship.getVsttype());
            timeLineDTO.setRequestDate(ship.getLudrqdt());
            timeLineDTO.setRequestTime(ship.getLudrqtm());

            int requiredTime = Objects.nonNull(ship.getLudptim()) ? ship.getLudptim() : 0;
            if (Objects.nonNull(ship.getEstsecs())) {
                requiredTime += (int) Math.round(ship.getEstsecs() / 60.0);
            }
            timeLineDTO.setRequiredTime(requiredTime);

            if (estimatedDate == null) {
                estimatedDate = LocalDateTime.of(ship.getLudrqdt(), ship.getLudrqtm());
            } else {
                estimatedDate = estimatedDate.plusMinutes(requiredTime);
            }
            timeLineDTO.setEstimatedDate(estimatedDate.toLocalDate());
            timeLineDTO.setEstimatedTime(estimatedDate.toLocalTime());

            timeLineDTO.setCompanyName(ship.getPtnamlc());
            if (ship.getPtnrtyp().equals(PartnerTypeEnum.CUSTOMER)) {
                timeLineDTO.setDropOffArea(ship.getDenamlc());
            }

            timeLineDTOList.add(timeLineDTO);
        }

        return timeLineDTOList.stream()
                .sorted(Comparator.comparing(DispatchResponseDTO.TimeLineDTO::getVisitOrder))
                .toList();
    }

    public DispatchResponseDTO.DispatchDetailDTO getDispatchDetail(String shipmentKey) {
        DispatchResponseDTO.DispatchDetailDTO detailDTO = new DispatchResponseDTO.DispatchDetailDTO();

        List<DispatchPlan> entity = dispatchPlanRepository.findByShipmentKey(shipmentKey);
        DispatchPlanDTO dto = dispatchPlanMapper.toDto(entity.get(0));
        DispatchResponseDTO.DispatchDriverVehicleInfo driverVehicleInfo = getDriverVehicleInfo(dto);
        List<DispatchResponseDTO.TimeLineDTO> timeLineList = getTimeLineList(dto);
        int totalRequiredTime = timeLineList.stream()
                .mapToInt(DispatchResponseDTO.TimeLineDTO::getRequiredTime)
                .sum();
        List<ShipmentSectionDTO> shipList = shipmentSectionService.getShipmentSectionListByShipment(dto.getCompkey(), dto.getShipmentKey());
        Optional<ShipmentDTO> shipmentDTO = shipmentService.getShipment2(dto.getCompkey(), dto.getShipmentKey());

        shipmentDTO.ifPresent(s -> {
            detailDTO.setLoadrat(s.getLoadrat());
            detailDTO.setEstimatedFee(s.getEstimatedfee());
        });
        detailDTO.setDispatchPlanId(dto.getDispatchPlanId());
        detailDTO.setRequestDate(dto.getRequestDate());
        detailDTO.setTransportRound(dto.getTransportRound());
        detailDTO.setShipmentKey(shipmentKey);
        detailDTO.setCategory(dto.getCargoty());
        detailDTO.setDriverVehicleInfo(driverVehicleInfo);
        detailDTO.setTimeLineList(timeLineList);
        detailDTO.setTotalRequiredTime(totalRequiredTime);

        List<DispatchResponseDTO.LoadingInfo> loadingInfoList = new ArrayList<>();
        List<DispatchResponseDTO.DispatchDeliveryDTO> deliveryDTOList = new ArrayList<>();

        for (ShipmentSectionDTO pickup : shipList) {
            if (pickup.getVsttype() == VisitTypeEnum.PICKUP) {
                ShipmentSectionDTO dropOff = shipList.stream()
                        .filter(s -> Objects.equals(s.getOetmsky(), pickup.getOetmsky()))
                        .filter(s -> s.getVsttype() != pickup.getVsttype())
                        .toList().get(0);

                DispatchResponseDTO.LoadingInfo loadingInfo = getLoadingInfo(pickup);
                DispatchResponseDTO.DispatchDeliveryDTO deliveryDTO = getDeliveryInfo(dto, pickup, dropOff);

                deliveryDTO.setLoadingInfo(loadingInfo);

                loadingInfoList.add(loadingInfo);
                deliveryDTOList.add(deliveryDTO);
            }
        }

        detailDTO.setLoadingInfoList(loadingInfoList);
        detailDTO.setDeliveryDTOList(deliveryDTOList);

        return detailDTO;
    }

    public DispatchResponseDTO.LoadingInfo getLoadingInfo(ShipmentSectionDTO ship) {
        DispatchResponseDTO.LoadingInfo loadingInfo = new DispatchResponseDTO.LoadingInfo();

        loadingInfo.setOpkcate(ship.getOpkcate());
        loadingInfo.setOpktype(ship.getOpktype());
        loadingInfo.setOpakqty(ship.getOpakqty());
        loadingInfo.setPakwidh(ship.getPakwidh());
        loadingInfo.setPakdept(ship.getPakdept());
        loadingInfo.setPakheig(ship.getPakheig());
        loadingInfo.setPkrweig(ship.getPkrweig());

        return loadingInfo;
    }

    public DispatchResponseDTO.DispatchDeliveryDTO getDeliveryInfo(DispatchPlanDTO dto, ShipmentSectionDTO pickup, ShipmentSectionDTO dropOff) {
        DispatchResponseDTO.DispatchDeliveryDTO deliveryDTO = new DispatchResponseDTO.DispatchDeliveryDTO();

        deliveryDTO.setOetmsky(pickup.getOetmsky());
        deliveryDTO.setTransportOrder(pickup.getVstordr());
        deliveryDTO.setCategory(dto.getCargoty());

        String concatenatedString = Stream.of(pickup.getUnavat1(), dropOff.getUnavat1())
                .map(ShipmentUtils::convertUnavat1Code)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.joining(", "));
        if (!concatenatedString.isEmpty())
            deliveryDTO.setRestriction(concatenatedString);
        deliveryDTO.setPickupRestriction(ShipmentUtils.convertUnavat1Code(pickup.getUnavat1()));
        deliveryDTO.setDropOffRestriction(ShipmentUtils.convertUnavat1Code(dropOff.getUnavat1()));

        deliveryDTO.setPickupCompanyName(pickup.getPtnamlc());
        deliveryDTO.setPickupRequestDate(pickup.getLudrqdt());
        deliveryDTO.setPickupRequestTime(pickup.getLudrqtm());

        deliveryDTO.setDropOffCompanyName(dropOff.getPtnamlc());
        deliveryDTO.setDropOffRequestDate(dropOff.getLudrqdt());
        deliveryDTO.setDropOffRequestTime(dropOff.getLudrqtm());

        return deliveryDTO;
    }

    @Transactional
    public DispatchPlanDTO updateDriver(final Long dispatchPlanId,
                                        DispatchPlanDTO updateDispatchDTO) {

        return dispatchPlanRepository.findById(dispatchPlanId)
                .map(dp -> {
                    final DispatchPlan entity = dispatchPlanMapper.toEntity(updateDispatchDTO);
                    return dispatchPlanMapper.toDto(dispatchPlanRepository.save(entity));
                })
                .orElseThrow(() -> new RuntimeException("DispatchPlan not be found with dispatchPlanId: " + dispatchPlanId));
    }

    public List<DispatchResponseDTO.RouteSummaryDTO> getRouteSummary(LocalDate requestDate, int transportRound, String companyName) {
        List<DispatchResponseDTO.RouteSummaryDTO> routeSummaryList = new ArrayList<>();

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        List<DispatchPlanDTO> dispatchInfoList =
                dispatchPlanRepository.findByRequestDateAndTransportRoundAndDropOffCompanyName(requestDate, transportRound, companyName)
                .stream()
                .map(dispatchPlanMapper::toDto)
                .filter(d -> d.getIsManual().equals(false))
                .filter(d -> d.getPartnerKey().equals(Objects.requireNonNull(authDTO).getPartnerKey()))
                .toList();
        if (dispatchInfoList.isEmpty())
            return routeSummaryList;

        for (DispatchPlanDTO dto : dispatchInfoList) {
            DispatchResponseDTO.RouteSummaryDTO routeSummary = new DispatchResponseDTO.RouteSummaryDTO();

            DispatchResponseDTO.RouteDispatchDTO routeDispatch = new DispatchResponseDTO.RouteDispatchDTO();
            routeDispatch.setIsConfirmed(dto.getIsConfirmed());
            routeDispatch.setIsDispatched(dto.getVehicleId() != null);
            routeDispatch.setCategory(dto.getCargoty());
            routeDispatch.setDriverVehicleInfo(getDriverVehicleInfo(dto));


            routeSummary.setShipmentKey(dto.getShipmentKey());
            routeSummary.setRouteDispatch(routeDispatch);

            List<DispatchResponseDTO.RouteOrderDTO> routeOrderList = getRouteOrderList(dto);
            routeSummary.setRouteOrderList(routeOrderList);

            LbsRouteOnDemandResponse response = getRoutFromLBS(routeOrderList);
            routeSummary.setRoute(response.getRoutePathList().get(0));

           routeSummaryList.add(routeSummary);
        }

        return routeSummaryList;
    }

    public List<DispatchResponseDTO.RouteOrderDTO> getRouteOrderList(DispatchPlanDTO dto) {
        List<DispatchResponseDTO.RouteOrderDTO> routeOrderList = new ArrayList<>();
        List<ShipmentSectionDTO> shipList = shipmentSectionService.getShipmentSectionListByShipment(dto.getCompkey(), dto.getShipmentKey());
        for (ShipmentSectionDTO ship : shipList) {
            DispatchResponseDTO.RouteOrderDTO routeOrder = getRouteOrder(ship);

            routeOrderList.add(routeOrder);
        }

        return routeOrderList;
    }

    public DispatchResponseDTO.RouteOrderDTO getRouteOrder(ShipmentSectionDTO ship) {
        DispatchResponseDTO.RouteOrderDTO routeOrder = new DispatchResponseDTO.RouteOrderDTO();

        routeOrder.setVisitOrder(ship.getVstordr());
        routeOrder.setVisitType(ship.getVsttype());
        routeOrder.setOetmsky(ship.getOetmsky());
        routeOrder.setCompanyName(ship.getPtnamlc());
        routeOrder.setCompanyAddress(ship.getPtaddr());
        routeOrder.setCompanyPoint(ship.getDecoord());
        if (ship.getPtnrtyp().equals(PartnerTypeEnum.CUSTOMER)) {
            routeOrder.setDropOffArea(ship.getDenamlc());
        }
        return routeOrder;
    }

    public LbsRouteOnDemandResponse getRoutFromLBS(List<DispatchResponseDTO.RouteOrderDTO> routeOrderList) {
        List<LbsRouteDestination> destinations = new ArrayList<>();
        for (DispatchResponseDTO.RouteOrderDTO dto : routeOrderList) {
            LbsRouteDestination destination = LbsRouteDestination.builder()
                    .nId((long) dto.getVisitOrder())
                    .coordinate(Objects.requireNonNull(GeometryUtils.createPoint(dto.getCompanyPoint().getX(), dto.getCompanyPoint().getY())))
                    .build();
            destinations.add(destination);
        }
        Double startX = Objects.requireNonNull(destinations.stream()
                        .min(Comparator.comparingLong(LbsRouteDestination::getNId))
                        .orElse(null))
                .getCoordinate().getX();
        Double startY = Objects.requireNonNull(destinations.stream()
                        .min(Comparator.comparingLong(LbsRouteDestination::getNId))
                        .orElse(null))
                .getCoordinate().getY();

        final LbsRouteOnDemandRequest requestBody = LbsRouteOnDemandRequest.builder()
                .requestId(Long.valueOf(NumberUtils.generateRandomNumeric(9)))
                .start(Objects.requireNonNull(GeometryUtils.createPoint(startX, startY)))
                .routeOption(LbsConstant.ROUTE_OPTION_DEFAULT)
                .destinations(destinations)
                .build();

        return lbsRouteService.routeOnDemand(requestBody);
    }

    public LbsRouteOnDemandResponse modifyRouteOrder(String shipmentKey, List<Integer> visitOrderList) {
        DispatchPlan entity = dispatchPlanRepository.findByShipmentKeyAndIsManual(shipmentKey, false);
        DispatchPlanDTO dto = dispatchPlanMapper.toDto(entity);

        if (dto.getIsConfirmed()) {
            log.info("skip, modifyRouteOrder: {} is already confirmed.", shipmentKey);
            return null;
        }
        List<DispatchResponseDTO.RouteOrderDTO> routeOrderList = getRouteOrderList(dto);
        for (int i = 0; i < routeOrderList.size(); i++) {
            DispatchResponseDTO.RouteOrderDTO orderDTO = routeOrderList.get(i);
            orderDTO.setVisitOrder(visitOrderList.get(i));
        }

        return getRoutFromLBS(routeOrderList);
    }

    @Transactional
    public Boolean confirmRouteOrder(String shipmentKey, List<Integer> visitOrderList) {
        DispatchPlan entity = dispatchPlanRepository.findByShipmentKeyAndIsManual(shipmentKey, false);
        DispatchPlanDTO dto = dispatchPlanMapper.toDto(entity);

        if (dto.getIsConfirmed()) {
            log.info("skip, confirmRouteOrder: {} is already confirmed.", shipmentKey);
            return false;
        }
        List<ShipmentSectionDTO> shipList = shipmentSectionService.getShipmentSectionListByShipment(dto.getCompkey(), dto.getShipmentKey());

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        for (ShipmentSectionDTO ship : shipList) {
            int index = visitOrderList.indexOf(ship.getVstordr()) + 1;
            ship.setVstordr(index);

            if (Objects.nonNull(authDTO)) {
                ship.setLmouser(authDTO.getId());
            }
        }
        int count = shipmentSectionService.saveAll(shipList);
        return count == shipList.size();
    }

    public void saveCallRoute(DispatchPlanDTO dto, LbsRouteOnDemandResponse response) {
        List<?> result = callRouteService.findByVehicleRequestDateAndRound(dto.getVehicleId(), dto.getRequestDate(), dto.getTransportRound());
        if (result.isEmpty()) {
            CallRouteCustomDTO.CallRouteInnerAddDTO addDTO = new CallRouteCustomDTO.CallRouteInnerAddDTO();

            addDTO.setRequestDate(dto.getRequestDate());
            addDTO.setTransportRound(dto.getTransportRound());
            addDTO.setShpmtky(dto.getShipmentKey());
            addDTO.setTransportId(0L);
            addDTO.setVehicleId(dto.getVehicleId());
            addDTO.setRoute(response.getRoutePathList().get(0));

            int totalEstimatedMeters = response.getVisitList().stream()
                    .mapToInt(LbsRouteOnDemandResponse.LbsRouteOrderResult::getEstimatedMeters).sum();
            int totalEstimatedSecs = response.getVisitList().stream()
                    .mapToInt(LbsRouteOnDemandResponse.LbsRouteOrderResult::getEstimatedSecs).sum();
            addDTO.setExpectedDistance(totalEstimatedMeters);
            addDTO.setExpectedTime(totalEstimatedSecs);

            callRouteService.innerSave(addDTO);
        } else {
            CallRouteCustomDTO.UpdateRouteOne updateDto = new CallRouteCustomDTO.UpdateRouteOne();

            updateDto.setShpmtky(dto.getShipmentKey());
            updateDto.setRoute(response.getRoutePathList().get(0));

            int totalEstimatedMeters = response.getVisitList().stream()
                    .mapToInt(LbsRouteOnDemandResponse.LbsRouteOrderResult::getEstimatedMeters).sum();
            int totalEstimatedSecs = response.getVisitList().stream()
                    .mapToInt(LbsRouteOnDemandResponse.LbsRouteOrderResult::getEstimatedSecs).sum();
            updateDto.setExpectedDistance(totalEstimatedMeters);
            updateDto.setExpectedTime(totalEstimatedSecs);

            callRouteService.updateOne(updateDto);
        }

    }

    @Transactional
    public Boolean confirmDispatch(String shipmentKey, int rentalCharge) {
        DispatchPlan entity = dispatchPlanRepository.findByShipmentKeyAndIsManual(shipmentKey, false);
        DispatchPlanDTO dto = dispatchPlanMapper.toDto(entity);

        if ((dto.getDriverId() == null) || (dto.getVehicleId() == null)) {
            log.info("confirmDispatch: {} driver has not been set up yet.", shipmentKey);
            return false;
        }
        if (dto.getIsConfirmed()) {
            log.info("confirmDispatch: {} is already confirmed.", shipmentKey);
            return false;
        }

        dto.setIsConfirmed(true);

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            dto.setLmouser(authDTO.getId());
        }

        DispatchPlan modifyEntity = dispatchPlanMapper.toEntity(dto);
        dispatchPlanRepository.save(modifyEntity);

        if (rentalCharge != 0) {
            qDispatchPlanRepository.updateRentalCharge(shipmentKey, rentalCharge);
        }

        List<DispatchResponseDTO.RouteOrderDTO> routeOrderList = getRouteOrderList(dto);
        LbsRouteOnDemandResponse lbsResponse = getRoutFromLBS(routeOrderList);
        saveCallRoute(dto, lbsResponse);

        Optional<DriverInfoDTO.DriverDetailInfo> driverDetailInfo =
                driverInfoCustomService.findByUserasqWithDriverHolidayInfo(Objects.requireNonNull(dto).getDriverId());
        driverDetailInfo.ifPresent(s -> {
            List<ShipmentSectionDTO> shipList = shipmentSectionService.getShipmentSectionListByShipment(dto.getCompkey(), dto.getShipmentKey());
            for (ShipmentSectionDTO ship : shipList) {
                String weightString = s.getDriverAllInfo().getVehicleWeight().getEnglish();
                qDispatchPlanRepository.updateDispatchCompleted(ship.getShipment().getShpmtky(), ship.getOetmsky(),
                        s.getDriverAllInfo().getRegistrationNumber(), weightString, false);
            }
        });

        TransportInfoUpdateByDispatch.ByShipmentDTO updateByShipmentDTO = new TransportInfoUpdateByDispatch.ByShipmentDTO();
        updateByShipmentDTO.setCompkey(dto.getCompkey());
        updateByShipmentDTO.setShipmentKey(dto.getShipmentKey());
        updateByShipmentDTO.setDispatchId(dto.getDispatchPlanId());
        updateByShipmentDTO.setVehicleId(dto.getVehicleId());
        updateByShipmentDTO.setTransportRound(dto.getTransportRound());
        updateByShipmentDTO.setPartnerKey(dto.getPartnerKey());

        transportInfoService.saveByShipment(updateByShipmentDTO, lbsResponse);

        pushService.sendNewTransport(dto.getDriverId());

        String useract = "", username = "", telphnm = "";
        if(driverDetailInfo.isPresent()) {
            DriverInfoDTO.DriverAllInfo info = driverDetailInfo.get().getDriverAllInfo();
            useract = info.getUseract();
            username = info.getUsernam();
            telphnm = info.getTelphnm();
        };

        tmsToVmsService.postDispatchConfirmed(shipmentKey, useract, username, telphnm);

        return true;
    }

    @Transactional
    public Boolean cancelDispatch(List<String> shipmentKeyList, Boolean isCheck) {
        if (shipmentKeyList.isEmpty()) {
            log.info("cancelDispatch: shipmentKeyList are empty.");
            return false;
        }

        List<DispatchPlan> entities = new ArrayList<>();
        for (String shipmentKey : shipmentKeyList) {
            DispatchPlan entity = dispatchPlanRepository.findByShipmentKeyAndIsManual(shipmentKey, false);
            DispatchPlanDTO dto = dispatchPlanMapper.toDto(entity);
            if (Objects.isNull(dto)) {
                log.info("cancelDispatch: shipmentKey {} not found.", shipmentKey);
                return false;
            }
            if (dto.getIsConfirmed()) {
                log.info("cancelDispatch: shipmentKey {} is already confirmed.", shipmentKey);
                return false;
            }
            entities.add(entity);
        }

        if (!isCheck)
            dispatchPlanRepository.deleteAll(entities);
        return true;
    }

    @Transactional
    public Boolean cancelSelectedDispatch(String shipmentKey) {
        DispatchPlan entity = dispatchPlanRepository.findByShipmentKeyAndIsManual(shipmentKey, false);
        DispatchPlanDTO dto = dispatchPlanMapper.toDto(entity);
        if (Objects.isNull(dto)) {
            log.info("cancelDispatch: shipmentKey {} not found.", shipmentKey);
            return false;
        }
        if (dto.getIsConfirmed()) {
            log.info("cancelDispatch: shipmentKey {} is already confirmed.", shipmentKey);
            return false;
        }

        dispatchPlanRepository.delete(entity);
        return true;
    }

    public List<String> cancelCheckDispatch(List<String> shipmentKeyList) {
        if(shipmentKeyList.isEmpty()) {
            return null;
        }

        List<String> confirmList = new ArrayList<>();
        for (String shipmentKey : shipmentKeyList) {
            DispatchPlan entity = dispatchPlanRepository.findByShipmentKeyAndIsManual(shipmentKey, false);
            DispatchPlanDTO dto = dispatchPlanMapper.toDto(entity);
            if (Objects.nonNull(dto) && dto.getIsConfirmed()) {
                confirmList.add(shipmentKey);
            }
        }
        return confirmList;
    }
}
