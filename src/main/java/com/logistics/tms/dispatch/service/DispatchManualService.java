package com.logistics.tms.dispatch.service;

import com.logistics.tms.common.enumeration.PartnerTypeEnum;
import com.logistics.tms.common.enumeration.VisitTypeEnum;
import com.logistics.tms.common.util.AuthUtils;
import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.dispatch.constant.DispatchConstant;
import com.logistics.tms.dispatch.dto.DispatchAddDTO;
import com.logistics.tms.dispatch.dto.DispatchPlanDTO;
import com.logistics.tms.dispatch.dto.DispatchResponseDTO;
import com.logistics.tms.dispatch.dto.ManualResponseDTO;
import com.logistics.tms.dispatch.entity.DispatchPlan;
import com.logistics.tms.dispatch.mapper.DispatchPlanMapper;
import com.logistics.tms.dispatch.repository.DispatchManualRepository;
import com.logistics.tms.dispatch.repository.DispatchPlanRepository;
import com.logistics.tms.dispatch.repository.QDispatchPlanRepository;
import com.logistics.tms.driver.dto.DriverInfoDTO;
import com.logistics.tms.driver.service.DriverInfoCustomService;
import com.logistics.tms.lbs.domain.route.LbsRouteOnDemandResponse;
import com.logistics.tms.push.service.PushService;
import com.logistics.tms.shipment.constant.ShipmentUtils;
import com.logistics.tms.shipment.dto.ShipmentDTO;
import com.logistics.tms.shipment.dto.ShipmentSectionDTO;
import com.logistics.tms.shipment.service.ShipmentSectionService;
import com.logistics.tms.shipment.service.ShipmentService;
import com.logistics.tms.transport.dto.TransportInfoDTO;
import com.logistics.tms.transport.dto.TransportInfoUpdateByDispatch;
import com.logistics.tms.transport.service.TmsToVmsService;
import com.logistics.tms.transport.service.TransportInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.logistics.tms.shipment.constant.ShipmentConstant.SHIPMENT_ROUND_ENDTIME;

@Slf4j
@Validated
@RequiredArgsConstructor
@Transactional(readOnly = true)
@Service
public class DispatchManualService {

    private final DispatchPlanRepository dispatchPlanRepository;
    private final QDispatchPlanRepository qDispatchPlanRepository;
    private final DispatchPlanMapper dispatchPlanMapper;
    private final ShipmentSectionService shipmentSectionService;
    private final DispatchManualRepository dispatchManualRepository;
    private final DispatchPlanService dispatchPlanService;
    private final DriverInfoCustomService driverInfoCustomService;
    private final TransportInfoService transportInfoService;
    private final PushService pushService;
    private final ShipmentService shipmentService;
    private final TmsToVmsService tmsToVmsService;

    @Transactional
    public void saveByManual(String compkey, String oetmsky, String shipmentKey) {
        List<DispatchPlan> existEntity = dispatchManualRepository.findByCompkeyAndOetmskyAndIsManualAndShipmentKey(compkey, oetmsky, true, shipmentKey);
        if (!existEntity.isEmpty()) {
            log.info("saveByManual : Skip because {} {} already exists.", oetmsky, shipmentKey);
            throw new RuntimeException("saveByManual : Skip because " + oetmsky + " " + shipmentKey + " already exists.");
        }

        ShipmentSectionDTO pickup = shipmentSectionService.getShipmentSectionByOetmsky(compkey, oetmsky, VisitTypeEnum.PICKUP);
        ShipmentSectionDTO ship = shipmentSectionService.getShipmentSectionByOetmsky(compkey, oetmsky, VisitTypeEnum.DROPOFF);
        if (Objects.isNull(pickup) || Objects.isNull(ship)) {
            log.info("saveByManual : Cancel because {} {} is not exist in shipmentSection", oetmsky, shipmentKey);
            throw new RuntimeException("saveByManual : Cancel because " + oetmsky + " " + shipmentKey + " is not exist in shipmentSection");
        }

        DispatchAddDTO.OetmskyDTO addDTO = new DispatchAddDTO.OetmskyDTO();
        addDTO.setCompkey(compkey);
        addDTO.setShipmentKey(ship.getShipment().getShpmtky());
        addDTO.setOetmsky(oetmsky);
        addDTO.setIsManual(true);
        addDTO.setRequestDate(ship.getLudrqdt());
        addDTO.setTransportRound(ShipmentUtils.getRoundPuDf(pickup.getLudrqdt(),pickup.getLudrqtm(),ship.getLudrqdt(),ship.getLudrqtm()));
        addDTO.setDropOffCompanyName(ship.getPtnamlc());
        addDTO.setCargoty(ship.getShipment().getCargoty());
        addDTO.setPartnerKey(ship.getShipment().getShipmentPlan().getPtnrkey());

        DispatchPlanDTO dto = new DispatchPlanDTO(addDTO);

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            dto.setCreuser(authDTO.getId());
        }

        DispatchPlan entity = dispatchPlanMapper.toEntity(dto);
        dispatchPlanRepository.save(entity);
    }

    @Deprecated
    public int getRound(LocalTime requestTime) {
        int transportRound;

        if (requestTime == null) {
            return 1;
        } else {
            if (requestTime.isBefore(SHIPMENT_ROUND_ENDTIME[1])) {
                transportRound = 1;
            } else if (requestTime.isBefore(SHIPMENT_ROUND_ENDTIME[2])) {
                transportRound = 2;
            } else {
                transportRound = 3;
            }
        }
        return transportRound;
    }

    @Deprecated
    public int getRoundNew(ShipmentSectionDTO pickup, ShipmentSectionDTO dropOff) {
        return ShipmentUtils.getRoundPuDf(pickup, dropOff);
    }

    public List<ManualResponseDTO.ManualDTO> getManualSummary() {
        List<ManualResponseDTO.ManualDTO> manualDTOList = new ArrayList<>();

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        List<DispatchPlanDTO> manualList = dispatchManualRepository.findByIsManualAndIsConfirmed(true, false)
                .stream().map(dispatchPlanMapper::toDto)
                .filter(d -> d.getPartnerKey().equals(Objects.requireNonNull(authDTO).getPartnerKey()))
                .toList();
        if (manualList.isEmpty())
            return manualDTOList;

        for (DispatchPlanDTO dto : manualList) {
            ManualResponseDTO.ManualDTO manualDTO = new ManualResponseDTO.ManualDTO();

            List<ShipmentSectionDTO> shipList = shipmentSectionService.getShipmentSectionListByOetmsky(dto.getCompkey(), dto.getOetmsky())
                    .stream()
                    .toList();
            if (shipList.isEmpty())
                continue;
            ShipmentSectionDTO pickup = shipList.stream()
                    .filter(s -> s.getVsttype().equals(VisitTypeEnum.PICKUP))
                    .toList().get(0);
            ShipmentSectionDTO dropOff = shipList.stream()
                    .filter(s -> s.getVsttype().equals(VisitTypeEnum.DROPOFF))
                    .toList().get(0);

            DispatchResponseDTO.LoadingInfo loadingInfo = dispatchPlanService.getLoadingInfo(pickup);
            DispatchResponseDTO.DispatchDeliveryDTO deliveryDTO = dispatchPlanService.getDeliveryInfo(dto, pickup, dropOff);

            deliveryDTO.setLoadingInfo(loadingInfo);

            manualDTO.setDispatchPlanId(dto.getDispatchPlanId());
            manualDTO.setDeliveryDTO(deliveryDTO);
            ManualResponseDTO.ManualDriverInfo manualDriverInfo = getManualDriver(dto, false);
            manualDTO.setManualDriverInfo(manualDriverInfo);

            manualDTOList.add(manualDTO);
        }

        return manualDTOList;
    }

    public ManualResponseDTO.ManualDriverInfo getManualDriver(DispatchPlanDTO dto, boolean withTimeLine) {
        if (dto.getVehicleId() != null) {
            ManualResponseDTO.ManualDriverInfo manualDriverInfo = new ManualResponseDTO.ManualDriverInfo();

            DispatchResponseDTO.DispatchDriverVehicleInfo driverVehicleInfo = dispatchPlanService.getDriverVehicleInfo(dto);

            List<DispatchPlan> other = dispatchManualRepository.findByDriverIdAndRequestDateAndTransportRoundAndIsConfirmed(
                    dto.getDriverId(), dto.getRequestDate(), dto.getTransportRound(), true);
            if (!other.isEmpty()) {
                DispatchPlanDTO otherDTO = dispatchPlanMapper.toDto(other.get(0));

                if (Objects.nonNull(otherDTO)) {
                    DispatchResponseDTO.DispatchDetailDTO detailDTO = dispatchPlanService.getDispatchDetail(otherDTO.getShipmentKey());

                    manualDriverInfo.setLoadRate(detailDTO.getLoadrat());
                    manualDriverInfo.setLoadingInfoList(detailDTO.getLoadingInfoList());
                    if (withTimeLine) {
                        manualDriverInfo.setTimeLineList(detailDTO.getTimeLineList());
                    }
                }
            }
            manualDriverInfo.setDriverVehicleInfo(driverVehicleInfo);

            return manualDriverInfo;
        } else {
            return null;
        }
    }

    public List<ManualResponseDTO.ManualDriverInfo> getManualDriverList(Long dispatchPlanId, String partnerKey) {
        List<ManualResponseDTO.ManualDriverInfo> manualDriverList = new ArrayList<>();

        DispatchPlan entity = dispatchManualRepository.findByDispatchPlanId(dispatchPlanId);
        DispatchPlanDTO dto = dispatchPlanMapper.toDto(entity);

//        List<DispatchPlanDTO> exclusion = dispatchManualRepository.findByRequestDateAndTransportRoundAndIsManual(dto.getRequestDate(), dto.getTransportRound(), true)
//                .stream()
//                .map(dispatchPlanMapper::toDto)
//                .toList();
//        List<Long> driverIds = exclusion.stream()
//                .map(DispatchPlanDTO::getDriverId)
//                .filter(Objects::nonNull).toList();

        List<DriverInfoDTO.DriverDetailInfo> driverDetailInfos = driverInfoCustomService.findByPartnerKeyWithHoliday(partnerKey, null);
        for(DriverInfoDTO.DriverDetailInfo driverDetailInfo : driverDetailInfos) {
            ManualResponseDTO.ManualDriverInfo driverInfo = new ManualResponseDTO.ManualDriverInfo();

            driverInfo.setDriverVehicleInfo(dispatchPlanService.getDriverDTO(driverDetailInfo, dto.getRequestDate(), null));

            List<DispatchPlan> other = dispatchManualRepository.findByDriverIdAndRequestDateAndTransportRoundAndIsConfirmed(
                    driverDetailInfo.getDriverAllInfo().getUserasq(), dto.getRequestDate(), dto.getTransportRound(), true);
            if (!other.isEmpty()) {
                DispatchPlanDTO otherDTO = dispatchPlanMapper.toDto(other.get(0));

                if (Objects.nonNull(otherDTO)) {
                    Optional<ShipmentDTO> otherShipmentDTO = shipmentService.getShipment2(otherDTO.getCompkey(), otherDTO.getShipmentKey());
                    AtomicBoolean otherShipyard = new AtomicBoolean(false);
                    otherShipmentDTO.ifPresent(o -> {
                        if (o.getPtnrtyp() == PartnerTypeEnum.CUSTOMER)
                            otherShipyard.set(true);
                    });
                    if (otherShipyard.get()) {
                        Optional<ShipmentDTO> shipmentDTO = shipmentService.getShipment2(dto.getCompkey(), dto.getShipmentKey());
                        AtomicBoolean isShipyard = new AtomicBoolean(false);
                        shipmentDTO.ifPresent(s -> {
                            if (s.getPtnrtyp() == PartnerTypeEnum.CUSTOMER)
                                isShipyard.set(true);
                        });
                        if (isShipyard.get()) {
                            if (!otherDTO.getDropOffCompanyName().equals(dto.getDropOffCompanyName()))
                                continue;
                        }
                    }
                    List<TransportInfoDTO> transportDTOList = transportInfoService.getTransportByShipmentKey(otherDTO.getShipmentKey());
                    boolean allCompleted = transportDTOList.stream()
                            .allMatch(d -> DispatchConstant.NOT_ALLOWED_STATUS.contains(d.getTransportStatus()));
                    if (allCompleted)
                        continue;

                    DispatchResponseDTO.DispatchDetailDTO detailDTO = dispatchPlanService.getDispatchDetail(otherDTO.getShipmentKey());

                    driverInfo.setLoadRate(detailDTO.getLoadrat());
                    driverInfo.setLoadingInfoList(detailDTO.getLoadingInfoList());
                    driverInfo.setTimeLineList(detailDTO.getTimeLineList());

                    int totalRequiredTime = detailDTO.getTimeLineList().stream()
                            .mapToInt(DispatchResponseDTO.TimeLineDTO::getRequiredTime)
                            .sum();
                    driverInfo.setTotalRequiredTime(totalRequiredTime);
                    driverInfo.setCategory(detailDTO.getCategory());
                }
            }

            manualDriverList.add(driverInfo);
        }

        return manualDriverList;
    }

    @Transactional
    public DispatchPlanDTO updateDriver(final Long dispatchPlanId,
                                        DispatchPlanDTO updateDispatchDTO) {

        return dispatchPlanRepository.findById(dispatchPlanId)
                .map(dp -> {
                    final DispatchPlan entity = dispatchPlanMapper.toEntity(updateDispatchDTO);
                    return dispatchPlanMapper.toDto(dispatchPlanRepository.save(entity));
                })
                .orElseThrow(() -> new RuntimeException("DispatchPlan not be found with dispatchPlanId: " + dispatchPlanId));
    }

    @Transactional
    public Boolean confirmManual(Long dispatchPlanId) {
        Optional<DispatchPlanDTO> dispatchPlanDTO = dispatchPlanService.findById(dispatchPlanId);
        if (dispatchPlanDTO.isPresent()) {
            DispatchPlanDTO dto = dispatchPlanDTO.get();
            if ((dto.getDriverId() == null) || (dto.getVehicleId() == null))
                return false;

            dto.setIsConfirmed(true);

            final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
            if (Objects.nonNull(authDTO)) {
                dto.setLmouser(authDTO.getId());
            }

            String shipmentKey = dto.getShipmentKey();
            List<DispatchPlan> other = dispatchManualRepository.findByDriverIdAndRequestDateAndTransportRoundAndIsConfirmed(
                    dto.getDriverId(), dto.getRequestDate(), dto.getTransportRound(), true);
            TransportInfoUpdateByDispatch.ByTransportCodeDTO updateByTransportCodeDTO = new TransportInfoUpdateByDispatch.ByTransportCodeDTO();
            if (!other.isEmpty()) {
                DispatchPlanDTO otherDTO = dispatchPlanMapper.toDto(other.get(0));
                if (Objects.nonNull(otherDTO)) {
                    List<ShipmentSectionDTO> otherShipList = shipmentSectionService.getShipmentSectionListByShipment(otherDTO.getCompkey(), otherDTO.getShipmentKey());
                    if (!otherShipList.isEmpty()) {
                        OptionalInt maxVisitOrder = otherShipList.stream()
                                .mapToInt(ShipmentSectionDTO::getVstordr)
                                .max();
                        int maxOrder = maxVisitOrder.getAsInt();
                        changeShipment(dto.getCompkey(), dto.getShipmentKey(), dto.getOetmsky(), otherDTO.getShipmentKey(), maxOrder + 1);

                        updateByTransportCodeDTO = getByTransportCodeDTO(otherDTO);
                        updateByTransportCodeDTO.setPickupOrder(maxOrder + 1);
                        updateByTransportCodeDTO.setDropOffOrder(maxOrder + 2);
                        updateByTransportCodeDTO.setDispatchId(dto.getDispatchPlanId());
                        updateByTransportCodeDTO.setTransportCode(dto.getOetmsky());
                        updateByTransportCodeDTO.setPartnerKey(dto.getPartnerKey());

                        shipmentKey = updateByTransportCodeDTO.getShipmentKey();
                    }
                }
            } else {
                updateByTransportCodeDTO = getByTransportCodeDTO(dto);
                updateByTransportCodeDTO.setPickupOrder(1);
                updateByTransportCodeDTO.setDropOffOrder(2);
                updateByTransportCodeDTO.setDispatchId(dto.getDispatchPlanId());
                updateByTransportCodeDTO.setPartnerKey(dto.getPartnerKey());
            }

            Optional<DriverInfoDTO.DriverDetailInfo> driverDetailInfo =
                    driverInfoCustomService.findByUserasqWithDriverHolidayInfo(Objects.requireNonNull(dto).getDriverId());
            String finalShipmentKey = shipmentKey;
            driverDetailInfo.ifPresent(s -> {
                String weightString = s.getDriverAllInfo().getVehicleWeight().getEnglish();
                qDispatchPlanRepository.updateDispatchCompleted(finalShipmentKey, dto.getOetmsky(),
                        s.getDriverAllInfo().getRegistrationNumber(), weightString, true);
            });

            dto.setShipmentKey(shipmentKey);
            DispatchPlan modifyEntity = dispatchPlanMapper.toEntity(dto);
            dispatchPlanRepository.save(modifyEntity);

            List<DispatchResponseDTO.RouteOrderDTO> routeOrderList = dispatchPlanService.getRouteOrderList(dto);
            TransportInfoUpdateByDispatch.ByTransportCodeDTO finalUpdateByTransportCodeDTO = updateByTransportCodeDTO;
            routeOrderList.stream()
                    .filter(d -> d.getOetmsky().equals(finalUpdateByTransportCodeDTO.getTransportCode())
                            && d.getVisitType() == VisitTypeEnum.PICKUP)
                    .forEach(d -> d.setVisitOrder(finalUpdateByTransportCodeDTO.getPickupOrder()));
            routeOrderList.stream()
                    .filter(d -> d.getOetmsky().equals(finalUpdateByTransportCodeDTO.getTransportCode())
                            && d.getVisitType() == VisitTypeEnum.DROPOFF)
                    .forEach(d -> d.setVisitOrder(finalUpdateByTransportCodeDTO.getDropOffOrder()));
            LbsRouteOnDemandResponse lbsResponse = dispatchPlanService.getRoutFromLBS(routeOrderList);
            dispatchPlanService.saveCallRoute(dto, lbsResponse);

            transportInfoService.saveByTransportCode(updateByTransportCodeDTO, lbsResponse);

            pushService.sendNewTransport(dto.getDriverId());

            String useract = "", username = "", telphnm = "";
            if (driverDetailInfo.isPresent()) {
                DriverInfoDTO.DriverAllInfo info = driverDetailInfo.get().getDriverAllInfo();
                useract = info.getUseract();
                username = info.getUsernam();
                telphnm = info.getTelphnm();
            }
            tmsToVmsService.postDispatchConfirmed(shipmentKey, useract, username, telphnm);
        }

        return true;
    }

    private static TransportInfoUpdateByDispatch.ByTransportCodeDTO getByTransportCodeDTO(DispatchPlanDTO dto) {
        TransportInfoUpdateByDispatch.ByTransportCodeDTO updateByTransportCodeDTO = new TransportInfoUpdateByDispatch.ByTransportCodeDTO();
        updateByTransportCodeDTO.setCompkey(dto.getCompkey());
        updateByTransportCodeDTO.setShipmentKey(dto.getShipmentKey());
        updateByTransportCodeDTO.setTransportCode(dto.getOetmsky());
        updateByTransportCodeDTO.setVehicleId(dto.getVehicleId());
        updateByTransportCodeDTO.setTransportRound(dto.getTransportRound());
        return updateByTransportCodeDTO;
    }

    private void changeShipment(String compkey, String oldShipmentKey, String oetmsky, String newShipmentKey, int maxOrder) {
        List<ShipmentSectionDTO> shipList = shipmentSectionService.getShipmentSectionListByShipment(compkey, oldShipmentKey);
        List<ShipmentSectionDTO> oldShipList = shipList.stream()
                .filter(s -> !s.getOetmsky().equals(oetmsky))
                .sorted(Comparator.comparing(ShipmentSectionDTO::getVstordr))
                .toList();

        int order = 1;
        for (ShipmentSectionDTO s : oldShipList) {
            s.setVstordr(order++);
            shipmentSectionService.updateVisitOrderAndShipmentKey(s.getCompkey(), s.getShpscky(), s.getVstordr(), s.getShipment().getShpmtky());
        }

        List<ShipmentSectionDTO> newShipList = shipList.stream()
                .filter(s -> s.getOetmsky().equals(oetmsky))
                .sorted(Comparator.comparing(ShipmentSectionDTO::getVstordr))
                .toList();
        for (ShipmentSectionDTO s : newShipList) {
            s.setVstordr(maxOrder++);
            shipmentSectionService.updateVisitOrderAndShipmentKey(s.getCompkey(), s.getShpscky(), s.getVstordr(), newShipmentKey);
        }
    }

    @Transactional
    public Boolean cancelManualDispatch(String shipmentKey, String oetmsky, Boolean isCheck) {
        if (Objects.isNull(shipmentKey)) {
            throw new RuntimeException("cancelManualDispatch : Cancel because shipmentKey is null.");
        }
        if (Objects.isNull(oetmsky)) {
            throw new RuntimeException("cancelManualDispatch : Cancel because oetmsky is null.");
        }

        DispatchPlan entity = dispatchPlanRepository.findByShipmentKeyAndOetmskyAndIsManual(shipmentKey, oetmsky, true);
        DispatchPlanDTO dto = dispatchPlanMapper.toDto(entity);
        if (Objects.isNull(dto)) {
            log.info("cancelManualDispatch: shipmentKey {}, oetmsky {} not found.", shipmentKey, oetmsky);
            return false;
        }
        if (dto.getIsConfirmed()) {
            log.info("cancelManualDispatch: shipmentKey {}, oetmsky {} is already confirmed.", shipmentKey, oetmsky);
            return false;
        }

        if (!isCheck)
            dispatchPlanRepository.delete(entity);
        return true;
    }

}
