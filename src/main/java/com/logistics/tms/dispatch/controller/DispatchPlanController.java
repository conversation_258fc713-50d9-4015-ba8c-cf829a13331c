package com.logistics.tms.dispatch.controller;

import com.logistics.tms.common.enumeration.OrderStatusEnum;
import com.logistics.tms.common.enumeration.SimulationOptionEnum;
import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.configuration.security.AuthUser;
import com.logistics.tms.dispatch.constant.DispatchConstant;
import com.logistics.tms.dispatch.dto.DispatchAddDTO;
import com.logistics.tms.dispatch.dto.DispatchPlanDTO;
import com.logistics.tms.dispatch.dto.DispatchResponseDTO;
import com.logistics.tms.dispatch.interfaces.DispatchPlanSwagger;
import com.logistics.tms.dispatch.service.DispatchPlanService;
import com.logistics.tms.driver.dto.DriverInfoDTO;
import com.logistics.tms.driver.service.DriverInfoCustomService;
import com.logistics.tms.external.dto.OetmhdTmsInfoDTO;
import com.logistics.tms.external.service.OetmhdService;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO;
import com.logistics.tms.shipment.dto.ShipmentPlanCustomDTO.ShipmentPlanSimpleInfo;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO.ShipmentSimpleInfoDTO;

import com.logistics.tms.shipment.entity.Shipment;
import com.logistics.tms.shipment.entity.ShipmentPlan;
import com.logistics.tms.shipment.service.ShipmentPlanService;
import com.logistics.tms.shipment.service.ShipmentService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Validated
@RestController
@RequestMapping(DispatchConstant.URL_DISPATCH_PLANS)
@RequiredArgsConstructor
public class DispatchPlanController implements DispatchPlanSwagger {

    private final DispatchPlanService dispatchPlanService;
    private final DriverInfoCustomService driverInfoCustomService;
    private final OetmhdService oetmhdService;
    private final ShipmentPlanService shipmentPlanService;
    private final ShipmentService shipmentService;

    private Boolean isConfirmRun = false;

    @PostMapping(path = "/byShip", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> addDispatchPlan(@RequestBody final List<DispatchAddDTO.ShipmentDTO> addDTOList) {

        final int created = dispatchPlanService.saveByShipment(addDTOList);
        return ResponseEntity.ok(created + " created");
    }

    @GetMapping("/dateRound")
    public ResponseEntity<?> getDateRound(
            @RequestParam(value = "today", required = false) @Nullable final String today) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate localDate = (today != null) ? LocalDate.parse(today, formatter) : LocalDate.now();
            List<DispatchResponseDTO.DispatchDateRoundDTO> response = dispatchPlanService.getDateRound(localDate);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/summary")
    public ResponseEntity<?> getSummary(@RequestParam @NotBlank final String requestDate, @RequestParam final int transportRound) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate localDate = (requestDate != null) ? LocalDate.parse(requestDate, formatter) : LocalDate.now();
            List<DispatchResponseDTO.DispatchSummaryDTO> response = dispatchPlanService.getSummary(localDate, transportRound, false);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/summaryMobileWeb")
    public ResponseEntity<?> getSummaryMobileWeb(@RequestParam @NotBlank final String requestDate, @RequestParam final int transportRound) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate localDate = (requestDate != null) ? LocalDate.parse(requestDate, formatter) : LocalDate.now();
            List<DispatchResponseDTO.DispatchSummaryDTO> response = dispatchPlanService.getSummary(localDate, transportRound, true);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/detail")
    public ResponseEntity<?> getDetail(@RequestParam @NotBlank final String shipmentKey) {
        try {
            DispatchResponseDTO.DispatchDetailDTO response = dispatchPlanService.getDispatchDetail(shipmentKey);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/driver")
    public ResponseEntity<?> getDriver(@AuthUser final AuthDTO authDTO,
                                       @RequestParam @NotBlank final String requestDate,
                                       @RequestParam final int transportRound,
                                       @RequestParam @NotBlank final String shipmentKey) {
        try {
            String partnerKey = authDTO.getPartnerKey();

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate localDate = (requestDate != null) ? LocalDate.parse(requestDate, formatter) : LocalDate.now();
            List<DispatchResponseDTO.DispatchDriverVehicleInfo> response = dispatchPlanService.getDriverVehicleInfoList(partnerKey, localDate, transportRound, shipmentKey);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/driver")
    public ResponseEntity<?> updateDriver(@AuthUser final AuthDTO authDTO,
                                          @RequestParam final Long dispatchPlanId,
                                          @RequestParam final Long userasq) {
        try {
            String partnerKey = authDTO.getPartnerKey();

            Optional<DispatchPlanDTO> dispatchPlanDTO = dispatchPlanService.findById(dispatchPlanId);

            final Optional<DriverInfoDTO.DriverDetailInfo> driverDetailInfo = driverInfoCustomService.findByUserasqWithDriverHolidayInfo(userasq);

            if(dispatchPlanDTO.isPresent() && driverDetailInfo.isPresent()) {
                DispatchPlanDTO updateDispatch = dispatchPlanDTO.get();

                List<OetmhdTmsInfoDTO> oetmhdTmsInfoDTOS = oetmhdService.findOetmskyByShipmentKey(updateDispatch.getShipmentKey());
                if(!oetmhdTmsInfoDTOS.isEmpty()) {
                    for(OetmhdTmsInfoDTO oetmhdTmsInfo : oetmhdTmsInfoDTOS) {
                        if(OrderStatusEnum.CANCEL.equals(oetmhdTmsInfo.getTshitst())) {
                            return ResponseEntity.badRequest().body("updateDriver : 취소된 운송 정보 -> " + oetmhdTmsInfo.getOetmsky());
                        }
                    }
                }

                DriverInfoDTO.DriverDetailInfo driverDetailDTO = driverDetailInfo.get();
                if(!partnerKey.isEmpty() && !partnerKey.equals(driverDetailDTO.getDriverAllInfo().getPtnrkey())) {
                    log.info("partnerKey 불일치!! CarrierPartnerKey->{}, DriverPartnerKey->{} ", partnerKey, driverDetailDTO.getDriverAllInfo().getPtnrkey());
                    return ResponseEntity.badRequest().body("partnerKey 불일치");
                }

                updateDispatch.setDriverId(userasq);
                updateDispatch.setVehicleId(driverDetailDTO.getDriverAllInfo().getVehicleId());

                final DispatchPlanDTO updatedDTO = dispatchPlanService.updateDriver(dispatchPlanId, updateDispatch);
                return ResponseEntity.ok(updatedDTO);
            }
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/routeSummary")
    public ResponseEntity<?> getRouteSummary(@RequestParam @NotBlank final String requestDate,
                                             @RequestParam final int transportRound,
                                             @RequestParam @NotBlank final String companyName) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate localDate = (requestDate != null) ? LocalDate.parse(requestDate, formatter) : LocalDate.now();
            List<DispatchResponseDTO.RouteSummaryDTO> response = dispatchPlanService.getRouteSummary(localDate, transportRound, companyName);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/modifyRouteOrder")
    public ResponseEntity<?> modifyRouteOrder(@RequestParam @NotBlank final String shipmentKey, @RequestParam final List<Integer> visitOrderList) {
        try {
            log.info("modifyRouteOrder: shipmentKey {}, visitOrderList: {}", shipmentKey, visitOrderList);
            return ResponseEntity.ok(dispatchPlanService.modifyRouteOrder(shipmentKey, visitOrderList));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/confirmRouteOrder")
    public ResponseEntity<?> confirmRouteOrder(@RequestParam @NotBlank final String shipmentKey, @RequestParam final List<Integer> visitOrderList) {
        try {
            log.info("confirmRouteOrder: shipmentKey {}, visitOrderList: {}", shipmentKey, visitOrderList);
            return ResponseEntity.ok(dispatchPlanService.confirmRouteOrder(shipmentKey, visitOrderList));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/confirm")
    public ResponseEntity<?> confirmDispatch(@RequestParam @NotBlank final String shipmentKey,
                                             @RequestParam(value = "charge", required = false) @Nullable final Integer charge) {
        try {
            if (!isConfirmRun) {
                isConfirmRun = true;
                log.info("confirmDispatch: {} charge {}", shipmentKey, charge);

                List<OetmhdTmsInfoDTO> oetmhdTmsInfoDTOS = oetmhdService.findOetmskyByShipmentKey(shipmentKey);
                if(!oetmhdTmsInfoDTOS.isEmpty()) {
                    for(OetmhdTmsInfoDTO oetmhdTmsInfo : oetmhdTmsInfoDTOS) {
                        if(OrderStatusEnum.CANCEL.equals(oetmhdTmsInfo.getTshitst())) {
                            isConfirmRun = false;
                            return ResponseEntity.badRequest().body("confirmDispatch : 취소된 운송 정보 -> " + oetmhdTmsInfo.getOetmsky());
                        }
                    }
                }

                int rentalCharge = (charge == null) ? 0 : charge;
                Boolean result = dispatchPlanService.confirmDispatch(shipmentKey, rentalCharge);
                isConfirmRun = false;
                return ResponseEntity.ok(result);
            } else {
                log.info("skip, confirmDispatch: {} is running.", shipmentKey);
                isConfirmRun = false;
                return ResponseEntity.ok(false);
            }
        } catch (RuntimeException e) {
            isConfirmRun = false;
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @Hidden
    @PutMapping("/cancel")
    public ResponseEntity<?> cancelDispatch(@RequestParam final List<String> shipmentKeyList) {
        try {
            log.info("cancelDispatch: {}", shipmentKeyList);
            return ResponseEntity.ok(dispatchPlanService.cancelDispatch(shipmentKeyList, false));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping(path = "/cancel/select", produces = MediaType.APPLICATION_JSON_VALUE)
    public DispatchResponseDTO.DispatchDeletedDTO cancelSelectedDispatchs(@AuthUser final AuthDTO authDTO,
                                                                          @RequestParam @NotNull final String cancelShipmentKey,
                                                                          @RequestParam @NotNull final List<String> cancelOetmskyList,
                                                                          @Nullable SimulationOptionEnum simulop
    ) {

        final String compkey = authDTO.getCompany();
        final String partnerKey = authDTO.getPartnerKey();
        DispatchResponseDTO.DispatchDeletedDTO response = new DispatchResponseDTO.DispatchDeletedDTO();

        ShipmentSimpleInfoDTO shipmentSimpleInfoDTO = shipmentService.findShipmentSimpleInfo(compkey, cancelShipmentKey);
        if(shipmentSimpleInfoDTO == null) {
            log.info("cancelSelectedDispatchs : 해당 Shipment No 에 대한 운송정보가 없음. cancelShipmentKey. {}", cancelShipmentKey);
            response.setMessage("해당 Shipment No 에 대한 운송정보가 존재하지 않습니다. : " + cancelShipmentKey);
            response.setErrorCode(2);
            return response;
        }

        ShipmentPlanSimpleInfo shipmentPlanSimpleInfo = shipmentPlanService.findShipmentPlanSimpleInfo(compkey, shipmentSimpleInfoDTO.getShpplky());
        if(shipmentPlanSimpleInfo == null) {
            log.info("cancelSelectedDispatchs : 해당 운송계획에 대한 정보가 없음. Shpplky(). {}", shipmentSimpleInfoDTO.getShpplky());
            response.setMessage("해당 운송계획에 대한 정보가 존재하지 않습니다. : " + shipmentSimpleInfoDTO.getShpplky());
            response.setErrorCode(2);
            return response;
        }

        final String shpplky = shipmentPlanSimpleInfo.getShpplky();
        log.info("cancelSelectedDispatchs : shpplky -> {}", shpplky);

        List<String> fixedShpmtkyList = shipmentPlanService.getFixedShipmentList(compkey, shpplky);
        log.info("cancelSelectedDispatchs : fixedShpmtkyList. {}", fixedShpmtkyList);

        // Cancel DispatchPlan
        if(fixedShpmtkyList != null && !fixedShpmtkyList.isEmpty()) {
            if(!fixedShpmtkyList.contains(cancelShipmentKey)) {
                log.info("cancelSelectedDispatchs : 확정되지 않은 운송계획 입니다. : " + cancelShipmentKey);
                response.setMessage("확정되지 않은 운송계획 입니다. : " + cancelShipmentKey);
                response.setErrorCode(2);
                return response;
            }

            List<String> dispatchConfirmList = dispatchPlanService.cancelCheckDispatch(fixedShpmtkyList);
            if(dispatchConfirmList != null && !dispatchConfirmList.isEmpty()) {
                if(dispatchConfirmList.contains(cancelShipmentKey)) {
                    response.setMessage("배차완료된 운송계획은 삭제할 수 없습니다. : " + cancelShipmentKey);
                    response.setErrorCode(2);
                    return response;
                }
            }

            boolean isDeleteDispatch = true;
            List<String> failedShipmentKeyList = new ArrayList<>();
            for(String shipmentKey : fixedShpmtkyList) {
                Boolean bRet = dispatchPlanService.cancelSelectedDispatch(shipmentKey);
                if(!bRet) {
                    log.info("cancelSelectedDispatchs : 배차계획 삭제 실패!! -> " + shipmentKey);
                    isDeleteDispatch = false;
                    failedShipmentKeyList.add(shipmentKey);
                } else {
                    log.info("cancelSelectedDispatchs : 배차계획 삭제 완료!! -> " + shipmentKey);
                }
            }
            if(!isDeleteDispatch) {
                response.setMessage("배차계획 삭제 실패 : " + failedShipmentKeyList);
                response.setErrorCode(2);
                return response;
            }
        }

        LocalDate uldrqdt = shipmentPlanSimpleInfo.getShpmtdt();
        Integer roundno = shipmentPlanSimpleInfo.getRoundno();
        List<String> reOetmskyList = new ArrayList<>();

        // Initialize
        List<String> fixedOetmskyList = shipmentPlanService.findFixedOetmskyList(compkey, shpplky);

        ShipmentPlanCustomDTO.ShipmentPlanDeletedInfoDTO deletedInfo = shipmentPlanService.deleteSelectedShipmentPlans(
                authDTO, compkey, uldrqdt, roundno, cancelOetmskyList);

        shipmentPlanService.clearOetmhdTmshpno(deletedInfo.getOetmskyValues());
        shipmentPlanService.deleteShipmentOmsItems(deletedInfo.getShpmtkyList());

        if(fixedOetmskyList != null && !fixedOetmskyList.isEmpty()) {
            for(String oetmsky : fixedOetmskyList) {
                if(!cancelOetmskyList.contains(oetmsky)) {
                    reOetmskyList.add(oetmsky);
                }
            }
        }

        log.info("cancelSelectedDispatchs : TotalOetmskyList -> {}", deletedInfo.getOetmskyValues());
        log.info("cancelSelectedDispatchs : cancelOetmskyList -> {}", cancelOetmskyList);
        log.info("cancelSelectedDispatchs : fixedOetmskyList -> {}", fixedOetmskyList);
        log.info("cancelSelectedDispatchs : reOetmskyList -> {}", reOetmskyList);

        if(!reOetmskyList.isEmpty()) {
            // Re-Simulation
            //ShipmentPlanCustomDTO.ShipmentPlanMakeResponseDTO responseMake = new ShipmentPlanCustomDTO.ShipmentPlanMakeResponseDTO();

            ShipmentPlanCustomDTO.ShipmentPlanMakeDummyDTO retMake = shipmentPlanService.makeSelectedShipmentPlan(authDTO,
                    compkey, authDTO.getId(), partnerKey, uldrqdt, roundno, reOetmskyList, simulop);

            retMake = shipmentPlanService.saveShipmentPlan(authDTO, compkey, retMake);
            retMake = shipmentPlanService.saveShipmentOmsItem(authDTO, compkey, retMake);

            ShipmentPlan retMakeShipPlan = retMake.getShipPlan();
            List<String> shipmentKeyList = new ArrayList<>();
            if(retMakeShipPlan != null) {
                log.info("cancelSelectedDispatchs : retMakeShipPlan.getShpplky() -> {}", retMakeShipPlan.getShpplky());
                List<Shipment> shipmentList = retMakeShipPlan.getShipmentList();
                if(shipmentList != null) {
                    for(Shipment shipment : shipmentList) {
                        shipmentKeyList.add(shipment.getShpmtky());
                    }
                }
            }
            shipmentPlanService.updateOetmhdTmshpno(retMake.getShipPlan(), reOetmskyList);
            shipmentPlanService.updateOetmskyShipmentKey(retMake.getManualPairList());
            //shipmentPlanService.deleteShipmentOmsItems(shipmentKeyList);

            ShipmentPlanCustomDTO.ShipmentPlanMakeDetailDTO detail = new ShipmentPlanCustomDTO.ShipmentPlanMakeDetailDTO();
            detail.setShpplky(retMake.getShpplky());
            detail.setShpmtkyCount(retMake.getShpmtkyCount());

            //responseMake.setDetailShipPlan(detail);
            //responseMake.setErrorMsg(retMake.getErrorMsg());
            log.info("cancelSelectedDispatchs : retMake.getShpplky(): {}", retMakeShipPlan.getShpplky());

            // Fix ShipmentPlan
            log.info("cancelSelectedDispatchs : fixShipmentPlan -> shipmentKetList: {}", shipmentKeyList);
            ShipmentPlan savedEntity = shipmentPlanService.fixShipmentPlanByShipment(compkey, retMake.getShpplky(), shipmentKeyList);
            if(Objects.isNull(savedEntity)) {
                log.info("cancelSelectedDispatchs : fixShipmentPlan -> 해당하는 운송계획이 존재하지 않습니다.");
                response.setErrorCode(2);
                response.setMessage("운송계획 확정 실패.");
                response.setShpplky(shpplky);
                return response;
            }
            List<String> oetmskyValues = shipmentPlanService.getOetmskyByShipmentPlanByShipment(savedEntity, shipmentKeyList);
            shipmentPlanService.updateOetmhdTshitstPlanFix(oetmskyValues);
            shipmentPlanService.updateDispatchShipmentPlanByShipmnet(savedEntity, shipmentKeyList);

            if ( savedEntity == null ) log.info("cancelSelectedDispatchs : Cannot fix ShipmentPlan, shpplky: {}", shpplky);
            else log.info("deleteSelectedShipmentPlans : Fix ShipmentPlan, shpplky: {}", shpplky);
        }

        response.setErrorCode(0);
        response.setUldrqdt(uldrqdt);
        response.setRoundno(roundno);
        response.setShpplky(shpplky);
        log.info("cancelSelectedDispatchs : shpplky: {}", shpplky);
        return response;
    }

    @Hidden
    @PutMapping("/cancelCheck")
    public ResponseEntity<?> cancelCheckDispatch(@RequestParam final List<String> shipmentKeyList) {
        try {
            log.info("cancelCheckDispatch: {}", shipmentKeyList);
            return ResponseEntity.ok(dispatchPlanService.cancelCheckDispatch(shipmentKeyList));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }
}
