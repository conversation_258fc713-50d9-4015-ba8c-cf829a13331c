package com.logistics.tms.dispatch.interfaces;

import com.logistics.tms.common.enumeration.SimulationOptionEnum;
import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.configuration.security.AuthUser;
import com.logistics.tms.dispatch.dto.DispatchAddDTO;
import com.logistics.tms.dispatch.dto.DispatchPlanDTO;
import com.logistics.tms.dispatch.dto.DispatchResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.locationtech.jts.geom.LineString;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Tag(name = "DispatchPlan", description = "배차계획정보 API")
public interface DispatchPlanSwagger {

    @Operation(summary = "운송 계획 확정시 배차 계획 추가")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "size() created"),
            @ApiResponse(responseCode = "404", description = "Not Found",
                    content = @Content(schema = @Schema(hidden = true))),
    })
    ResponseEntity<?> addDispatchPlan(@RequestBody final List<DispatchAddDTO.ShipmentDTO> addDTOList);

    @Operation(summary = "납품일시 탭 조회")
    @Parameters({
            @Parameter(name = "today", description = "기준 일자 (값이 없으면 자동으로 오늘 날짜 지정됨)", example = "20241008")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            array = @ArraySchema(schema = @Schema(implementation = DispatchResponseDTO.DispatchDateRoundDTO.class))))
    })
    ResponseEntity<?> getDateRound(@RequestParam(value = "today", required = false) @Nullable final String today);

    @Operation(summary = "배차 대상 및 배차 정보 조회")
    @Parameters({
            @Parameter(name = "requestDate", description = "납품 일자", example = "20241008"),
            @Parameter(name = "transportRound", description = "운송 회차", example = "1")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            array = @ArraySchema(schema = @Schema(implementation = DispatchResponseDTO.DispatchSummaryDTO.class))))
    })
    ResponseEntity<?> getSummary(@RequestParam @NotBlank final String requestDate, @RequestParam final int transportRound);

    @Operation(summary = "배차 대상 및 배차 정보 조회 (모바일웹)")
    @Parameters({
            @Parameter(name = "requestDate", description = "납품 일자", example = "20241008"),
            @Parameter(name = "transportRound", description = "운송 회차", example = "1")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            array = @ArraySchema(schema = @Schema(implementation = DispatchResponseDTO.DispatchSummaryDTO.class))))
    })
    ResponseEntity<?> getSummaryMobileWeb(@RequestParam @NotBlank final String requestDate, @RequestParam final int transportRound);

    @Operation(summary = "배차 상세 정보 조회")
    @Parameters({
            @Parameter(name = "shipmentKey", description = "Shipment No.", example = "S000000002")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DispatchResponseDTO.DispatchDetailDTO.class)))
    })
    ResponseEntity<?> getDetail(@RequestParam @NotBlank final String shipmentKey);

    @Operation(summary = "배차 대상 기사/차량 정보 조회", description = "입력한 날짜에 운송이 있는 기사는 제외하고 보여준다.")
    @Parameters({
            @Parameter(name = "requestDate", description = "납품 일자", example = "20241008"),
            @Parameter(name = "transportRound", description = "운송 회차", example = "1"),
            @Parameter(name = "shipmentKey", description = "Shipment No.", example = "S000000002")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DispatchResponseDTO.DispatchDriverVehicleInfo.class)))
    })
    ResponseEntity<?> getDriver(@AuthUser final AuthDTO authDTO,
                                @RequestParam @NotBlank final String requestDate,
                                @RequestParam final int transportRound,
                                @RequestParam @NotBlank final String shipmentKey);

    @Operation(summary = "배차 대상 기사/차량 정보 업데이트")
    @Parameters({
            @Parameter(name = "dispatchPlanId", description = "배차계획 ID"),
            @Parameter(name = "userasq", description = "운송기사 ID 일련번호"),
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "OK",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = DispatchPlanDTO.class))),
            @ApiResponse(responseCode = "404", description = "Not Found",
                    content = @Content(schema = @Schema(hidden = true))),
    })
    ResponseEntity<?> updateDriver(@AuthUser final AuthDTO authDTO,
                                   @RequestParam final Long dispatchPlanId,
                                   @RequestParam final Long userasq);


    @Operation(summary = "배차 경로 정보 조회")
    @Parameters({
            @Parameter(name = "requestDate", description = "납품 일자", example = "20241008"),
            @Parameter(name = "transportRound", description = "운송 회차", example = "1"),
            @Parameter(name = "companyName", description = "하차지 상호명", example = "삼성중공업")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            array = @ArraySchema(schema = @Schema(implementation = DispatchResponseDTO.RouteSummaryDTO.class))))
    })
    ResponseEntity<?> getRouteSummary(@RequestParam @NotBlank final String requestDate,
                                      @RequestParam final int transportRound,
                                      @RequestParam @NotBlank final String companyName);

    @Operation(summary = "배차 경로 순서 변경")
    @Parameters({
            @Parameter(name = "shipmentKey", description = "Shipment No.", example = "S000000002"),
            @Parameter(name = "visitOrderList", description = "운송 순서 배열", example = "2,1,3,4")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = LineString.class)))
    })
    ResponseEntity<?> modifyRouteOrder(@RequestParam @NotBlank final String shipmentKey, @RequestParam final List<Integer> visitOrderList);

    @Operation(summary = "배차 경로 순서 적용")
    @Parameters({
            @Parameter(name = "shipmentKey", description = "Shipment No.", example = "S000000002"),
            @Parameter(name = "visitOrderList", description = "운송 순서 배열", example = "2,1,3,4")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = Boolean.class)))
    })
    ResponseEntity<?> confirmRouteOrder(@RequestParam @NotBlank final String shipmentKey, @RequestParam final List<Integer> visitOrderList);

    @Operation(summary = "배차 확정")
    @Parameters({
            @Parameter(name = "shipmentKey", description = "Shipment No.", example = "S000000002"),
            @Parameter(name = "charge", description = "용차 비용", example = "200000")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = Boolean.class)))
    })
    ResponseEntity<?> confirmDispatch(@RequestParam @NotBlank final String shipmentKey,
                                      @RequestParam(value = "charge", required = false) @Nullable final Integer charge);

    @Operation(summary = "배차계획 선택취소")
    @Parameters({
            @Parameter(name = "cancelShipmentKey", description = "취소할 Shipment", required = true, example = "S000007047"),
            @Parameter(name = "cancelOetmskyList", description = "취소할 운송정보", required = true, example = "3000055635, 3000055803"),
            @Parameter(name = "simulop", description = SimulationOptionEnum.COLUMN_COMMENT, required = false, example = "지정"),
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = Boolean.class)))
    })
    DispatchResponseDTO.DispatchDeletedDTO cancelSelectedDispatchs(@AuthUser final AuthDTO authDTO,
                                                                   @RequestParam @NotNull final String cancelShipmentKey,
                                                                   @RequestParam @NotNull final List<String> cancelOetmskyList,
                                                                   @jakarta.annotation.Nullable SimulationOptionEnum simulop);
}
