package com.logistics.tms.tracks.controller;

import com.logistics.tms.common.enumeration.TrackOverSpeedEnum;
import com.logistics.tms.common.util.AuthUtils;
import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.tracks.dto.TrackAddDTO;
import com.logistics.tms.tracks.dto.TrackCustomDTO;
import com.logistics.tms.tracks.dto.TrackCustomDTO.TrackPrevData;
import com.logistics.tms.tracks.dto.TrackDTO;
import com.logistics.tms.tracks.interfaces.TrackSwagger;
import com.logistics.tms.tracks.service.TrackService;

import com.logistics.tms.transport.service.TmsToVmsService;
import io.micrometer.common.lang.Nullable;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.HttpStatus;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


@Slf4j
@RestController
@RequestMapping("/api/tracks")
@RequiredArgsConstructor
public class TrackController implements TrackSwagger {

    private final TrackService trackService;
    private final TmsToVmsService tmsToVmsService;

    private List<TrackPrevData> prevDatas = new ArrayList<>();

    @GetMapping
    public List<TrackDTO> getAllTracks() {
        return trackService.findAll();
    }

    @GetMapping("/{id}")
    public ResponseEntity<TrackDTO> getTrackById(@PathVariable Long id) {
        return trackService.findById(id)
                .map(dto -> ResponseEntity.ok().body(dto))
                .orElse(ResponseEntity.notFound().build());
    }

    // @PostMapping
    // public TrackDTO createTrack(@RequestBody TrackDTO trackDTO) {
    //     return trackService.save(trackDTO);
    // }

    // @DeleteMapping("/{id}")
    // public ResponseEntity<Void> deleteTrack(@PathVariable Long id) {
    //     trackService.deleteById(id);
    //     return ResponseEntity.noContent().build();
    // }

    @Hidden
    @Override
    @GetMapping(path = "/get")
    public ResponseEntity<?> getTrackInfo(@RequestParam @NotBlank final String compkey,
                                        @RequestParam @NotNull final Long trackid) {
        try {
            final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
            final String _CompKey = Objects.requireNonNull(authDTO).getCompany();
            List<?> result = trackService.getTrackInfo(_CompKey, trackid);
            return ResponseEntity.ok().body(result);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.toString());
        }
    }

    @Operation(summary = "Track 등록")
    @PostMapping(path = "/add")
    public ResponseEntity<?> addTrackInfo(@RequestBody TrackAddDTO trackAddDTO) {
        try {
            final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
            final String _CompKey = Objects.requireNonNull(authDTO).getCompany();
            final String _UserAct = Objects.requireNonNull(authDTO).getId();

            if ( trackAddDTO.getShpmtky() == null || trackAddDTO.getShpmtky().isBlank() ) {
                log.error("Add Track, Shipment is NULL : {}/{}", trackAddDTO.getOverSpeed(), trackAddDTO.getOverNinety());
                return ResponseEntity.status(HttpStatus.NOT_ACCEPTABLE)
                            .body("Not Acceptable. Shipment is NULL.");
            }

            Boolean isDuplecate = trackService.AddRecentData(this.prevDatas, trackAddDTO);
            if ( isDuplecate ) {
                log.info("isDuplecate Prev Track = {}", isDuplecate);
                final int TIME_WINDOW = 1; // 초 단위
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS)
                            .header("Retry-After", TIME_WINDOW + "")
                            .body("Too many requests. Please try again after " + TIME_WINDOW + " seconds.");
                // return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).body("Too Many Requests");
            }

            TrackDTO _madeTrackDTO = trackService.makeTrack(_CompKey, _UserAct, trackAddDTO);
            if ( _madeTrackDTO == null ) {
                return ResponseEntity.notFound().build();
            }
            TrackDTO trackDTO = trackService.saveDB(_madeTrackDTO);
            trackService.updateCallRoute(trackDTO, trackAddDTO);

            if (trackDTO.getOverNinety()) {
                tmsToVmsService.postSpeed(_CompKey, "SP", trackDTO.getTrackId(),
                        BigDecimal.valueOf(trackDTO.getMatchedLocation().getX()),
                        BigDecimal.valueOf(trackDTO.getMatchedLocation().getY()),
                        _UserAct);
            }
            if (trackDTO.getOverSpeed().equals(TrackOverSpeedEnum.TO) || trackDTO.getOverSpeed().equals(TrackOverSpeedEnum.TS)) {
                tmsToVmsService.postSudden(_CompKey, "SA", trackDTO.getTrackId(),
                        BigDecimal.valueOf(trackDTO.getMatchedLocation().getX()),
                        BigDecimal.valueOf(trackDTO.getMatchedLocation().getY()),
                        _UserAct);
            }
            return ResponseEntity.ok(trackDTO);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.toString());
        }
    }

    @Override
    @Operation(summary = "Track 조회 by 운송요청일,운송회차")
    @GetMapping(path = "/getTrackByDateAndTransportRound")
    public ResponseEntity<?> getTrackByDateAndTransportRound(
                                @RequestParam @NotNull final Long vehicleId,
                                @RequestParam @NotBlank final String requestDate,
                                @RequestParam @NotNull final Integer transportRound) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate today = (requestDate != null) ? LocalDate.parse(requestDate, formatter) : null;
        TrackCustomDTO.TrackListWithTransportRouteAndDriver result = trackService.findByVehicleRequestDateAndRound(vehicleId, today, transportRound);
        return ResponseEntity.ok().body(result);
    }

    @Override
    @Operation(summary = "Track 조회 by 운송ID")
    @GetMapping(path = "/getByTransportId")
    public ResponseEntity<?> getByTransportId(@RequestParam @NotNull final Long transportId) {
        TrackCustomDTO.TrackListWithTransportRouteAndDriver result = trackService.getByTransportId(transportId);
        return ResponseEntity.ok().body(result);
    }

    @Override
    @Operation(summary = "Track 조회 by Shipment No.(shpmtky)")
    @GetMapping(path = "/getByShpmtky")
    public ResponseEntity<?> getByShpmtky(@RequestParam @NotBlank final String shpmtky) {
        TrackCustomDTO.TrackListWithTransportRouteAndDriver result = trackService.getByShpmtky(shpmtky);
        return ResponseEntity.ok().body(result);
    }

    @Hidden
    @Operation(summary = "Track 조회 by ShipmentNo.s")
    @GetMapping(path = "/getByShpmtkys")
    public ResponseEntity<List<TrackDTO>> getLatestByShpmtkys(@RequestParam @NotNull final List<String> shpmtkys) {
        List<TrackDTO> result = trackService.getLatestByShpmtkys(shpmtkys);
        return ResponseEntity.ok().body(result);
    }

    @Hidden
    @Override
    @Operation(summary = "운행별 마지막 Track 조회")
    @GetMapping(path = "/getLatestByTransportId")
    public ResponseEntity<?> getLatestByTransportIds(
                        @RequestParam @NotNull final List<Long> transportIds,
                        @Nullable final Long intervalSeconds ) {
        List<?> result = trackService.getLatestByTransportIds(transportIds, intervalSeconds);
        return ResponseEntity.ok().body(result);
    }

    @Hidden
    @Operation(summary = "Track 조회 by 운송ID")
    @GetMapping(path = "/findByTransportIdWithSusrma")
    public ResponseEntity<?> findByTransportIdWithSusrma(@RequestParam @NotNull final Long transportId) {
        List<?> result = trackService.findByTransportIdWithSusrma(transportId);
        return ResponseEntity.ok().body(result);
    }

    @Hidden
    @Override
    @Operation(summary = "Track 조회 by User")
    @GetMapping(path = "/getByUser")
    public ResponseEntity<?> findByUseract(@RequestParam @NotBlank final String useract) {
        Optional<?> result = trackService.findUserinfoByUseract(useract);
        return ResponseEntity.ok().body(result);

        // return trackService.findByUseract(useract)
        //         .map(dto -> ResponseEntity.ok().body(dto))
        //         .orElse(ResponseEntity.notFound().build());
    }

}
