package com.logistics.tms.realtracking.service;

import com.logistics.tms.common.enumeration.VisitTypeEnum;
import com.logistics.tms.common.util.AuthUtils;
import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.dispatch.dto.DispatchResponseDTO;
import com.logistics.tms.dispatch.service.DispatchPlanService;
import com.logistics.tms.driver.dto.DriverVehicleInfoDTO;
import com.logistics.tms.driver.service.DriverVehicleInfoService;
import com.logistics.tms.external.service.McodemService;
import com.logistics.tms.realtracking.dto.RealTrackingDTO;
import com.logistics.tms.realtracking.dto.TransportHistoryWebDTO;
import com.logistics.tms.realtracking.repository.QRealTrackingRepository;
import com.logistics.tms.realtracking.repository.QTransportHistoryWebRepository;
import com.logistics.tms.realtracking.repository.RealTrackingRepository;
import com.logistics.tms.shipment.constant.ShipmentUtils;
import com.logistics.tms.shipment.dto.ShipmentDTO;
import com.logistics.tms.shipment.dto.ShipmentSectionDTO;
import com.logistics.tms.shipment.entity.Shipment;
import com.logistics.tms.shipment.repository.ShipmentRepository;
import com.logistics.tms.shipment.service.ShipmentSectionService;
import com.logistics.tms.shipment.service.ShipmentService;
import com.logistics.tms.tracks.dto.TrackDTO;
import com.logistics.tms.tracks.service.TrackService;
import com.logistics.tms.transport.dto.TransportHistoryDTO;
import com.logistics.tms.transport.dto.TransportHistoryResponseDTO;
import com.logistics.tms.transport.dto.TransportInfoDTO;
import com.logistics.tms.transport.entity.TransportInfo;
import com.logistics.tms.transport.repository.TransportInfoRepository;
import com.logistics.tms.transport.service.TransportEventService;
import com.logistics.tms.transport.service.TransportHistoryService;
import com.logistics.tms.transport.service.TransportInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.logistics.tms.transport.entity.TransportInfo.TransportStatus.운송대기;

@Slf4j
@RequiredArgsConstructor
@Transactional(readOnly = true)
@Service
public class RealTrackingService {
    private final RealTrackingRepository realTrackingRepository;
    private final TransportInfoService transportInfoService;
    private final TransportInfoRepository transportInfoRepository;
    private final DriverVehicleInfoService driverVehicleInfoService;
    private final TransportHistoryService transportHistoryService;
    private final TransportEventService transportEventService;
    private final ShipmentSectionService shipmentSectionService;
    private final QRealTrackingRepository qRealTrackingRepository;
    private final QTransportHistoryWebRepository qTransportHistoryWebRepository;
    private final ShipmentService shipmentService;
    private final ShipmentRepository shipmentRepository;
    private final TrackService trackService;
    private final McodemService mcodemService;
    private final DispatchPlanService dispatchPlanService;

    public List<RealTrackingDTO.TrackingSummaryDTO> getTrackingSummary(LocalDate requestDate) {
        List<RealTrackingDTO.TrackingSummaryDTO> summaryDTOS = new ArrayList<>();

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        String partnerKey = Objects.requireNonNull(authDTO).getPartnerKey();
        Long limitDate = mcodemService.getLimitDate();
        LocalDate startDate = requestDate.minusDays(limitDate);
        List<LocalDate> dates = qRealTrackingRepository.findDistinctRequestDateByRequestDateGreaterThanEqual(startDate, partnerKey);
        if (!dates.isEmpty()) {
            for (LocalDate date : dates) {
                List<Integer> roundList = qRealTrackingRepository.findDistinctByTransportRoundAndRequestDate(date, partnerKey);
                for (int round : roundList) {
                    RealTrackingDTO.TrackingSummaryDTO dto = new RealTrackingDTO.TrackingSummaryDTO();

                    int singleVehicle = 0;
                    int mixedVehicle = 0;
                    List<Long> vehicleIdList = realTrackingRepository.getVehicleListByRequestDateAndTransportRound(date, round, partnerKey);

                    for (Long vehicleId : vehicleIdList) {
                        int transportCount = transportInfoRepository.countDistinctByTransportCodeAndVehicleIdAndRequestDateEqualsAndTransportRoundEquals(vehicleId, date, round);
                        if (transportCount == 1)
                            singleVehicle++;
                        else
                            mixedVehicle++;
                    }
                    dto.setRequestDate(date);
                    dto.setTransportRound(round);
                    dto.setTotalVehicle(vehicleIdList.size());
                    dto.setSingleVehicle(singleVehicle);
                    dto.setMixedVehicle(mixedVehicle);
                    summaryDTOS.add(dto);
                }
            }
        }
        return summaryDTOS;
    }

    public RealTrackingDTO.StatusSummaryDTO getStatusSummary(LocalDate requestDate, int transportRound) {
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        String partnerKey = Objects.requireNonNull(authDTO).getPartnerKey();

        List<Long> vehicleIdList = realTrackingRepository.getVehicleListByRequestDateEquals(requestDate, transportRound, partnerKey);

        List<RealTrackingDTO.TransportInfoDTO> transportInfoList = new ArrayList<>();
        int ready = 0;
        int inTransit = 0;
        int completed = 0;
        int impossible = 0;
        for (Long vehicleId :vehicleIdList) {
            RealTrackingDTO.TransportInfoDTO infoDTO = new RealTrackingDTO.TransportInfoDTO();

            List<Long> transportId = realTrackingRepository.getTransportIdByVehicleIdAndRequestDate(vehicleId, requestDate, transportRound, partnerKey);
            TransportInfoDTO transportInfo = transportInfoService.findByTransportId(transportId.get(0));
            Optional<DriverVehicleInfoDTO.DriverVehicleInfoCustom> vehicleInfo = driverVehicleInfoService.findVehliceInfoByVehicleId(vehicleId);

            List<TransportInfo.TransportStatus> statusList = realTrackingRepository.getTransportStatusByShipmentKey(transportInfo.getShipmentKey());
            List<TransportInfo.TransportStatus> statusValues = Arrays.asList(TransportInfo.TransportStatus.values());
            TransportInfo.TransportStatus transportStatus = 운송대기;
            for (TransportInfo.TransportStatus status : statusList) {
                if(statusValues.indexOf(status) > statusValues.indexOf(transportStatus))
                    transportStatus = status;
            }
            switch (transportStatus) {
                case 운송대기: ready++; break;
                case 운송완료: completed++; break;
                case 운송완료대기, 상차불가, 하차불가, 회수불가, 반품불가, 입문불가: impossible++; break;
                default: inTransit++; break;
            }

            int transportCount = transportInfoRepository.countDistinctByTransportCodeAndVehicleIdAndRequestDateEqualsAndTransportRoundEquals(vehicleId, requestDate, transportRound);
            TransportHistoryDTO transportHistory = transportHistoryService.getLastHistory(transportInfo.getShipmentKey());
            if (Objects.isNull(transportHistory)) {
                infoDTO.setShipmentKey(transportInfo.getShipmentKey());
                infoDTO.setCompanyName(transportInfo.getCompanyName());
                infoDTO.setCompanyAddress(transportInfo.getCompanyAddress());
                infoDTO.setRequestTime(transportInfo.getRequestTime());
                switch (transportStatus) {
                    case 상차완료, 하차완료, 회수완료, 운송완료:
                        infoDTO.setCompletedTime(transportInfo.getLmotime());
                        break;
                }
            } else {
                transportStatus = transportHistory.getTransportStatus();

                infoDTO.setShipmentKey(transportHistory.getShipmentKey());
                infoDTO.setCompanyName(transportHistory.getCompanyName());
                infoDTO.setCompanyAddress(transportHistory.getCompanyAddress());
                infoDTO.setRequestTime(transportHistory.getRequestTime());
                switch (transportStatus) {
                    case 상차완료, 하차완료, 회수완료, 운송완료:
                        infoDTO.setCompletedTime(transportHistory.getCretime());
                        break;
                }
            }
            infoDTO.setTransportCount(transportCount);
            infoDTO.setTransportStatus(transportStatus);
            infoDTO.setTransportRound(transportInfo.getTransportRound());
            infoDTO.setRequestDate(transportInfo.getRequestDate());

            vehicleInfo.ifPresent(s -> {
                RealTrackingDTO.TrackingDriverVehicleInfo driverVehicleInfo = new RealTrackingDTO.TrackingDriverVehicleInfo();
                driverVehicleInfo.setVehicleId(s.getVehicleId());
                driverVehicleInfo.setUsername(s.getUsernam());
                driverVehicleInfo.setVehicleType(s.getVehicleType());
                driverVehicleInfo.setVehicleBusinessType(s.getVehicleBusinessType());
                driverVehicleInfo.setVehicleWeight(s.getVehicleWeight());
                driverVehicleInfo.setRegistrationNumber(s.getRegistrationNumber());
                infoDTO.setVehicleInfo(driverVehicleInfo);
            });

            List<RealTrackingDTO.TrackingTimeLineDTO> timeLineList = getTimeLineList(transportInfo.getShipmentKey());
            infoDTO.setTimeLineList(timeLineList);
            transportInfoList.add(infoDTO);
        }
        RealTrackingDTO.StatusSummaryDTO dto = new RealTrackingDTO.StatusSummaryDTO();
        dto.setReadyVehicle(ready);
        dto.setInTransitVehicle(inTransit);
        dto.setCompletedVehicle(completed);
        dto.setImpossibleVehicle(impossible);
        dto.setTransportInfoList(transportInfoList);
        return dto;
    }

    public List<TransportInfoDTO> combineProcess(List<TransportInfoDTO> infoList) {
        List<TransportInfoDTO> resultList = new ArrayList<>();

        if (infoList == null || infoList.isEmpty()) {
            return resultList;
        }
        TransportInfoDTO prevInfo = null;

        for (TransportInfoDTO currentInfo : infoList) {
            if (prevInfo == null ||
                    !prevInfo.getCompanyName().equals(currentInfo.getCompanyName()) ||
                    prevInfo.getTransportOrder() + 1 != currentInfo.getTransportOrder()) {
                resultList.add(currentInfo);
            }
            prevInfo = currentInfo;
        }

        return resultList;
    }

    public List<RealTrackingDTO.TrackingTimeLineDTO> getTimeLineList(final String shipmentKey) {
        List<TransportInfoDTO> infoList = transportInfoService.getTransportByShipmentKey(shipmentKey);

        List<TransportInfoDTO> filteredList = combineProcess(infoList);

        List<RealTrackingDTO.TrackingTimeLineDTO> timeLineList = new ArrayList<>();

        LocalDateTime estimatedDate = null;
        for (TransportInfoDTO item : filteredList) {
            RealTrackingDTO.TrackingTimeLineDTO timeLineDTO = new RealTrackingDTO.TrackingTimeLineDTO();
            timeLineDTO.setTransportId(item.getTransportId());
            timeLineDTO.setTransportCode(item.getTransportCode());
            timeLineDTO.setTransportType(item.getTransportType());
            timeLineDTO.setTransportStatus(item.getTransportStatus());
            timeLineDTO.setTransportOrder(item.getTransportOrder());
            timeLineDTO.setRequestDate(item.getRequestDate());
            timeLineDTO.setRequestTime(item.getRequestTime());
            timeLineDTO.setCompanyName(item.getCompanyName());
            timeLineDTO.setDropOffArea(item.getDropOffArea());
            timeLineDTO.setCompanyAddress(item.getCompanyAddress());
            timeLineDTO.setCompanyLocation(item.getCompanyLocation());
            timeLineDTO.setImpossible(item.getImpossible());
            timeLineDTO.setRequiredTime(item.getRequiredTime());
            timeLineDTO.setChangeReason(item.getChangeReason());

            if (Objects.nonNull(item.getDropOffArea())) {
                List<Long> transportIdList = infoList.stream()
                        .filter(info -> Objects.nonNull(info.getDropOffArea()))
                        .map(TransportInfoDTO::getTransportId)
                        .toList();
                List<RealTrackingDTO.TrackingEventDTO> allEvents = new ArrayList<>();
                for (Long transportId : transportIdList) {
                    List<RealTrackingDTO.TrackingEventDTO> eventList = transportEventService.findByTransportIdList(transportId);
                    allEvents.addAll(eventList);
                }
                timeLineDTO.setEventList(allEvents);
            } else {
                List<RealTrackingDTO.TrackingEventDTO> eventList = transportEventService.findByTransportIdList(item.getTransportId());
                if (!eventList.isEmpty()) {
                    timeLineDTO.setEventList(eventList);
                }
            }

            switch (item.getTransportStatus()) {
                case 상차완료, 하차완료, 회수완료, 운송완료:
                    timeLineDTO.setCompletedTime(item.getLmotime());
                    timeLineDTO.setCompletedDate(item.getLmodate());
                    estimatedDate = LocalDateTime.of(item.getLmodate(), item.getLmotime());
                    break;
                case 운송대기:
                    if (estimatedDate == null) {
                        estimatedDate = LocalDateTime.of(item.getRequestDate(), item.getRequestTime());
                    } else {
                        estimatedDate = estimatedDate.plusMinutes(item.getRequiredTime());
                    }
                    break;
                default:
                    if (estimatedDate == null) {
                        estimatedDate = LocalDateTime.of(item.getLmodate(), item.getLmotime());
                    } else {
                        estimatedDate = estimatedDate.plusMinutes(item.getRequiredTime());
                    }
                    break;
            }
            timeLineDTO.setEstimatedDate(estimatedDate.toLocalDate());
            timeLineDTO.setEstimatedTime(estimatedDate.toLocalTime());

            timeLineList.add(timeLineDTO);
        }
        return timeLineList.stream()
                .sorted(Comparator.comparing(RealTrackingDTO.TrackingTimeLineDTO::getTransportOrder))
                .toList();
    }

    public List<RealTrackingDTO.DeliveryPopupDTO> getDeliveryPopup(final String shipmentKey) {
        List<RealTrackingDTO.DeliveryPopupDTO> deliveryPopupDTO = new ArrayList<>();

        List<TransportInfoDTO> infoList = transportInfoService.getTransportByShipmentKey(shipmentKey);
        for (TransportInfoDTO pickupInfo : infoList) {
            RealTrackingDTO.DeliveryPopupDTO dto = new RealTrackingDTO.DeliveryPopupDTO();

            if (pickupInfo.getTransportType() == TransportInfo.TransportType.상차 || pickupInfo.getTransportType() == TransportInfo.TransportType.회수) {
                TransportInfoDTO dropOffInfo = infoList.stream()
                        .filter(info -> Objects.equals(info.getTransportCode(), pickupInfo.getTransportCode()))
                        .filter(info -> info.getTransportType() != pickupInfo.getTransportType())
                        .toList().get(0);

                dto.setTransportCode(pickupInfo.getTransportCode());
                dto.setRequestDate(pickupInfo.getRequestDate());
                dto.setQuantity(pickupInfo.getQuantity());
                dto.setPickupCompanyName(pickupInfo.getCompanyName());
                dto.setPickupRequestTime(pickupInfo.getRequestTime());
                dto.setDropOffCompanyName(dropOffInfo.getCompanyName());
                dto.setDropOffRequestTime(dropOffInfo.getRequestTime());
                int transportCount = infoList.stream()
                        .filter(info -> (info.getTransportType() == TransportInfo.TransportType.하차) ||
                                (info.getTransportType() == TransportInfo.TransportType.반품))
                        .filter(info -> Objects.equals(info.getCompanyName(), dropOffInfo.getCompanyName()))
                        .toList().size();
                dto.setTransportCount(transportCount);

                String concatenatedString = Stream.of(pickupInfo.getRestriction(), dropOffInfo.getRestriction())
                        .map(ShipmentUtils::convertUnavat1Code)
                        .filter(s -> !s.isEmpty())
                        .collect(Collectors.joining(", "));
                if (!concatenatedString.isEmpty())
                    dto.setRestriction(concatenatedString);

                deliveryPopupDTO.add(dto);
            }
        }

        return deliveryPopupDTO;
    }

    public RealTrackingDTO.TransportDetailDTO getTransportDetail(final String shipmentKey) {
        RealTrackingDTO.TransportDetailDTO transportDetailDTO = new RealTrackingDTO.TransportDetailDTO();

        List<TransportInfo> transportList = qTransportHistoryWebRepository.getShipment(shipmentKey);
        Set<String> uniqueTransportCodes = transportList.stream()
                .map(TransportInfo::getTransportCode)
                .collect(Collectors.toSet());
        int transportCount = uniqueTransportCodes.size();

        TransportInfo lastTransport = transportList.stream()
                .max(Comparator.comparingInt(TransportInfo::getTransportOrder))
                .orElse(null);
        Optional<DriverVehicleInfoDTO.DriverVehicleInfoCustom> vehicleInfo =
                driverVehicleInfoService.findVehliceInfoByVehicleId(Objects.requireNonNull(lastTransport).getVehicleId());

        List<RealTrackingDTO.TrackingTimeLineDTO> timeLineList = this.getTimeLineList(shipmentKey);
        List<TransportHistoryResponseDTO.TransportHistoryByShipmentKeyDTO> historyList = transportHistoryService.findByShipmentKey(shipmentKey);
        List<TransportHistoryResponseDTO.TransportHistoryByShipmentKeyDTO> filteredList = new ArrayList<>(historyList.stream()
                .filter(h -> !h.getTransportStatus().equals(TransportInfo.TransportStatus.조선소로이동중)
                        && !h.getTransportStatus().equals(TransportInfo.TransportStatus.조선소입문)
                        && !h.getTransportStatus().equals(TransportInfo.TransportStatus.입문불가))
                .toList());
        if (!filteredList.isEmpty()) {
            TransportHistoryResponseDTO.TransportHistoryByShipmentKeyDTO h1 = historyList.stream()
                    .filter(history -> history.getTransportStatus().equals(TransportInfo.TransportStatus.조선소로이동중))
                    .findFirst().orElse(null);
            TransportHistoryResponseDTO.TransportHistoryByShipmentKeyDTO h2 = historyList.stream()
                    .filter(history -> history.getTransportStatus().equals(TransportInfo.TransportStatus.조선소입문))
                    .findFirst().orElse(null);
            TransportHistoryResponseDTO.TransportHistoryByShipmentKeyDTO h3 = historyList.stream()
                    .filter(history -> history.getTransportStatus().equals(TransportInfo.TransportStatus.입문불가))
                    .findFirst().orElse(null);
            if (h1 != null) {
                filteredList.add(h1);
            }
            if (h2 != null) {
                filteredList.add(h2);
            }
            if (h3 != null) {
                filteredList.add(h3);
            }
        }

        Optional<ShipmentDTO> shipmentDTO = shipmentService.getShipment2(lastTransport.getCompkey(), lastTransport.getShipmentKey());
        shipmentDTO.ifPresent(s -> {
            transportDetailDTO.setLoadrat(s.getLoadrat());
            transportDetailDTO.setEstimatedFee(s.getEstimatedfee());
        });

        List<DispatchResponseDTO.LoadingInfo> loadingInfoList = new ArrayList<>();
        List<ShipmentSectionDTO> shipList = shipmentSectionService.getShipmentSectionListByShipment(lastTransport.getCompkey(), lastTransport.getShipmentKey());
        for (ShipmentSectionDTO pickup : shipList) {
            if (pickup.getVsttype() == VisitTypeEnum.PICKUP) {
                DispatchResponseDTO.LoadingInfo loadingInfo = dispatchPlanService.getLoadingInfo(pickup);

                loadingInfoList.add(loadingInfo);
            }
        }
        transportDetailDTO.setLoadingInfoList(loadingInfoList);

        List<RealTrackingDTO.TrackingDeliveryDTO> deliveryList = getDelivery(shipmentKey);

        transportDetailDTO.setShipmentKey(shipmentKey);
        transportDetailDTO.setTransportCount(transportCount);
        transportDetailDTO.setTimeLineList(timeLineList);
        transportDetailDTO.setHistoryList(filteredList);
        transportDetailDTO.setDeliveryList(deliveryList);
        vehicleInfo.ifPresent(s -> {
            transportDetailDTO.setUsername(s.getUsernam());
            transportDetailDTO.setVehicleType(s.getVehicleType());
            transportDetailDTO.setVehicleWeight(s.getVehicleWeight());
            transportDetailDTO.setVehicleBusinessType(s.getVehicleBusinessType());
            transportDetailDTO.setRegistrationNumber(s.getRegistrationNumber());
        });
        Shipment shipment = shipmentRepository.findByCompkeyAndShpmtky(lastTransport.getCompkey(), shipmentKey);
        transportDetailDTO.setRentalCharge(shipment.getVehcost());

        return transportDetailDTO;
    }

    public List<RealTrackingDTO.TrackingDeliveryDTO> getDelivery(final String shipmentKey) {
        List<RealTrackingDTO.TrackingDeliveryDTO> deliveryDTO = new ArrayList<>();

        List<TransportInfoDTO> infoList = transportInfoService.getTransportByShipmentKey(shipmentKey);
        String compkey = infoList.get(0).getCompkey();
        List<ShipmentSectionDTO> shipList = shipmentSectionService.getShipmentSectionListByShipment(compkey, shipmentKey);
        for (TransportInfoDTO pickupInfo : infoList) {
            RealTrackingDTO.TrackingDeliveryDTO dto = new RealTrackingDTO.TrackingDeliveryDTO();

            if (pickupInfo.getTransportType() == TransportInfo.TransportType.상차 || pickupInfo.getTransportType() == TransportInfo.TransportType.회수) {
                TransportInfoDTO dropOffInfo = infoList.stream()
                        .filter(info -> Objects.equals(info.getTransportCode(), pickupInfo.getTransportCode()))
                        .filter(info -> info.getTransportType() != pickupInfo.getTransportType())
                        .toList().get(0);

                dto.setTransportCode(pickupInfo.getTransportCode());
                dto.setPickupCompanyName(pickupInfo.getCompanyName());
                dto.setPickupCompanyAddress(pickupInfo.getCompanyAddress());
                dto.setPickupRequestDate(pickupInfo.getRequestDate());
                dto.setPickupRequestTime(pickupInfo.getRequestTime());
                dto.setDropOffCompanyName(dropOffInfo.getCompanyName());
                dto.setDropOffRequestTime(dropOffInfo.getRequestTime());
                dto.setDropOffRequestDate(dropOffInfo.getRequestDate());
                dto.setRequiredTime(dropOffInfo.getRequiredTime());

                String concatenatedString = Stream.of(pickupInfo.getRestriction(), dropOffInfo.getRestriction())
                        .map(ShipmentUtils::convertUnavat1Code)
                        .filter(s -> !s.isEmpty())
                        .collect(Collectors.joining(", "));
                if (!concatenatedString.isEmpty())
                    dto.setRestriction(concatenatedString);

                if (pickupInfo.getChangeReason() != null)
                    dto.setChangeReason(pickupInfo.getChangeReason());
                else if (dropOffInfo.getChangeReason() != null)
                    dto.setChangeReason(dropOffInfo.getChangeReason());

                if (dropOffInfo.getTransportStatus() == 운송대기) {
                    dto.setTransportType(pickupInfo.getTransportType());
                    dto.setTransportStatus(pickupInfo.getTransportStatus());
                } else {
                    dto.setTransportType(dropOffInfo.getTransportType());
                    dto.setTransportStatus(dropOffInfo.getTransportStatus());
                }

                List<RealTrackingDTO.TrackingEventDTO> pickupEvent = transportEventService.findByTransportIdList(pickupInfo.getTransportId());
                List<RealTrackingDTO.TrackingEventDTO> dropOffEvent = transportEventService.findByTransportIdList(dropOffInfo.getTransportId());
                List<RealTrackingDTO.TrackingEventDTO> eventList = Stream.concat(pickupEvent.stream(), dropOffEvent.stream())
                        .toList();
                dto.setEventList(eventList);

                TransportHistoryWebDTO.OtherChargeDTO otherChargeDTO = qTransportHistoryWebRepository.getOtherChargeByTransportCode(pickupInfo.getTransportCode());
                if (Objects.nonNull(otherChargeDTO) && (otherChargeDTO.getOthechg() != 0)) {
                    dto.setOtherCharge(otherChargeDTO.getOthechg());
                    dto.setOtherChargeComment(otherChargeDTO.getOthersn());
                }

                dto.setQuantity(pickupInfo.getQuantity());

                List<ShipmentSectionDTO> ships = shipList.stream()
                        .filter(s -> Objects.equals(s.getOetmsky(), pickupInfo.getTransportCode()))
                        .filter(s -> s.getVsttype() == VisitTypeEnum.PICKUP)
                        .toList();
                if (!ships.isEmpty()) {
                    ShipmentSectionDTO ship = ships.get(0);

                    dto.setPalletWidth(ship.getPakwidh());
                    dto.setPalletDepth(ship.getPakdept());
                    dto.setPalletHeight(ship.getPakheig());
                    dto.setWeight(ship.getPkrweig());

                    final double MM_PER_METER = 1000000.0;
                    double areaInSquareMeters = ship.getPakwidh() * ship.getPakdept() / MM_PER_METER;
                    dto.setPalletArea((int) areaInSquareMeters);

                    dto.setPalletPosition(ship.getLoadyplt() == null ? 0 : ship.getLoadyplt());
                    dto.setPackageCategory(ship.getOpkcate());
                    dto.setPackageType(ship.getOpktype());
                    dto.setDeliveryItem(ship.getSkudesc());
                    dto.setDeliveryQuantity(ship.getRgitqty());
                }

                deliveryDTO.add(dto);
            }
        }

        return deliveryDTO;
    }

    @Transactional
    public void modifyOtherCharge(final String transportCode, final int otherCharge, final String otherChargeComment) {
        qRealTrackingRepository.updateOtherCharge(transportCode, otherCharge, otherChargeComment);
    }

    public RealTrackingDTO.VMSTrackingInfo getVMSTracking(LocalDate requestDate, String dropOffCompany, String partnerKey) {
        RealTrackingDTO.VMSTrackingInfo VMSTracking = new RealTrackingDTO.VMSTrackingInfo();
        List<RealTrackingDTO.VMSVehicleInfo> VMSVehicleList = new ArrayList<>();

        int pickupVehicle = 0;
        int dropOffVehicle = 0;
        int shipEntryVehicle = 0;
        int completedVehicle = 0;

        List<Long> vehicleIdList = realTrackingRepository.getVehicleListByRequestDateAndCompanyName(requestDate, dropOffCompany, partnerKey);
        for (Long vehicleId :vehicleIdList) {
            RealTrackingDTO.VMSVehicleInfo trackingInfo = new RealTrackingDTO.VMSVehicleInfo();
            Optional<DriverVehicleInfoDTO.DriverVehicleInfoCustom> vehicleInfo = driverVehicleInfoService.findVehliceInfoByVehicleId(vehicleId);
            vehicleInfo.ifPresent(s -> {
                trackingInfo.setVehicleId(s.getVehicleId());
                trackingInfo.setUseract(s.getUseract());
                trackingInfo.setUsername(s.getUsernam());
                trackingInfo.setVehicleBusinessType(s.getVehicleBusinessType());
                trackingInfo.setVehicleType(s.getVehicleType());
                trackingInfo.setVehicleWeight(s.getVehicleWeight());
                trackingInfo.setRegistrationNumber(s.getRegistrationNumber());
                trackingInfo.setTelphnm(s.getTelphnm());
            });
            TransportHistoryDTO transportHistory = transportHistoryService.getLastHistoryByVehicleId(vehicleId);
            if (Objects.nonNull(transportHistory)) {
                trackingInfo.setTransportStatus(transportHistory.getTransportStatus());
                trackingInfo.setShpmtky(transportHistory.getShipmentKey());
                Optional<ShipmentDTO> ship = shipmentService.getShipment2(transportHistory.getCompkey(), transportHistory.getShipmentKey());
                ship.ifPresent(s -> trackingInfo.setLoadrat(s.getLoadrat()));
                List<TrackDTO> trackDTOList = trackService.getLatestByShpmtkys(Collections.singletonList(transportHistory.getShipmentKey()));
                if (!trackDTOList.isEmpty()) {
                    trackingInfo.setAngle(trackDTOList.get(0).getAngle());
                    trackingInfo.setMatchedLocation(trackDTOList.get(0).getMatchedLocation());
                } else
                    continue;
            }
            switch (trackingInfo.getTransportStatus()) {
                case 상차지로이동중, 회수지로이동중 -> pickupVehicle++;
                case 하차지로이동중, 반품지로이동중, 조선소로이동중 -> dropOffVehicle++;
                case 조선소입문 -> shipEntryVehicle++;
                case 운송완료 -> completedVehicle++;
            }

            VMSVehicleList.add(trackingInfo);
        }
        VMSTracking.setPickupVehicle(pickupVehicle);
        VMSTracking.setDropOffVehicle(dropOffVehicle);
        VMSTracking.setShipEntryVehicle(shipEntryVehicle);
        VMSTracking.setCompletedVehicle(completedVehicle);
        VMSTracking.setVehicleInfoList(VMSVehicleList);

        return VMSTracking;
    }

    public RealTrackingDTO.ShipmentTrackingInfo getShipmentTracking(List<String> shipmentKeyList) {
        RealTrackingDTO.ShipmentTrackingInfo VMSTracking = new RealTrackingDTO.ShipmentTrackingInfo();
        List<RealTrackingDTO.VMSVehicleInfo> VMSVehicleList = new ArrayList<>();

        //int pickupVehicle = 0;
        //int dropOffVehicle = 0;
        int pickAndDropVehicle = 0;
        int shipEntryVehicle = 0;
        int completedVehicle = 0;

        for (String shipmentKey : shipmentKeyList) {
            RealTrackingDTO.VMSVehicleInfo trackingInfo = new RealTrackingDTO.VMSVehicleInfo();

            Long vehicleId = realTrackingRepository.getVehicleByShipment(shipmentKey);
            TransportHistoryDTO transportHistory = transportHistoryService.getLastHistoryByShipmentKey(shipmentKey);
            if (Objects.nonNull(transportHistory)) {
                trackingInfo.setTransportStatus(transportHistory.getTransportStatus());
                trackingInfo.setShpmtky(transportHistory.getShipmentKey());
                Optional<ShipmentDTO> ship = shipmentService.getShipment2(transportHistory.getCompkey(), transportHistory.getShipmentKey());
                ship.ifPresent(s -> trackingInfo.setLoadrat(s.getLoadrat()));
                List<ShipmentSectionDTO> shipList = shipmentSectionService.getShipmentSectionListByShipment(transportHistory.getCompkey(), shipmentKey);
                if (!shipList.isEmpty()) {
                    int opakqty = shipList.stream()
                            .filter(s -> s.getVsttype() == VisitTypeEnum.PICKUP)
                            .mapToInt(ShipmentSectionDTO::getOpakqty)
                            .sum();
                    trackingInfo.setOpakqty(opakqty);
                }

                List<TrackDTO> trackDTOList = trackService.getLatestByShpmtkys(Collections.singletonList(transportHistory.getShipmentKey()));
                if (!trackDTOList.isEmpty()) {
                    trackingInfo.setAngle(trackDTOList.get(0).getAngle());
                    trackingInfo.setMatchedLocation(trackDTOList.get(0).getMatchedLocation());
                    switch (trackingInfo.getTransportStatus()) {
                        case 운송시작, 상차지로이동중, 상차지도착, 상차완료, 하차지로이동중, 하차지도착, 하차완료, 조선소로이동중 -> pickAndDropVehicle++;
                        case 조선소입문 -> shipEntryVehicle++;
                        case 운송완료 -> completedVehicle++;
                    }

                    Optional<DriverVehicleInfoDTO.DriverVehicleInfoCustom> vehicleInfo = driverVehicleInfoService.findVehliceInfoByVehicleId(vehicleId);
                    vehicleInfo.ifPresent(s -> {
                        trackingInfo.setVehicleId(s.getVehicleId());
                        trackingInfo.setUseract(s.getUseract());
                        trackingInfo.setUsername(s.getUsernam());
                        trackingInfo.setVehicleBusinessType(s.getVehicleBusinessType());
                        trackingInfo.setVehicleType(s.getVehicleType());
                        trackingInfo.setVehicleWeight(s.getVehicleWeight());
                        trackingInfo.setRegistrationNumber(s.getRegistrationNumber());
                        trackingInfo.setTelphnm(s.getTelphnm());
                    });
                    VMSVehicleList.add(trackingInfo);
                }
            }
        }
        VMSTracking.setPickAndDropVehicle(pickAndDropVehicle);
        VMSTracking.setShipEntryVehicle(shipEntryVehicle);
        VMSTracking.setDeliverCompVehicle(completedVehicle);
        VMSTracking.setVehicleInfoList(VMSVehicleList);

        return VMSTracking;
    }
}
