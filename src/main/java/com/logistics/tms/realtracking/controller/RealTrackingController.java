package com.logistics.tms.realtracking.controller;

import com.logistics.tms.realtracking.dto.RealTrackingDTO;
import com.logistics.tms.realtracking.interfaces.RealTrackingSwagger;
import com.logistics.tms.realtracking.service.RealTrackingService;
import com.logistics.tms.transport.entity.TransportInfo;
import com.logistics.tms.transport.service.TransportInfoService;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;

@RestController
@RequestMapping("/api/realtracking")
@RequiredArgsConstructor
public class RealTrackingController implements RealTrackingSwagger {
    private final RealTrackingService realTrackingService;
    private final TransportInfoService transportInfoService;

    @GetMapping("/summary")
    public ResponseEntity<?> getSummary(
            @RequestParam(value = "today", required = false) @Nullable final String today) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate localDate = (today != null) ? LocalDate.parse(today, formatter) : LocalDate.now();
            List<RealTrackingDTO.TrackingSummaryDTO> response = realTrackingService.getTrackingSummary(localDate);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/statusSummary")
    public ResponseEntity<?> getStatusSummary(
            @RequestParam(value = "today", required = false) @Nullable final String today,
            @RequestParam(value = "round", required = false) @Nullable  final Integer round) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate localDate = (today != null) ? LocalDate.parse(today, formatter) : LocalDate.now();
            int transportRound = (round != null) ? round : 1;
            RealTrackingDTO.StatusSummaryDTO response = realTrackingService.getStatusSummary(localDate, transportRound);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/deliveryPopup")
    public ResponseEntity<?> getDeliveryPopup (@RequestParam @NotBlank final String shipmentKey) {
        try {
            List<RealTrackingDTO.DeliveryPopupDTO> response = realTrackingService.getDeliveryPopup(shipmentKey);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/detail")
    public ResponseEntity<?> getDetail (@RequestParam @NotBlank final String shipmentKey) {
        try {
            RealTrackingDTO.TransportDetailDTO response = realTrackingService.getTransportDetail(shipmentKey);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/otherCharge")
    public ResponseEntity<?> modifyOtherCharge(@RequestParam @NotBlank final String transportCode,
                                               @RequestParam final int otherCharge,
                                               @RequestParam @NotBlank final String otherChargeComment) {
        try {
            realTrackingService.modifyOtherCharge(transportCode, otherCharge, otherChargeComment);
            return ResponseEntity.ok().build();
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/modifyStatus")
    public ResponseEntity<?> modifyStatusByManager(
            @RequestParam @NotBlank final String transportCode,
            @RequestParam final TransportInfo.TransportType transportType,
            @RequestParam final TransportInfo.TransportStatus transportStatus,
            @RequestParam @NotBlank final String reason) {
        try {
            return ResponseEntity.ok(transportInfoService.modifyStatusByManager(transportCode, transportType, transportStatus, reason));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/vmsTracking")
    public ResponseEntity<?> getVMSTracking(
            @RequestParam(value = "today", required = false) @Nullable final String today,
            @RequestParam @NotBlank final String dropOffCompany,
            @RequestParam @NotBlank final String partnerKey) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate localDate = (today != null) ? LocalDate.parse(today, formatter) : LocalDate.now();
            RealTrackingDTO.VMSTrackingInfo response = realTrackingService.getVMSTracking(localDate, dropOffCompany, partnerKey);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/shipTracking")
    public ResponseEntity<?> getShipmentTracking(@RequestParam final List<String> shipmentKeyList) {
        try {
            RealTrackingDTO.ShipmentTrackingInfo response = realTrackingService.getShipmentTracking(shipmentKeyList);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }
}
