package com.logistics.tms.realtracking.controller;

import com.logistics.tms.realtracking.constant.TransportHistoryWebConstant;
import com.logistics.tms.realtracking.dto.TransportHistoryExcelDTO;
import com.logistics.tms.realtracking.dto.TransportHistoryWebDTO;
import com.logistics.tms.realtracking.interfaces.TransportHistoryWebSwagger;
import com.logistics.tms.realtracking.service.TransportHistoryWebService;
import com.logistics.tms.transport.dto.TransportConfirmationDTO;
import com.logistics.tms.transport.entity.TransportInfo;
import com.logistics.tms.transport.service.TransportInfoService;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.*;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Objects;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;


@RestController
@RequestMapping("/api/history")
@RequiredArgsConstructor
public class TransportHistoryWebController implements TransportHistoryWebSwagger {
    private final TransportHistoryWebService transportHistoryWebService;
    private final TransportInfoService transportInfoService;

    @GetMapping("/page")
    public ResponseEntity<?> getHistoy(@Nullable final TransportHistoryWebConstant.HistorySearchCondition historySearchCondition,
                                       @Nullable final String historySearch,
                                       @RequestParam final Integer sortMethod,
                                       @RequestParam final Integer periodCondition,
                                       @RequestParam @NotBlank final String fromDate,
                                       @RequestParam @NotBlank final String toDate,
                                       @Nullable final Pageable pageable) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate startDate = LocalDate.parse(fromDate, formatter);
            LocalDate endDate = LocalDate.parse(toDate, formatter);

            if (historySearchCondition != null) {
                if (historySearch == null)
                    return ResponseEntity.badRequest().body("historySearch is null.");
                if (historySearch.length() < 3)
                    return ResponseEntity.badRequest().body("historySearch length is short than 3.");
            }

            Page<TransportHistoryWebDTO.HistoryWebDTO> response = transportHistoryWebService.getHistory(
                    historySearchCondition, historySearch, null, null, null, null,
                    sortMethod, periodCondition, startDate, endDate, pageable);
            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/mobilePage")
    public ResponseEntity<?> getMobileHistoy(@Nullable String shipNoSearch,
                                             @Nullable String vehicleSearch,
                                             @Nullable String pickupSearch,
                                             @Nullable String dropoffSearch,
                                             @RequestParam final Integer sortMethod,
                                             @RequestParam final Integer periodCondition,
                                             @RequestParam @NotBlank final String fromDate,
                                             @RequestParam @NotBlank final String toDate,
                                             @Nullable final Pageable pageable) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate startDate = LocalDate.parse(fromDate, formatter);
            LocalDate endDate = LocalDate.parse(toDate, formatter);

            Page<TransportHistoryWebDTO.HistoryWebDTO> response = transportHistoryWebService.getHistory(
                    null, null, shipNoSearch, vehicleSearch, pickupSearch, dropoffSearch,
                    sortMethod, periodCondition, startDate, endDate, pageable);
            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/downloadExcel")
    public ResponseEntity<?> downloadExcel(
            @Nullable final TransportHistoryWebConstant.HistorySearchCondition historySearchCondition,
            @Nullable final String historySearch,
            @RequestParam final Integer sortMethod,
            @RequestParam final Integer periodCondition,
            @RequestParam @NotBlank final String fromDate,
            @RequestParam @NotBlank final String toDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate startDate = LocalDate.parse(fromDate, formatter);
        LocalDate endDate = LocalDate.parse(toDate, formatter);

        if (historySearchCondition != null) {
            if (historySearch == null) {
                return ResponseEntity.badRequest().body("error: historySearch is null.");
            }
            if (Objects.requireNonNull(historySearch).length() < 3) {
                return ResponseEntity.badRequest().body("error: historySearch length is short than 3.");
            }
        }

        List<TransportHistoryExcelDTO> historyList = transportHistoryWebService.getTransportHistoryExcel(
                historySearchCondition, historySearch, sortMethod, periodCondition, startDate, endDate);

        String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String fileName = "TransportHistory_" + now + ".xlsx";
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);

       try {
           ByteArrayOutputStream outputStream;
           try (XSSFWorkbook workbook = new XSSFWorkbook()) {
               CellStyle cellStyle = workbook.createCellStyle();
               cellStyle.setAlignment(HorizontalAlignment.CENTER);
               cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
               cellStyle.setBorderBottom(BorderStyle.THIN);
               cellStyle.setBorderTop(BorderStyle.THIN);
               cellStyle.setBorderLeft(BorderStyle.THIN);
               cellStyle.setBorderRight(BorderStyle.THIN);

               CellStyle headerStyle = workbook.createCellStyle();
               headerStyle.cloneStyleFrom(cellStyle);
               Font boldFont = workbook.createFont();
               boldFont.setBold(true);
               headerStyle.setFont(boldFont);
               headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
               headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

               XSSFSheet sheet = workbook.createSheet("운송 내역");
               PrintSetup printSetup = sheet.getPrintSetup();
               printSetup.setLandscape(true);
               sheet.createFreezePane(0, 1);
               sheet.setRepeatingRows(CellRangeAddress.valueOf("1:1"));

               int rowCount = 0;
               String[] header = new String[]{"Shipment No.", "단독/혼적", "요청 일시", "완료 일시", "기사", "차량 정보", "상차지", "하차지"};
               int[] width = {12, 10, 18, 19, 9, 10, 20, 14};
               Row headerRow = sheet.createRow(rowCount++);
               for (int col = 0; col < header.length; col++) {
                   Cell cell = headerRow.createCell(col);
                   cell.setCellValue(header[col]);
                   cell.setCellStyle(headerStyle);
                   sheet.setColumnWidth(col, 256 * width[col]);
               }

               for (TransportHistoryExcelDTO transportHistoryExcelDTO : historyList) {
                   Row bodyRow = sheet.createRow(rowCount++);
                   for (int col = 0; col < header.length; col++) {
                       Cell cell = bodyRow.createCell(col);
                       cell.setCellStyle(cellStyle);

                       switch (col) {
                           case 0:
                               cell.setCellValue(transportHistoryExcelDTO.getShipmentKey());
                               break;
                           case 1:
                               cell.setCellValue(transportHistoryExcelDTO.getTransportCount());
                               break;
                           case 2:
                               cell.setCellValue(transportHistoryExcelDTO.getRequestDate());
                               break;
                           case 3:
                               cell.setCellValue(transportHistoryExcelDTO.getCompletedDate());
                               break;
                           case 4:
                               cell.setCellValue(transportHistoryExcelDTO.getUsername());
                               break;
                           case 5:
                               cell.setCellValue(transportHistoryExcelDTO.getVehicleInfo());
                               break;
                           case 6:
                               cell.setCellValue(transportHistoryExcelDTO.getPickupCompany());
                               break;
                           case 7:
                               cell.setCellValue(transportHistoryExcelDTO.getDropOffCompany());
                               break;
                       }
                   }
               }
               outputStream = new ByteArrayOutputStream();
               workbook.write(outputStream);
           }
           HttpHeaders headers = new HttpHeaders();
           headers.setContentDispositionFormData("attachment", encodedFileName);
           headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

           return ResponseEntity.ok()
                   .headers(headers)
                   .body(outputStream.toByteArray());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @GetMapping("/detail")
    public ResponseEntity<?> getDetail (@RequestParam @NotBlank final String shipmentKey) {
        try {
            TransportHistoryWebDTO.HistoryWebDetailDTO response = transportHistoryWebService.getTransportHistoryDetail(shipmentKey);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/modifyComplete")
    public ResponseEntity<?> modifyComplete(@RequestParam final Long transportId) {
        try {
            return ResponseEntity.ok(transportInfoService.modifyTransportStatus(transportId, TransportInfo.TransportStatus.운송완료, (float)0, (float)0));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/downloadZip")
    public ResponseEntity<InputStreamResource> downloadZip(@RequestParam @NotBlank final String shipmentKey) throws Exception {
        // 압축할 파일 목록
        List<String> filePaths = transportInfoService.getSignUrl(shipmentKey);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ZipOutputStream zipOutputStream = getZipOutputStream(outputStream, filePaths);

        zipOutputStream.close();

        ByteArrayInputStream bis = new ByteArrayInputStream(outputStream.toByteArray());
        HttpHeaders headers = new HttpHeaders();
        String zipFileName = "signUrl_" + shipmentKey + ".zip";
        headers.setContentDispositionFormData("attachment", zipFileName);
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(outputStream.size())
                .body(new InputStreamResource(bis));
    }

    private static ZipOutputStream getZipOutputStream(ByteArrayOutputStream outputStream, List<String> filePaths) throws IOException {
        ZipOutputStream zipOutputStream = new ZipOutputStream(outputStream);

        for (String filePath : filePaths) {
            File file = new File(filePath);
            FileInputStream fileInputStream = new FileInputStream(file);

            ZipEntry zipEntry = new ZipEntry(file.getName());
            zipOutputStream.putNextEntry(zipEntry);

            byte[] buffer = new byte[1024];
            int len;
            while ((len = fileInputStream.read(buffer)) > 0) {
                zipOutputStream.write(buffer, 0, len);
            }

            fileInputStream.close();
            zipOutputStream.closeEntry();
        }
        return zipOutputStream;
    }

    @GetMapping("/confirmation")
    public ResponseEntity<?> getTransportConfirmation(@RequestParam @NotBlank final String shipmentKey) {
        try {
            TransportConfirmationDTO.ShippingDTO response = transportInfoService.getTransportConfirmation(shipmentKey);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

}
