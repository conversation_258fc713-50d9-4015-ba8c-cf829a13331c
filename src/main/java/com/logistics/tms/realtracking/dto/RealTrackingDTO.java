package com.logistics.tms.realtracking.dto;

import com.logistics.tms.common.enumeration.*;
import com.logistics.tms.dispatch.dto.DispatchResponseDTO;
import com.logistics.tms.transport.dto.TransportHistoryResponseDTO;
import com.logistics.tms.transport.entity.TransportEvent;
import com.logistics.tms.transport.entity.TransportInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.locationtech.jts.geom.Point;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

public class RealTrackingDTO {
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class TrackingSummaryDTO {
        @Schema(description = "요청 일자", example = "2024-10-08")
        private LocalDate requestDate;

        @Schema(description = "운송 회차", example = "1 or 2")
        private int transportRound;

        @Schema(description = "전체 차량 개수")
        private int totalVehicle;

        @Schema(description = "단독 차량 개수")
        private int singleVehicle;

        @Schema(description = "혼적 차량 개수")
        private int mixedVehicle;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class TrackingDriverVehicleInfo {
        @Schema(description = "차량 ID", example = "1")
        private Long vehicleId; // 차량 ID

        @Schema(description = "사용자이름", example = "홍길동")
        private String username;

        @Schema(description = VehicleBusinessTypeEnum.COLUMN_COMMENT, example = VehicleBusinessTypeEnum.EXAMPLE)
        private VehicleBusinessTypeEnum vehicleBusinessType;

        @Schema(description = VehicleTypeEnum.COLUMN_COMMENT, example = VehicleTypeEnum.EXAMPLE)
        private VehicleTypeEnum vehicleType;

        @Schema(description = VehicleWeightTypeEnum.COLUMN_COMMENT, example = VehicleWeightTypeEnum.EXAMPLE)
        private VehicleWeightTypeEnum vehicleWeight; // 차량톤수: T01(1톤), T025(2.5톤), T05(5톤), T11(11톤)

        @Schema(description = "차량등록번호", example = "서울11가1112")
        private String registrationNumber;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class TrackingTimeLineDTO {
        @Schema(description = "운송 ID", example = "1")
        private Long transportId;

        @Schema(description = "운송주문번호", example = "3000046200")
        private String transportCode;

        @Schema(description = "운송 종류")
        private TransportInfo.TransportType transportType;

        @Schema(description = "운송 상태")
        private TransportInfo.TransportStatus transportStatus;

        @Schema(description = "운송 순서", example = "1, 2, 3, 4, ...")
        private int transportOrder;

        @Schema(description = "요청 일자", example = "2024-10-08")
        private LocalDate requestDate;

        @Schema(description = "요청 시간", example = "08:00:00")
        private LocalTime requestTime;

        @Schema(description = "완료 일자", example = "2024-10-08")
        private LocalDate completedDate;

        @Schema(description = "완료 시간", example = "08:00:00")
        private LocalTime completedTime;

        @Schema(description = "소요 시간(분)", example = "60")
        private int requiredTime;

        @Schema(description = "예상 도착 일자", example = "2024-10-08")
        private LocalDate estimatedDate;

        @Schema(description = "예상 도착 시간", example = "08:00:00")
        private LocalTime estimatedTime;

        @Schema(description = "운송지 상호명", example = "세광중공업")
        private String companyName;

        @Schema(description = "조선소 하차지", example = "제 1 적치장")
        private String dropOffArea;

        @Schema(description = "운송지 주소", example = "울산시 동구 방어동175-1번지")
        private String companyAddress;

        @Schema(description = "운송지 좌표", example = "{\"x\": 128.625605,\"y\": 34.879160}")
        private Point companyLocation;

        @Schema(description = "불가 사유", example = "악천후로 운송 불가합니다.")
        private String impossible;

        @Schema(description = "이벤트 목록")
        List<TrackingEventDTO> eventList;

        @Schema(description = "상태 변경 사유(관리자)", example = "기사 앱 동작 이상의 문제")
        private String changeReason;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class TrackingEventDTO {
        @Schema(description = "이벤트 종류")
        private TransportEvent.EventType eventType;

        @Schema(description = "이벤트 상세 내용", example = "날씨가 추워져서 시동이 안걸려요.")
        private String detail;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class TransportInfoDTO {
        @Schema(description = "Shipment No.", example = "S123456789")
        private String shipmentKey;

        @Schema(description = "운송 개수 1이면 단독, 2이상이면 혼적")
        private int transportCount;

        @Schema(description = "운송 상태")
        private TransportInfo.TransportStatus transportStatus;

        @Schema(description = "운송 회차", example = "1 or 2")
        private int transportRound;

        @Schema(description = "요청 일자", example = "2024-10-08")
        private LocalDate requestDate;

        @Schema(description = "요청 시간", example = "08:00:00")
        private LocalTime requestTime;

        @Schema(description = "완료 시간", example = "08:00:00")
        private LocalTime completedTime;

        @Schema(description = "운송지 상호명", example = "세광중공업")
        private String companyName;

        @Schema(description = "운송지 주소", example = "울산시 동구 방어동175-1번지")
        private String companyAddress;

        @Schema(description = "차량 정보")
        TrackingDriverVehicleInfo vehicleInfo;

        @Schema(description = "timeLine 배열")
        List<TrackingTimeLineDTO> timeLineList;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class StatusSummaryDTO {
        @Schema(description = "운송 대기 대수")
        private int readyVehicle;

        @Schema(description = "운송중 대수")
        private int inTransitVehicle;

        @Schema(description = "운송 완료 대수")
        private int completedVehicle;

        @Schema(description = "운송 불가 대수")
        private int impossibleVehicle;

        @Schema(description = "운송 정보 배열")
        List<TransportInfoDTO> transportInfoList;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class DeliveryPopupDTO {
        @Schema(description = "운송주문번호", example = "3000046200")
        private String transportCode;

        @Schema(description = "요청 일자", example = "2024-10-08")
        private LocalDate requestDate;

        @Schema(description = "물량", example = "one-side / 2ea")
        private String quantity;

        @Schema(description = "상차 상호명", example = "세광중공업")
        private String pickupCompanyName;

        @Schema(description = "상차 요청 시간", example = "08:00:00")
        private LocalTime pickupRequestTime;

        @Schema(description = "하차 상호명", example = "삼성중공업")
        private String dropOffCompanyName;

        @Schema(description = "하차 요청 시간", example = "08:00:00")
        private LocalTime dropOffRequestTime;

        @Schema(description = "운송 개수 1: 단독, 2이상: 혼적", example = "1, 2, ...")
        private int transportCount;

        @Schema(description = "제약 사항", example = "크레인 상차, 윙바디 불가")
        private String restriction;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class TrackingDeliveryDTO {
        @Schema(description = "운송주문번호", example = "3000046200")
        private String transportCode;

        @Schema(description = "운송 종류")
        private TransportInfo.TransportType transportType;

        @Schema(description = "운송 상태")
        private TransportInfo.TransportStatus transportStatus;

        @Schema(description = "소요 시간(분)", example = "60")
        private int requiredTime;

        @Schema(description = "제약 사항", example = "크레인 상차, 윙바디 불가")
        private String restriction;

        @Schema(description = "상차 상호명", example = "세광중공업")
        private String pickupCompanyName;

        @Schema(description = "상차 주소", example = "울산시 동구 방어동175-1번지")
        private String pickupCompanyAddress;

        @Schema(description = "상차 요청 일자", example = "2024-10-08")
        private LocalDate pickupRequestDate;

        @Schema(description = "상차 요청 시간", example = "08:00:00")
        private LocalTime pickupRequestTime;

        @Schema(description = "하차 상호명", example = "삼성중공업")
        private String dropOffCompanyName;

        @Schema(description = "하차 요청 일자", example = "2024-10-08")
        private LocalDate dropOffRequestDate;

        @Schema(description = "하차 요청 시간", example = "08:00:00")
        private LocalTime dropOffRequestTime;

        @Schema(description = "이벤트 목록")
        List<TrackingEventDTO> eventList;

        @Schema(description = "기타 지급 비용", example = "125000")
        private int otherCharge;

        @Schema(description = "기타 지급 사유", example = "자재가 너무 무거워")
        private String otherChargeComment;

        @Schema(description = "상태 변경 사유(관리자)", example = "기사 앱 동작 이상의 문제")
        private String changeReason;

        @Schema(description = "팔레트 정보", example = "one-side / 2ea")
        private String quantity;

        @Schema(description = "팔레트 면적 (㎡)", example = "1")
        private int palletArea;

        @Schema(description = "포장 형태", example = "PLT(팔레트), NSTANDARD(비표준), ONESIDE(원사이드), BULK(벌크)")
        private PackageCategoryEnum packageCategory;

        @Schema(description = "포장 타입", example = "T1100(PLT 표준), T550(PLT 1/2), T225(PLT 1/4), NORMAL(비표준), WOOD(우드), OT1100(ONESIDE PLT 표준), OT550(ONESIDE PLT 1/2), OT225(ONESIDE PLT 1/4), BULK(벌크)")
        private PackageTypeEnum packageType;

        @Schema(description = "팔레트 위치", example = "1 ~ 8")
        private int palletPosition;

        @Schema(description = "팔레트 가로", example = "1000")
        private int palletWidth;

        @Schema(description = "팔레트 세로", example = "2000")
        private int palletDepth;

        @Schema(description = "팔레트 높이", example = "1500")
        private int palletHeight;

        @Schema(description = "중량(t)", example = "0.3")
        private BigDecimal weight;

        @Schema(description = "납품 품목 및 규격", example = "C509-Spring")
        private String deliveryItem;

        @Schema(description = "납품 수량", example = "200")
        private int deliveryQuantity;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class TransportDetailDTO {
        @Schema(description = "Shipment No.", example = "S123456789")
        private String shipmentKey;

        @Schema(description = "운송 개수 1이면 단독, 2이상이면 혼적")
        private int transportCount;

        @Schema(description = "사용자이름", example = "홍길동")
        private String username;

        @Schema(description = VehicleBusinessTypeEnum.COLUMN_COMMENT, example = VehicleBusinessTypeEnum.EXAMPLE)
        private VehicleBusinessTypeEnum vehicleBusinessType;

        @Schema(description = "차량등록번호", example = "서울11가1112")
        private String registrationNumber;

        @Schema(description = VehicleTypeEnum.COLUMN_COMMENT, example = VehicleTypeEnum.EXAMPLE)
        private VehicleTypeEnum vehicleType;

        @Schema(description = VehicleWeightTypeEnum.COLUMN_COMMENT, example = VehicleWeightTypeEnum.EXAMPLE)
        private VehicleWeightTypeEnum vehicleWeight; // 차량톤수: T01(1톤), T025(2.5톤), T05(5톤), T11(11톤)

        @Schema(description = "용차 섭외 금액")
        private Integer rentalCharge;

        @Schema(description = "운송 이력 배열")
        List<TransportHistoryResponseDTO.TransportHistoryByShipmentKeyDTO> historyList;

        @Schema(description = "timeLine 배열")
        List<RealTrackingDTO.TrackingTimeLineDTO> timeLineList;

        @Schema(description = "예상 운임비")
        private Integer estimatedFee;

        @Schema(description = "적재율")
        private Integer loadrat;

        @Schema(description = "적재 정보")
        private List<DispatchResponseDTO.LoadingInfo> loadingInfoList;

        @Schema(description = "납품 정보 배열")
        List<TrackingDeliveryDTO> deliveryList;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class DeliveryItemDTO {
        @Schema(description = "납품 품목 및 규격", example = "C509-Spring")
        private String skudesc;

        @Schema(description = "납품 수량", example = "200")
        private int rgitqty;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class VMSTrackingInfo {
        @Schema(description = "요청 일자", example = "2024-10-08")
        private LocalDate requestDate;

       @Schema(description = "상차 차량 개수")
        private int pickupVehicle;

        @Schema(description = "하차 차량 개수")
        private int dropOffVehicle;

        @Schema(description = "입문 차량 개수")
        private int shipEntryVehicle;

        @Schema(description = "완료 차량 개수")
        private int completedVehicle;

        @Schema(description = "차량 정보 배열")
        List<VMSVehicleInfo> vehicleInfoList;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipmentTrackingInfo {
        @Schema(description = "이동중 차량 개수")
        private int pickAndDropVehicle;

        @Schema(description = "입문 차량 개수")
        private int shipEntryVehicle;

        @Schema(description = "완료 차량 개수")
        private int deliverCompVehicle;

        @Schema(description = "차량 정보 배열")
        List<VMSVehicleInfo> vehicleInfoList;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class VMSVehicleInfo {
        @Schema(description = "차량 ID", example = "1")
        private Long vehicleId; // 차량 ID

        @Schema(description = "사용자ID", example = "driver02")
        private String useract; // 사용자ID

        @Schema(description = "사용자이름", example = "홍길동")
        private String username;

        @Schema(description = VehicleBusinessTypeEnum.COLUMN_COMMENT, example = VehicleBusinessTypeEnum.EXAMPLE)
        private VehicleBusinessTypeEnum vehicleBusinessType;

        @Schema(description = VehicleTypeEnum.COLUMN_COMMENT, example = VehicleTypeEnum.EXAMPLE)
        private VehicleTypeEnum vehicleType;

        @Schema(description = VehicleWeightTypeEnum.COLUMN_COMMENT, example = VehicleWeightTypeEnum.EXAMPLE)
        private VehicleWeightTypeEnum vehicleWeight;

        @Schema(description = "차량등록번호", example = "서울11가1112")
        private String registrationNumber;

        @Schema(description = "휴대전화번호")
        private String telphnm; // 휴대전화번호

        @Schema(description = "운송 상태")
        private TransportInfo.TransportStatus transportStatus;

        @Schema(description = "Shipment No.")
        private String shpmtky;

        @Schema(description = "적재율")
        private Integer loadrat;

        @Schema(description = "팔레트 박스 수량")
        private Integer opakqty;

        @Schema(description = "방위각", example = "66")
        private Integer angle;

        @Schema(description = "맵매칭된 위치", implementation = org.springframework.data.geo.Point.class, example = "127.0011,37.0011")
        private Point matchedLocation;
    }
}
