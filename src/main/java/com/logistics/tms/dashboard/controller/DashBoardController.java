package com.logistics.tms.dashboard.controller;

import com.logistics.tms.dashboard.dto.DashBoardDTO;
import com.logistics.tms.dashboard.interfaces.DashBoardSwagger;
import com.logistics.tms.dashboard.service.DashBoardService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;

@RestController
@RequestMapping("/api/dashboard")
@RequiredArgsConstructor
public class DashBoardController implements DashBoardSwagger {
    private final DashBoardService dashBoardService;

    // 오늘의 현황(운송사)
    @GetMapping("/summaryCount")
    public ResponseEntity<?> apiGetSummaryCount(@RequestParam(value = "today") final String today) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

            LocalDate localDate = LocalDate.parse(today, formatter);
            DashBoardDTO.SummaryCountDTO response = dashBoardService.getSummaryCount(localDate);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    // 월대/용차, 단독/혼적 비율
    @GetMapping("/vehicle")
    public ResponseEntity<?> apiGetVehicle(@RequestParam(value = "today") final String today) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

            LocalDate localDate = LocalDate.parse(today, formatter);
            List<DashBoardDTO.VehicleDTO> response = dashBoardService.getVehicle(localDate);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    // 운송/배차 계획
    @GetMapping("/plan")
    public ResponseEntity<?> apiGetPlan(@RequestParam(value = "fromDate") final String fromDate,
                                        @RequestParam(value = "toDate") final String toDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

            LocalDate fromLocalDate = LocalDate.parse(fromDate, formatter);
            LocalDate toLocalDate = LocalDate.parse(toDate, formatter);

            List<DashBoardDTO.PlanDTO> response = dashBoardService.getPlan(fromLocalDate, toLocalDate);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    // 정시 도착률
    @GetMapping("/arrival")
    public ResponseEntity<?> apiGetArrival(@RequestParam(value = "fromDate") final String fromDate,
                                        @RequestParam(value = "toDate") final String toDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

            LocalDate fromLocalDate = LocalDate.parse(fromDate, formatter);
            LocalDate toLocalDate = LocalDate.parse(toDate, formatter);

            List<DashBoardDTO.ArrivalDTO> response = dashBoardService.getArrival(fromLocalDate, toLocalDate);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    // 주요 조선사 운송, 납품 현황
    @GetMapping("/shipyard")
    public ResponseEntity<?> apiGetShipyard(@RequestParam(value = "today") final String today) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

            LocalDate localDate = LocalDate.parse(today, formatter);
            List<DashBoardDTO.ShipyardDTO> response = dashBoardService.getShipyard(localDate);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

//    @Hidden
//    @GetMapping("/summary")
//    public ResponseEntity<?> apiGetSummary(@RequestParam(value = "today") final String today) {
//        try {
//            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
//            LocalDate localDate = LocalDate.parse(today, formatter);
//            List<DashBoardDTO.SummaryDTO> response = dashBoardService.getSummary(localDate);
//
//            return ResponseEntity.ok(response);
//        } catch (DateTimeParseException e) {
//            return ResponseEntity.badRequest().body(e.getMessage());
//        }
//    }

    @Hidden
    @GetMapping("/cost")
    public ResponseEntity<?> apiGetCost(@RequestParam(value = "fromDate") final String fromDate,
                                     @RequestParam(value = "toDate") final String toDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate startDt = LocalDate.parse(fromDate, formatter);
            LocalDate endDt = LocalDate.parse(toDate, formatter);
            List<DashBoardDTO.CostDTO> response = dashBoardService.getCost(startDt, endDt);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @Hidden
    @GetMapping("/issue_todelete")
    public ResponseEntity<?> apiGetIssueToDelete(@RequestParam(value = "fromDate") final String fromDate,
                                      @RequestParam(value = "toDate") final String toDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate startDt = LocalDate.parse(fromDate, formatter);
            LocalDate endDt = LocalDate.parse(toDate, formatter);
            List<DashBoardDTO.IssueDTO> response = dashBoardService.getIssueToDelete(startDt, endDt);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @Hidden
    @GetMapping("/misDelivery")
    public ResponseEntity<?> apiGetMisDelivery(
            @RequestParam @NotBlank final String startDate,
            @RequestParam @NotBlank final String endDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate startDt = LocalDate.parse(startDate, formatter);
            LocalDate endDt = LocalDate.parse(endDate, formatter);
            return ResponseEntity.ok(dashBoardService.getMisDelivery(startDt, endDt));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @Hidden
    @Override
    @GetMapping(path = "/loadrat-bs")
    public ResponseEntity<?> apiGetKpiShipmentLoadartBs(@RequestParam @NotBlank final String startDate,
                                                @RequestParam @NotBlank final String endDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate startDt = LocalDate.parse(startDate, formatter);
            LocalDate endDt = LocalDate.parse(endDate, formatter);
            return ResponseEntity.ok(dashBoardService.getShipmentLoadartBs(startDt, endDt));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    // 오늘의 현황(조선사)
    @GetMapping("/shipyardStatus")
    public ResponseEntity<?> apiGetShipyardStatus(@RequestParam final String today,
                                                  @RequestParam final String partnerKey) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

            LocalDate localDate = LocalDate.parse(today, formatter);
            DashBoardDTO.ShipyarStatusDTO response = dashBoardService.getShipyardStatus(localDate, partnerKey);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    // 차량 톤수별 도착예정시간
    @GetMapping("/weightArrival")
    public ResponseEntity<?> apiGetWeightArrival(@RequestParam final String today,
                                                 @RequestParam final String partnerKey) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

            LocalDate localDate = LocalDate.parse(today, formatter);
            List<DashBoardDTO.VehicleWeightDTO> response = dashBoardService.getWeightArrival(localDate, partnerKey);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @Hidden
    @GetMapping("/issue")
    public ResponseEntity<?> apiGetIssue(@RequestParam(value = "fromDate") final String fromDate,
                                                 @RequestParam(value = "toDate") final String toDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate startDt = LocalDate.parse(fromDate, formatter);
            LocalDate endDt = LocalDate.parse(toDate, formatter);
            List<DashBoardDTO.IssueDTO> response = dashBoardService.getIssue(startDt, endDt);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    // 운송이슈 상세(운영사)
    @GetMapping("/issueDetail")
    public ResponseEntity<?> apiGetIssueDetail(@RequestParam(value = "fromDate") final String fromDate,
                                         @RequestParam(value = "toDate") final String toDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate startDt = LocalDate.parse(fromDate, formatter);
            LocalDate endDt = LocalDate.parse(toDate, formatter);
            List<DashBoardDTO.EventDetailDTO> response = dashBoardService.getIssueDetail(startDt, endDt);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

}
