package com.logistics.tms.dashboard.dto;

import com.logistics.tms.common.enumeration.*;
import com.logistics.tms.realtracking.dto.RealTrackingDTO;
import com.logistics.tms.transport.entity.TransportEvent;
import com.logistics.tms.transport.entity.TransportInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

public class DashBoardDTO {
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class SummaryCountDTO {
        @Schema(description = "전체 운송요청 수(파레트 단위)")
        private int totalTransport;

        @Schema(description = "완료 운송 수(파레트 단위)")
        private int completedTransport;

        @Schema(description = "총 차량 수")
        private int totalVehicle;

        @Schema(description = "운송대기 차량 수")
        private int readyVehicle;

        @Schema(description = "운송중 차량 수")
        private int goingVehicle;

        @Schema(description = "운송 완료 차량 수")
        private int finishedVehicle;

        @Schema(description = "운송 실패 차량 수")
        private int failedVehicle;

    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class VehicleDTO {
        @Schema(description = "배차 차수", example = "2")
        private int round;

        @Schema(description = "월대,지입 차량 수")
        private int depositVehicle;

        @Schema(description = "용차 차량 수")
        private int rentalVehicle;

        @Schema(description = "운송완료 월대,지입 차량 수")
        private int depositFinished;

        @Schema(description = "운송완료 용차 차량 수")
        private int rentalFinished;

        @Schema(description = "단독 배차 수")
        private int singleShipment;

        @Schema(description = "혼적 배차 수")
        private int mixedShipment;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class SummaryDTO {
        @Schema(description = "Shipment No.", example = "S123456789")
        private String shipmentKey;

        @Schema(description = "운송 개수 1이면 단독, 2이상이면 혼적")
        private int transportCount;

        @Schema(description = "운송 상태")
        private TransportInfo.TransportStatus transportStatus;

        @Schema(description = VehicleBusinessTypeEnum.COLUMN_COMMENT, example = VehicleBusinessTypeEnum.EXAMPLE)
        private VehicleBusinessTypeEnum vehicleBusinessType;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class CostDTO {
        @Schema(description = "요청 일자", example = "2024-10-08")
        private LocalDate requestDate;

        @Schema(description = "운송비 합계")
        private int costSum;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class IssueDTO {
        @Schema(description = "요청 일자", example = "2024-10-08")
        private LocalDate requestDate;

        @Schema(description = "Shipment No.", example = "S123456789")
        private String shipmentKey;

        @Schema(description = "이벤트 목록")
        List<RealTrackingDTO.TrackingEventDTO> eventList;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class MisDeliveryInfoDTO {
        @Schema(description = "요청 일자", example = "2024-10-08")
        private LocalDate requestDate;

        @Schema(description = "운송주문번호", example = "3000046200")
        private String transportCode;

        @Schema(description = "운송지 상호명", example = "세광중공업")
        private String companyName;

        @Schema(description = "조선소 하차지", example = "제 1 적치장")
        private String dropOffArea;

        @Schema(description = "운송 상태")
        private TransportInfo.TransportStatus transportStatus;

        @Schema(description = "불가 사유", example = "악천후로 운송 불가합니다.")
        private String impossible;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class MisDeliveryDTO {
        @Schema(description = "요청 일자", example = "2024-10-08")
        private LocalDate requestDate;

        @Schema(description = "운송주문번호", example = "3000046200")
        private String transportCode;

        @Schema(description = "운송지 상호명", example = "세광중공업")
        private String companyName;

        @Schema(description = "오배송 여부", example = "true")
        private boolean misDelivery;
    }

    @Schema(description = "KPI 차량적재율")
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class KpiShipmentLoadratDTO {

        @Schema(description = "Shipment No.")
        private String shpmtky;

        @Schema(description = "차량톤수")
        private ShipmentVehicleTonEnum vhctncd;

        @Schema(description = "적재율")
        private Integer loadrat;

        @Schema(description = "운송완료일자")
        private LocalDate completedDate;

        @Schema(description = "협력업체 ptnrkey")
        private String ptnrkey;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipyardDTO {
        @Schema(description = "조선사 명칭", example = "현대중공업")
        private String nameShipyard;

        @Schema(description = "조선사 운송대기 운송 수")
        private int readyCount;

        @Schema(description = "조선사 운송중 운송 수")
        private int transportCount;

        @Schema(description = "조선사 완료 운송 수")
        private int completeCount;

        @Schema(description = "조선사 불가 운송 수")
        private int failedCount;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class PlanDTO {
        @Schema(description = "인덱스", example = "D-0")
        private String index;

        @Schema(description = "목록 명칭", example = "미지정리스트")      // 미지정리스트, 미배차리스트
        private String nameList;

        @Schema(description = "운송 일자", example = "2025-05-25")
        private String dateTransport;

        @Schema(description = "운송 회차", example = "2")
        private int roundTransport;

        @Schema(description = "운송 주문 번호", example = "3000055571")
        private String numberOrder;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ArrivalDTO {
        @Schema(description = "날짜", example = "2025-05-25")
        private String dateArrival;

        @Schema(description = "정시 도착 건수")
        private int regularArrival;

        @Schema(description = "지연 도착 건수")
        private int delayedArrival;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipyarStatusDTO {
        @Schema(description = "운행중 배차 건수")
        private int goingTransport;

        @Schema(description = "오전 완료 건수")
        private int amFinished;

        @Schema(description = "오후 완료 건수")
        private int pmFinished;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class VehicleWeightDTO {
        @Schema(description = "Shipment No.", example = "S000006233")
        private String shipmentNumber;

//        @Schema(description = "운송주문 번호")
//        private String orderNumber;

        @Schema(description = "차량 톤수")
        private Float vehicleWeight;

        @Schema(description = "도착 예정 시간", example = "1627")
        private String timeArrival;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class IssueSimpleDTO {
        @Schema(description = "전체 운송 건수")
        private int totalTransport;

        @Schema(description = "전체 운송지연 건수")
        private int delayedTransport;

        @Schema(description = "전체 오배송 건수")
        private int misdeliveryTransport;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class IssueDetailDTO {
        @Schema(description = "보관존")
        private String issue;

        @Schema(description = "접수일")
        private String date;

        @Schema(description = "접수자")
        private String reporter;

        @Schema(description = "이슈원인")
        private String issueReason;

        @Schema(description = "처리상태")
        private String status;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class EventDriverDTO {
        @Schema(description = "shipment")
        private String shpmtky;

        @Schema(description = "차량id")
        private Integer vhclid ;

        @Schema(description = "운송지 상호명")
        private String trcpnm;

        @Schema(description = "이벤트 종류")
        private TransportEvent.EventType eventType;

        @Schema(description = "생성 일자", example = "20241009")
        private LocalDate credate;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class OetmhdPartialDTO {
        private String oetmsky;
        private String ptnrkey;
        private String loadkey;
        private String custkey;
        private String destkey;
        private OrderStatusEnum tshitst;
        private VehicleStatusEnum trnstat;
        private Integer manuvhp;
        private PackageCategoryEnum opkcate;
        private PackageTypeEnum opktype;
        private String onsidty;
        private Integer opakqty;
        private TruckLoadTypeEnum cargoty;
        private Integer pakwidh;
        private Integer pakdept;
        private Integer pakheig;
        private BigDecimal pkrweig;
        private LocalDate lodrqdt;
        private LocalTime lodrqtm;
        private LocalDate uldrqdt;
        private LocalTime uldrqtm;
        private LocalDate loendat;
        private LocalTime loentim;
        private LocalDate dvendat;
        private LocalTime dventim;

        private LocalDate loaddat;
        private LocalTime loadtim;
        private LocalDate dvcmpdt;
        private LocalTime dvcmptm;
        private LocalDate cucmpdt;
        private LocalTime cucmptm;

        private String tmshpno;
        private Boolean turgtyn; // 긴급운송여부
        private String vhctncd;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ShipyardVehicleDTO {
        @Schema(description = "shipment")
        private String shpmtky;

        @Schema(description = "운송주문번호")
        private String oetmsky;

        @Schema(description = "차량id")
        private Long vhclid ;

        @Schema(description = "하차지 도착 일자")
        private LocalDate dvendat;

        @Schema(description = "하차지 도착 시간")
        private LocalTime dventim;

        @Schema(description = "납품 완료 일자")
        private LocalDate dvcmpdt;

        @Schema(description = "납품 완료 시간")
        private LocalTime dvcmptm;

        @Schema(description = "조선소 입문 일자")
        private LocalDate cucmpdt;

        @Schema(description = "조선소 입문 시간")
        private LocalTime cucmptm;

        @Schema(description = "운송 종류")
        private TransportInfo.TransportType trtype;

        @Schema(description = "운송 상태")
        private TransportInfo.TransportStatus trstat;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class EventDetailDTO {
        @Schema(description = "shipment", example = "S000006233")
        private String shipmentKey;

        @Schema(description = "차량id")
        private Long vehicleId ;

        @Schema(description = "사용자이름", example = "홍길동")
        private String userName ;

        @Schema(description = "운송종류", example = "상차")
        private TransportInfo.TransportType transportType;

        @Schema(description = "운송지 상호명", example = "동아밸브공업사")
        private String companyName;

        @Schema(description = "이벤트 종류", example = "정차시사고발생")
        private TransportEvent.EventType eventType;

        @Schema(description = "이벤트 상세", example = "뒤에서 그랜져가 받았어요")
        private String detail;

        @Schema(description = "생성 일자")
        private LocalDate createdDate;
    }
}
