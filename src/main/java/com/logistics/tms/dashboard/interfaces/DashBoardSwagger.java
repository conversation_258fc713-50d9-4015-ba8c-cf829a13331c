package com.logistics.tms.dashboard.interfaces;

import com.logistics.tms.dashboard.dto.DashBoardDTO;
import com.logistics.tms.dashboard.dto.DashBoardDTO.KpiShipmentLoadratDTO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestParam;

@Tag(name = "DashBoard", description = "대시보드(관리자웹) API")
public interface DashBoardSwagger {

    @Operation(summary = "오늘의 현황(운송사) 조회")
    @Parameters({
            @Parameter(name = "today", description = "기준 일자", example = "20250125")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DashBoardDTO.SummaryCountDTO.class)))
    })
    ResponseEntity<?> apiGetSummaryCount(@RequestParam(value = "today") final String today);

    @Operation(summary = "월대/용차, 단독/혼적 현황 조회")
    @Parameters({
            @Parameter(name = "today", description = "기준 일자", example = "20250125")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DashBoardDTO.VehicleDTO.class)))
    })
    ResponseEntity<?> apiGetVehicle(@RequestParam(value = "today") final String today);

    @Operation(summary = "주요 조선사 운송,납품 현황 조회")
    @Parameters({
            @Parameter(name = "today", description = "기준 일자", example = "20250125")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DashBoardDTO.ShipyardDTO.class)))
    })
    ResponseEntity<?> apiGetShipyard(@RequestParam(value = "today") final String today);

    @Operation(summary = "오늘의 현황(조선사) 조회")
    @Parameters({
            @Parameter(name = "today", description = "기준 일자", example = "20250125"),
            @Parameter(name = "partnerKey", description = "조선사 파트너키", example = "HHI")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DashBoardDTO.ShipyarStatusDTO.class)))
    })
    ResponseEntity<?> apiGetShipyardStatus(@RequestParam(value = "today") final String today,
                                           @RequestParam(value = "partnerKey") final String partnerKey);

    @Operation(summary = "차량 톤수별 도착예정시간 조회")
    @Parameters({
            @Parameter(name = "today", description = "기준 일자", example = "20250125"),
            @Parameter(name = "partnerKey", description = "조선사 파트너키", example = "HHI")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DashBoardDTO.VehicleWeightDTO.class)))
    })
    ResponseEntity<?> apiGetWeightArrival(@RequestParam(value = "today") final String today,
                                          @RequestParam(value = "partnerKey") final String partnerKey);

    @Operation(summary = "운송/배차계획 조회")
    @Parameters({
            @Parameter(name = "fromDate", description = "시작 일자", example = "20250125"),
            @Parameter(name = "toDate", description = "끝 일자", example = "20250127")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DashBoardDTO.PlanDTO.class)))
    })
    ResponseEntity<?> apiGetPlan(@RequestParam(value = "fromDate") final String fromDate,
                                 @RequestParam(value = "toDate") final String toDate);

    @Operation(summary = "정시 도착률 조회")
    @Parameters({
            @Parameter(name = "fromDate", description = "시작 일자", example = "20250125"),
            @Parameter(name = "toDate", description = "끝 일자", example = "20250127")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = DashBoardDTO.ArrivalDTO.class)))
    })
    ResponseEntity<?> apiGetArrival(@RequestParam(value = "fromDate") final String fromDate,
                                    @RequestParam(value = "toDate") final String toDate);

//    @Operation(summary = "배차 목록 조회")
//    @Parameters({
//            @Parameter(name = "today", description = "기준 일자", example = "20250108")
//    })
//    @ApiResponses({
//            @ApiResponse(responseCode = "200", description = "조회 성공",
//                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
//                            array = @ArraySchema(schema = @Schema(implementation = DashBoardDTO.SummaryDTO.class))))
//    })
//    ResponseEntity<?> apiGetSummary(@RequestParam(value = "today") final String today);

    @Operation(summary = "운송비 조회")
    @Parameters({
            @Parameter(name = "fromDate", description = "시작 일자", example = "20250108"),
            @Parameter(name = "toDate", description = "끝 일자", example = "20250109")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            array = @ArraySchema(schema = @Schema(implementation = DashBoardDTO.CostDTO.class))))
    })
    ResponseEntity<?> apiGetCost(@RequestParam(value = "fromDate") final String fromDate,
                              @RequestParam(value = "toDate") final String toDate);

    @Operation(summary = "운송 이슈 조회")
    @Parameters({
            @Parameter(name = "fromDate", description = "시작 일자", example = "20250108"),
            @Parameter(name = "toDate", description = "끝 일자", example = "20250109")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            array = @ArraySchema(schema = @Schema(implementation = DashBoardDTO.IssueDTO.class))))
    })
    ResponseEntity<?> apiGetIssueToDelete(@RequestParam(value = "fromDate") final String fromDate,
                               @RequestParam(value = "toDate") final String toDate);

    @Operation(summary = "KPI 오배송율 조회")
    @Parameters({
            @Parameter(name = "startDate", description = "시작 일자", required = true, example = "20250121"),
            @Parameter(name = "endDate", description = "끝 일자", required = true, example = "20250124")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = DashBoardDTO.MisDeliveryDTO.class)))
    })
    ResponseEntity<?> apiGetMisDelivery(@RequestParam @NotBlank final String startDate, @RequestParam @NotBlank final String endDate);

    // KPI API
    @Operation(summary = "차량적재율 조회")
    @Parameters({
        @Parameter(name = "startDate", description = "시작 일자", required = true, example = "20250101"),
        @Parameter(name = "endDate", description = "끝 일자", required = true, example = "20250131"),
    })
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = KpiShipmentLoadratDTO.class))
        ),
        @ApiResponse(responseCode = "404", description = "찾을 수 없음",
                    content = @Content(schema = @Schema(hidden = true))
        ),
    })
    ResponseEntity<?> apiGetKpiShipmentLoadartBs(@RequestParam @NotBlank final String startDate,
                                            @RequestParam @NotBlank final String endDate);

    @Operation(summary = "운송 이슈 조회")
    @Parameters({
            @Parameter(name = "fromDate", description = "시작 일자", example = "20250125"),
            @Parameter(name = "toDate", description = "끝 일자", example = "20250131")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            array = @ArraySchema(schema = @Schema(implementation = DashBoardDTO.IssueDTO.class))))
    })
    ResponseEntity<?> apiGetIssue(@RequestParam(value = "fromDate") final String fromDate,
                                          @RequestParam(value = "toDate") final String toDate);

    @Operation(summary = "상세 운송 이슈 조회")
    @Parameters({
            @Parameter(name = "fromDate", description = "시작 일자", example = "20250125"),
            @Parameter(name = "toDate", description = "끝 일자", example = "20250131")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            array = @ArraySchema(schema = @Schema(implementation = DashBoardDTO.EventDetailDTO.class))))
    })
    ResponseEntity<?> apiGetIssueDetail(@RequestParam(value = "fromDate") final String fromDate,
                                  @RequestParam(value = "toDate") final String toDate);
}
