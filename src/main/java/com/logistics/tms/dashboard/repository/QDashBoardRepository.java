package com.logistics.tms.dashboard.repository;

import com.logistics.tms.common.enumeration.TruckLoadTypeEnum;
import com.logistics.tms.dashboard.dto.DashBoardDTO;
import com.logistics.tms.dashboard.dto.DashBoardDTO.KpiShipmentLoadratDTO;
import com.logistics.tms.driver.dto.DriverInfoDTO;
import com.logistics.tms.driver.model.QDriverVehicleInfo;
import com.logistics.tms.external.model.QOetmhd;
import com.logistics.tms.external.model.QSusrma;
import com.logistics.tms.shipment.dto.ShipmentCustomDTO;
import com.logistics.tms.transport.entity.QTransportEvent;
import com.logistics.tms.transport.entity.QTransportInfo;
import com.logistics.tms.transport.entity.TransportInfo;
import com.logistics.tms.shipment.entity.QShipment;
import com.logistics.tms.shipment.entity.QShipmentPlan;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.repository.support.QuerydslRepositorySupport;
import org.springframework.stereotype.Repository;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.List;

@Slf4j
@Validated
@Repository
public class QDashBoardRepository extends QuerydslRepositorySupport {
    private final JPAQueryFactory queryFactory;

    public QDashBoardRepository (final JPAQueryFactory queryFactory) {
        super(TransportInfo.class);
        this.queryFactory = queryFactory;
    }

    public List<DashBoardDTO.MisDeliveryInfoDTO> getMisDelivery(LocalDate startDate, LocalDate endDate) {
        QTransportInfo ti = QTransportInfo.transportInfo;

        return queryFactory
                .select(Projections.fields(DashBoardDTO.MisDeliveryInfoDTO.class,
                        ti.requestDate, ti.transportCode, ti.companyName, ti.dropOffArea, ti.transportStatus, ti.impossible))
                .from(ti)
                .where(
                        ti.requestDate.goe(startDate),
                        ti.requestDate.loe(endDate),
                        ti.transportType.in(TransportInfo.TransportType.하차, TransportInfo.TransportType.반품)
                )
                .orderBy(ti.requestDate.asc())
                .fetch();
    }

    public List<KpiShipmentLoadratDTO> findShipmentByDate(final LocalDate startDate,
                                                        final LocalDate endDate,
                                                        final String ptnrkey,
                                                        final String compkey) {

        QShipment mt = QShipment.shipment;
        QShipmentPlan pl = QShipmentPlan.shipmentPlan;

        List<KpiShipmentLoadratDTO> result = null;

        try {
            BooleanExpression conditions = mt.deletat.isNull()
                .and(pl.deletat.isNull())
                .and(mt.cargoty.eq(TruckLoadTypeEnum.LTL)) // LTL 만 포함
                .and(mt.manlshp.eq(false)) // 수동배차는 제외
                .and(completedDateBetween(mt.completedDate, startDate, endDate))
                .and(StringUtils.isNotEmpty(ptnrkey) ? pl.ptnrkey.eq(ptnrkey) : null)
                .and(StringUtils.isNotEmpty(compkey) ? pl.compkey.eq(compkey) : null)
            ;

            result = queryFactory
                .select(Projections.fields(KpiShipmentLoadratDTO.class,
                    mt.shpmtky.as("shpmtky"),
                    mt.vhctncd.as("vhctncd"),
                    mt.loadrat.as("loadrat"),
                    mt.completedDate.as("completedDate"),
                    pl.ptnrkey.as("ptnrkey")
                ))
                .from(mt)
                    .leftJoin(pl).on(mt.shipmentPlan.eq(pl))
                .where(conditions)
                .orderBy(mt.completedDate.asc())
                .fetch();
        } catch (Exception ex) {
            log.info("Shipment ByDate, Exception:{}", ex.toString());
        }

        return result;
    }

    private BooleanExpression completedDateBetween(
        com.querydsl.core.types.dsl.DatePath<LocalDate> completedDate,
        LocalDate startDate,
        LocalDate endDate) {
        return completedDate.between(startDate, endDate);
    }

    public List<DashBoardDTO.OetmhdPartialDTO> findOrderByDate(final LocalDate reqDate,
                                                                   final String ptnrkey,
                                                                   final String compkey) {

        QOetmhd oetmhd = QOetmhd.oetmhd;

        final Predicate[] predicates = {
                StringUtils.isNotEmpty(ptnrkey) ? oetmhd.carrkey.eq(ptnrkey) : Expressions.FALSE,
                StringUtils.isNotEmpty(compkey) ? oetmhd.compkey.eq(compkey) : Expressions.FALSE,
                oetmhd.uldrqdt.eq(reqDate)
        };

        List<DashBoardDTO.OetmhdPartialDTO> itemList = queryFactory
                .select(Projections.fields(DashBoardDTO.OetmhdPartialDTO.class,
                        oetmhd.oetmsky.as("oetmsky"),
                        oetmhd.ptnrkey.as("ptnrkey"),
                        oetmhd.loadkey.as("loadkey"),
                        oetmhd.custkey.as("custkey"),
                        oetmhd.destkey.as("destkey"),
                        oetmhd.tshitst.as("tshitst"),
                        oetmhd.trnstat.as("trnstat"),
                        oetmhd.manuvhp.as("manuvhp"),
                        oetmhd.opkcate.as("opkcate"),
                        oetmhd.opktype.as("opktype"),
                        oetmhd.onsidty.as("onsidty"),
                        oetmhd.opakqty.as("opakqty"),
                        oetmhd.cargoty.as("cargoty"),
                        oetmhd.pakwidh.as("pakwidh"),
                        oetmhd.pakdept.as("pakdept"),
                        oetmhd.pakheig.as("pakheig"),
                        oetmhd.pkrweig.as("pkrweig"),
                        oetmhd.lodrqdt.as("lodrqdt"),
                        oetmhd.lodrqtm.as("lodrqtm"),
                        oetmhd.uldrqdt.as("uldrqdt"),
                        oetmhd.uldrqtm.as("uldrqtm"),
                        oetmhd.loadArrDat.as("loendat"),
                        oetmhd.loadArrTim.as("loentim"),
                        oetmhd.unloadArrDat.as("dvendat"),
                        oetmhd.unloadArrTim.as("dventim"),

                        oetmhd.loaddat.as("loaddat"),
                        oetmhd.loadtim.as("loadtim"),
                        oetmhd.dvcmpdt.as("dvcmpdt"),
                        oetmhd.dvcmptm.as("dvcmptm"),
                        oetmhd.cucmpdt.as("dvcmpdt"),
                        oetmhd.cucmptm.as("dvcmptm"),

                        oetmhd.tmshpno.as("tmshpno"),
                        oetmhd.turgtyn.as("turgtyn"),
                        oetmhd.vhctncd.as("vhctncd")
                ))
                .from(oetmhd)
                .where(predicates)
                .fetch();

        return itemList;
    }

    public List<DashBoardDTO.OetmhdPartialDTO> findShipyardOrderByDate(final LocalDate reqDate,
                                                                   final String ptnrkey,
                                                                   final String compkey) {

        QOetmhd oetmhd = QOetmhd.oetmhd;

        final Predicate[] predicates = {
                StringUtils.isNotEmpty(ptnrkey) ? oetmhd.custkey.eq(ptnrkey) : Expressions.FALSE,
                StringUtils.isNotEmpty(compkey) ? oetmhd.compkey.eq(compkey) : Expressions.FALSE,
                oetmhd.uldrqdt.eq(reqDate)
        };

        List<DashBoardDTO.OetmhdPartialDTO> itemList = queryFactory
                .select(Projections.fields(DashBoardDTO.OetmhdPartialDTO.class,
                        oetmhd.oetmsky.as("oetmsky"),
                        oetmhd.ptnrkey.as("ptnrkey"),
                        oetmhd.loadkey.as("loadkey"),
                        oetmhd.custkey.as("custkey"),
                        oetmhd.destkey.as("destkey"),
                        oetmhd.tshitst.as("tshitst"),
                        oetmhd.trnstat.as("trnstat"),
                        oetmhd.manuvhp.as("manuvhp"),
                        oetmhd.opkcate.as("opkcate"),
                        oetmhd.opktype.as("opktype"),
                        oetmhd.onsidty.as("onsidty"),
                        oetmhd.opakqty.as("opakqty"),
                        oetmhd.cargoty.as("cargoty"),
                        oetmhd.pakwidh.as("pakwidh"),
                        oetmhd.pakdept.as("pakdept"),
                        oetmhd.pakheig.as("pakheig"),
                        oetmhd.pkrweig.as("pkrweig"),
                        oetmhd.lodrqdt.as("lodrqdt"),
                        oetmhd.lodrqtm.as("lodrqtm"),
                        oetmhd.uldrqdt.as("uldrqdt"),
                        oetmhd.uldrqtm.as("uldrqtm"),
                        oetmhd.loadArrDat.as("loendat"),
                        oetmhd.loadArrTim.as("loentim"),
                        oetmhd.unloadArrDat.as("dvendat"),
                        oetmhd.unloadArrTim.as("dventim"),

                        oetmhd.loaddat.as("loaddat"),
                        oetmhd.loadtim.as("loadtim"),
                        oetmhd.dvcmpdt.as("dvcmpdt"),
                        oetmhd.dvcmptm.as("dvcmptm"),
                        oetmhd.cucmpdt.as("dvcmpdt"),
                        oetmhd.cucmptm.as("dvcmptm"),

                        oetmhd.tmshpno.as("tmshpno"),
                        oetmhd.turgtyn.as("turgtyn"),
                        oetmhd.vhctncd.as("vhctncd")
                ))
                .from(oetmhd)
                .where(predicates)
                .fetch();

        return itemList;
    }

    public List<DashBoardDTO.MisDeliveryInfoDTO> getTransportAndWeight(LocalDate startDate, LocalDate endDate) {
        QTransportInfo ti = QTransportInfo.transportInfo;

        return queryFactory
                .select(Projections.fields(DashBoardDTO.MisDeliveryInfoDTO.class,
                        ti.requestDate, ti.transportCode, ti.companyName, ti.dropOffArea, ti.transportStatus, ti.impossible))
                .from(ti)
                .where(
                        ti.requestDate.goe(startDate),
                        ti.requestDate.loe(endDate),
                        ti.transportType.in(TransportInfo.TransportType.하차, TransportInfo.TransportType.반품)
                )
                .orderBy(ti.requestDate.asc())
                .fetch();
    }

    public List<DriverInfoDTO.DriverVehicleInfo> getVehicleWeight() {
        QDriverVehicleInfo vi = QDriverVehicleInfo.driverVehicleInfo;

        return queryFactory
                .select(Projections.fields(DriverInfoDTO.DriverVehicleInfo.class,
                        vi.vehicleId, vi.maxPayload))
                .from(vi)
                .where(vi.vehicleId.isNotNull().and(vi.maxPayload.isNotNull()))
//                .where(
//                        vi.requestDate.goe(startDate),
//                        vi.requestDate.loe(endDate),
//                        vi.transportType.in(TransportInfo.TransportType.하차, TransportInfo.TransportType.반품)
//                )
                .orderBy(vi.vehicleId.asc())
                .fetch();
    }

    public List<DashBoardDTO.OetmhdPartialDTO> findAllOrderByDate(final LocalDate reqDate,
                                                                   final String compkey) {

        QOetmhd oetmhd = QOetmhd.oetmhd;

        final Predicate[] predicates = {
                StringUtils.isNotEmpty(compkey) ? oetmhd.compkey.eq(compkey) : Expressions.FALSE,
                oetmhd.uldrqdt.eq(reqDate)
        };

        List<DashBoardDTO.OetmhdPartialDTO> itemList = queryFactory
                .select(Projections.fields(DashBoardDTO.OetmhdPartialDTO.class,
                        oetmhd.oetmsky.as("oetmsky"),
                        oetmhd.ptnrkey.as("ptnrkey"),
                        oetmhd.loadkey.as("loadkey"),
                        oetmhd.custkey.as("custkey"),
                        oetmhd.destkey.as("destkey"),
                        oetmhd.tshitst.as("tshitst"),
                        oetmhd.trnstat.as("trnstat"),
                        oetmhd.manuvhp.as("manuvhp"),
                        oetmhd.opkcate.as("opkcate"),
                        oetmhd.opktype.as("opktype"),
                        oetmhd.onsidty.as("onsidty"),
                        oetmhd.opakqty.as("opakqty"),
                        oetmhd.cargoty.as("cargoty"),
                        oetmhd.pakwidh.as("pakwidh"),
                        oetmhd.pakdept.as("pakdept"),
                        oetmhd.pakheig.as("pakheig"),
                        oetmhd.pkrweig.as("pkrweig"),
                        oetmhd.lodrqdt.as("lodrqdt"),
                        oetmhd.lodrqtm.as("lodrqtm"),
                        oetmhd.uldrqdt.as("uldrqdt"),
                        oetmhd.uldrqtm.as("uldrqtm"),
                        oetmhd.loadArrDat.as("loendat"),
                        oetmhd.loadArrTim.as("loentim"),
                        oetmhd.unloadArrDat.as("dvendat"),
                        oetmhd.unloadArrTim.as("dventim"),

                        oetmhd.loaddat.as("loaddat"),
                        oetmhd.loadtim.as("loadtim"),
                        oetmhd.dvcmpdt.as("dvcmpdt"),
                        oetmhd.dvcmptm.as("dvcmptm"),
                        oetmhd.cucmpdt.as("dvcmpdt"),
                        oetmhd.cucmptm.as("dvcmptm"),

                        oetmhd.tmshpno.as("tmshpno"),
                        oetmhd.turgtyn.as("turgtyn"),
                        oetmhd.vhctncd.as("vhctncd")
                ))
                .from(oetmhd)
                .where(predicates)
                .fetch();

        return itemList;
    }

    public List<ShipmentCustomDTO.OetmhdSimpleDTO> findByShipKeyIn(final List<String> shipment) {

        QOetmhd oetmhd = QOetmhd.oetmhd;

        final Predicate[] predicates = {
//                StringUtils.isNotEmpty(compkey) ? oetmhd.compkey.eq(compkey) : Expressions.FALSE,
//                oetmhd.uldrqdt.eq(reqDate)
        };

        List<ShipmentCustomDTO.OetmhdSimpleDTO> itemList = queryFactory
                .select(Projections.fields(ShipmentCustomDTO.OetmhdSimpleDTO.class,
                        oetmhd.oetmsky.as("oetmsky"),
                        oetmhd.ptnrkey.as("ptnrkey"),
                        oetmhd.loadkey.as("loadkey"),
                        oetmhd.custkey.as("custkey"),
                        oetmhd.destkey.as("destkey"),
                        oetmhd.tshitst.as("tshitst"),
                        oetmhd.trnstat.as("trnstat"),
                        oetmhd.manuvhp.as("manuvhp"),
                        oetmhd.opkcate.as("opkcate"),
                        oetmhd.opktype.as("opktype"),
                        oetmhd.onsidty.as("onsidty"),
                        oetmhd.opakqty.as("opakqty"),
                        oetmhd.cargoty.as("cargoty"),
                        oetmhd.pakwidh.as("pakwidh"),
                        oetmhd.pakdept.as("pakdept"),
                        oetmhd.pakheig.as("pakheig"),
                        oetmhd.pkrweig.as("pkrweig"),
                        oetmhd.lodrqdt.as("lodrqdt"),
                        oetmhd.lodrqtm.as("lodrqtm"),
                        oetmhd.uldrqdt.as("uldrqdt"),
                        oetmhd.uldrqtm.as("uldrqtm"),
                        oetmhd.loadArrDat.as("loendat"),
                        oetmhd.loadArrTim.as("loentim"),
                        oetmhd.unloadArrDat.as("dvendat"),
                        oetmhd.unloadArrTim.as("dventim"),

                        oetmhd.loaddat.as("loaddat"),
                        oetmhd.loadtim.as("loadtim"),
                        oetmhd.dvcmpdt.as("dvcmpdt"),
                        oetmhd.dvcmptm.as("dvcmptm"),
                        oetmhd.cucmpdt.as("dvcmpdt"),
                        oetmhd.cucmptm.as("dvcmptm"),

                        oetmhd.tmshpno.as("tmshpno"),
                        oetmhd.turgtyn.as("turgtyn"),
                        oetmhd.vhctncd.as("vhctncd")
                ))
                .from(oetmhd)
                .where(predicates)
                .fetch();

        return itemList;
    }

    public List<DashBoardDTO.ShipyardVehicleDTO> findShipyardOrderAndVehicleByDate(final LocalDate reqDate,
                                                final String ptnrkey,
                                                final String compkey) {

        QOetmhd oetmhd = QOetmhd.oetmhd;
        QTransportInfo tmtrinf = QTransportInfo.transportInfo;

        final Predicate[] predicates = {
                StringUtils.isNotEmpty(ptnrkey) ? oetmhd.custkey.eq(ptnrkey) : Expressions.FALSE,
                StringUtils.isNotEmpty(compkey) ? oetmhd.compkey.eq(compkey) : Expressions.FALSE,
                oetmhd.uldrqdt.eq(reqDate),
                tmtrinf.transportType.eq(TransportInfo.TransportType.하차).or(tmtrinf.transportType.eq(TransportInfo.TransportType.회수))
        };

        //BooleanExpression 을 사용해야 or 나 and 를 쓸 수 있음
//        BooleanExpression  first = tmtrinf.transportType.eq(TransportInfo.TransportType.하차);
//        BooleanExpression  second = tmtrinf.transportType.eq(TransportInfo.TransportType.회수);
//        Predicate tailPredicate = first.or(second);

        List<DashBoardDTO.ShipyardVehicleDTO> itemList = queryFactory
                .select( Projections.fields(DashBoardDTO.ShipyardVehicleDTO.class,
                        oetmhd.tmshpno.as("shpmtky"),
                        oetmhd.oetmsky.as("oetmsky"),
                        oetmhd.unloadArrDat.as("dvendat"),
                        oetmhd.unloadArrTim.as("dventim"),
                        oetmhd.dvcmpdt.as("dvcmpdt"),
                        oetmhd.dvcmptm.as("dvcmptm"),
                        oetmhd.cucmpdt.as("cucmpdt"),
                        oetmhd.cucmptm.as("cucmptm"),
                        tmtrinf.transportType.as("trtype"),
                        tmtrinf.vehicleId.as("vhclid"),
                        tmtrinf.transportStatus.as("trstat")
                ))
                .from(oetmhd)
                .innerJoin(tmtrinf).on(oetmhd.oetmsky.eq(tmtrinf.transportCode))
                .where(predicates)
                .fetch();

        return itemList;
    }

    public List<DashBoardDTO.EventDetailDTO> getEventAndVehicle(LocalDate startDate, LocalDate endDate) {
        QTransportEvent ev = QTransportEvent.transportEvent;
        QDriverVehicleInfo dr = QDriverVehicleInfo.driverVehicleInfo;
        QSusrma su = QSusrma.susrma;

        List<DashBoardDTO.EventDetailDTO> itemList = queryFactory
                .select( Projections.fields(DashBoardDTO.EventDetailDTO.class,
                        ev.shipmentKey.as("shipmentKey"),
                        ev.vehicleId.as("vehicleId"),
                        ev.transportType.as("transportType"),
                        ev.eventType.as("eventType"),
                        ev.detail.as("detail"),
                        ev.credate.as("createdDate"),
                        ev.companyName.as("companyName"),
                        su.usernam.as("userName")
                ))
                .from(ev)
                .leftJoin(dr).on(dr.vehicleId.eq(ev.vehicleId))
                .leftJoin(su).on(su.useract.eq(dr.useract))
                .where(
                        ev.credate.between(startDate, endDate)
//                        ev.credate.goe(startDate),
//                        ev.credate.loe(endDate)
                )
                .orderBy(ev.credate.asc(), ev.shipmentKey.asc())
                .fetch();
        return itemList;
    }

    public List<DashBoardDTO.OetmhdPartialDTO> findOrderByDateIn(final LocalDate startDate,
                                                                final LocalDate endDate,
                                                              final String ptnrkey,
                                                              final String compkey) {

        QOetmhd oetmhd = QOetmhd.oetmhd;

        final Predicate[] predicates = {
                StringUtils.isNotEmpty(ptnrkey) ? oetmhd.carrkey.eq(ptnrkey) : Expressions.FALSE,
                StringUtils.isNotEmpty(compkey) ? oetmhd.compkey.eq(compkey) : Expressions.FALSE,
                oetmhd.uldrqdt.between(startDate, endDate)
        };

        List<DashBoardDTO.OetmhdPartialDTO> itemList = queryFactory
                .select(Projections.fields(DashBoardDTO.OetmhdPartialDTO.class,
                        oetmhd.oetmsky.as("oetmsky"),
                        oetmhd.ptnrkey.as("ptnrkey"),
                        oetmhd.loadkey.as("loadkey"),
                        oetmhd.custkey.as("custkey"),
                        oetmhd.destkey.as("destkey"),
                        oetmhd.tshitst.as("tshitst"),
                        oetmhd.trnstat.as("trnstat"),
                        oetmhd.manuvhp.as("manuvhp"),
                        oetmhd.opkcate.as("opkcate"),
                        oetmhd.opktype.as("opktype"),
                        oetmhd.onsidty.as("onsidty"),
                        oetmhd.opakqty.as("opakqty"),
                        oetmhd.cargoty.as("cargoty"),
                        oetmhd.pakwidh.as("pakwidh"),
                        oetmhd.pakdept.as("pakdept"),
                        oetmhd.pakheig.as("pakheig"),
                        oetmhd.pkrweig.as("pkrweig"),
                        oetmhd.lodrqdt.as("lodrqdt"),
                        oetmhd.lodrqtm.as("lodrqtm"),
                        oetmhd.uldrqdt.as("uldrqdt"),
                        oetmhd.uldrqtm.as("uldrqtm"),
                        oetmhd.loadArrDat.as("loendat"),
                        oetmhd.loadArrTim.as("loentim"),
                        oetmhd.unloadArrDat.as("dvendat"),
                        oetmhd.unloadArrTim.as("dventim"),

                        oetmhd.loaddat.as("loaddat"),
                        oetmhd.loadtim.as("loadtim"),
                        oetmhd.dvcmpdt.as("dvcmpdt"),
                        oetmhd.dvcmptm.as("dvcmptm"),
                        oetmhd.cucmpdt.as("dvcmpdt"),
                        oetmhd.cucmptm.as("dvcmptm"),

                        oetmhd.tmshpno.as("tmshpno"),
                        oetmhd.turgtyn.as("turgtyn"),
                        oetmhd.vhctncd.as("vhctncd")
                ))
                .from(oetmhd)
                .where(predicates)
                .fetch();

        return itemList;
    }
}
