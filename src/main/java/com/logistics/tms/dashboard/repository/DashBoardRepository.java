package com.logistics.tms.dashboard.repository;

import com.logistics.tms.driver.dto.DriverVehicleInfoDTO;
import com.logistics.tms.transport.entity.TransportEvent;
import com.logistics.tms.transport.entity.TransportInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface DashBoardRepository  extends JpaRepository<TransportInfo, Long> {
    @Query("""
            SELECT DISTINCT i.shipmentKey
            FROM TransportInfo i
            WHERE i.requestDate = :requestDate
            AND (i.transportType = '하차' OR i.transportType = '반품')
            AND i.partnerKey = :partnerKey""")
    List<String> findDistinctShipment(LocalDate requestDate, String partnerKey);

    @Query("""
            SELECT DISTINCT i.shipmentKey
            FROM TransportInfo i
            WHERE i.requestDate = :requestDate
            AND (i.transportType = '하차' OR i.transportType = '반품')""")
    List<String> findDistinctShipmentAllPartner(LocalDate requestDate);

    @Query("""
            SELECT te
            FROM TransportEvent te
            WHERE te.shipmentKey IN :shipList""")
    List<TransportEvent> findByShipKeyIn(List<String> shipList);

    @Query("""
            SELECT SUM(s.vehcost)
            FROM Shipment s
            WHERE s.shpmtky IN :shipmentKeys""")
    int findSumVehCostByShipmentKeysIn(List<String> shipmentKeys);

    @Query("""
            SELECT ti
            FROM TransportInfo ti
            WHERE ti.requestDate = :requestDate
            AND (ti.transportType = '하차' OR ti.transportType = '회수')""")
    List<TransportInfo> findDestinationShipment(LocalDate requestDate);

}
