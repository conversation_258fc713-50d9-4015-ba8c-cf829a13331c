package com.logistics.tms.dashboard.constant;

import com.logistics.tms.transport.entity.TransportEvent;
import com.logistics.tms.transport.entity.TransportInfo;

import java.util.Arrays;
import java.util.List;

public interface DashBoardContant {
    List<TransportInfo.TransportStatus> COMPLETED_STATUS = Arrays.asList(TransportInfo.TransportStatus.상차완료,
            TransportInfo.TransportStatus.하차완료, TransportInfo.TransportStatus.회수완료,
            TransportInfo.TransportStatus.운송완료);

    List<TransportInfo.TransportStatus> ALL_COMPLETED_STATUS = Arrays.asList(
            TransportInfo.TransportStatus.하차완료, TransportInfo.TransportStatus.운송완료);

    List<TransportInfo.TransportStatus> IMPOSSIBLE_STATUS = Arrays.asList(TransportInfo.TransportStatus.상차불가,
            TransportInfo.TransportStatus.하차불가, TransportInfo.TransportStatus.입문불가,
            TransportInfo.TransportStatus.회수불가, TransportInfo.TransportStatus.반품불가);

    List<TransportInfo.TransportStatus> READY_STATUS = Arrays.asList(TransportInfo.TransportStatus.운송대기,
            TransportInfo.TransportStatus.운송시작);

    List<TransportEvent.EventType> MISDELIVERY = Arrays.asList(TransportEvent.EventType.상차물량체적불일치, TransportEvent.EventType.상하차검수불일치,
            TransportEvent.EventType.상하차주소정보불일치);
}
