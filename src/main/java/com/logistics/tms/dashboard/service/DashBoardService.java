package com.logistics.tms.dashboard.service;

import com.logistics.tms.common.enumeration.VehicleBusinessTypeEnum;
import com.logistics.tms.common.util.AuthUtils;
import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.dashboard.constant.DashBoardContant;
import com.logistics.tms.dashboard.dto.DashBoardDTO;
import com.logistics.tms.dashboard.repository.DashBoardRepository;
import com.logistics.tms.dashboard.repository.QDashBoardRepository;
import com.logistics.tms.driver.dto.DriverInfoDTO;
import com.logistics.tms.driver.service.DriverInfoCustomService;
import com.logistics.tms.realtracking.dto.RealTrackingDTO;
import com.logistics.tms.transport.dto.TransportEventDTO;
import com.logistics.tms.transport.dto.TransportInfoDTO;
import com.logistics.tms.transport.entity.TransportEvent;
import com.logistics.tms.transport.entity.TransportInfo;
import com.logistics.tms.transport.mapper.TransportEventMapper;
//import com.logistics.tms.transport.mapper.TransportInfoMapper;
import com.logistics.tms.transport.service.TransportInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
//import java.util.concurrent.ConcurrentHashMap;
//import java.util.function.Function;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.logistics.tms.shipment.constant.ShipmentConstant.SHIPMENT_DEFAULT_OWNER_CUNAMLC;

@Slf4j
@RequiredArgsConstructor
@Transactional(readOnly = true)
@Service
public class DashBoardService {
    private final DashBoardRepository dashBoardRepository;
    private final TransportInfoService transportInfoService;
    private final DriverInfoCustomService driverInfoCustomService;
    private final TransportEventMapper transportEventMapper;
    private final QDashBoardRepository qDashBoardRepository;
//    private final TransportInfoMapper transportInfoMapper;



    public DashBoardDTO.SummaryCountDTO getSummaryCount(LocalDate requestDate) {
        DashBoardDTO.SummaryCountDTO countDTO = new DashBoardDTO.SummaryCountDTO();

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        String partnerKey = Objects.requireNonNull(authDTO).getPartnerKey();
        String companyKey = Objects.requireNonNull(authDTO.getCompany());

        List<DashBoardDTO.OetmhdPartialDTO> oetmhdList = qDashBoardRepository.findOrderByDate(requestDate, partnerKey, companyKey);     // 해당날짜의 전체 운송주문 리스트
        int totalPallet = oetmhdList.stream()
                .mapToInt(DashBoardDTO.OetmhdPartialDTO::getOpakqty)
                .sum();

        // 운송번호로 팔레트 갯수를 알아오기 위한 맵 구성
        Map<String, DashBoardDTO.OetmhdPartialDTO> oetmMap = oetmhdList.stream()
                .collect(Collectors.toMap(
                        DashBoardDTO.OetmhdPartialDTO::getOetmsky,
                        Function.identity(),                 // 값 그대로 사용
                        (existing, replacement) -> existing  // 키 중복 시 기존 값 유지
                ));

        List<String> shipList = (partnerKey == null) ?
                dashBoardRepository.findDistinctShipmentAllPartner(requestDate) :
                dashBoardRepository.findDistinctShipment(requestDate, partnerKey);      // shipmentkey list 선택

        // shipment를 key로 map을 작성-> 차량별 처리 가능(하차, 반품만 선택)
        Map<String, List<TransportInfoDTO>> totalTransportList = transportInfoService.getTransportByShipmentKeyIn(shipList).stream()
                .filter(e -> e.getTransportType() == TransportInfo.TransportType.하차
                        || e.getTransportType() == TransportInfo.TransportType.반품)
                .collect(Collectors.groupingBy(TransportInfoDTO::getShipmentKey));    // tmtrinf 중에서 하차, 반품의 경우만 선택

        int readyCount = 0;
        int goingCount = 0;
        int finishedCount = 0;
        int failedCount = 0;
        int finishedPallet = 0;
        String transCode, companyName;
        TransportInfo.TransportStatus transStatus;
        int completedVehicleCount = 0, failedVehicleCount = 0, goingVehicleCount = 0, readyVehicleCount = 0, totalVehicleCount = 0;

        for(Map.Entry<String, List<TransportInfoDTO>> entry : totalTransportList.entrySet()) {

            String shipment = entry.getKey();
            List<TransportInfoDTO> transportList = entry.getValue();        // 해당 shipment에 속하는 운송리스트

            List<String> finishedList = new ArrayList<>();
            List<String> failedList = new ArrayList<>();
            List<String> readyList = new ArrayList<>();
            List<String> goingList = new ArrayList<>();

            for(TransportInfoDTO code : transportList){
                transCode = code.getTransportCode();        // 운송주문번호
                companyName = code.getCompanyName();        // 운송지명칭(한화오션,현대중공업,삼성중공업,...)
                transStatus = code.getTransportStatus();

                // 팔레트 갯수 체크를 위해 운송 당 체크
                if(DashBoardContant.COMPLETED_STATUS.contains(transStatus)){
                    if(!finishedList.contains(transCode)){
                        finishedList.add(transCode);
                        // 해당 주문의 팔레트 수 합산 필요
                        DashBoardDTO.OetmhdPartialDTO partial = oetmMap.get(transCode);
                        if(partial != null) {
                            finishedPallet += partial.getOpakqty();            // 운송 완료된 팔레트 갯수를 더한다
                        } else {
                            int a = 0;
                        }
                    }
                } else if(DashBoardContant.IMPOSSIBLE_STATUS.contains(transStatus)) {
                    if(!failedList.contains(transCode)){
                        failedList.add(transCode);
                    }
                } else if(DashBoardContant.READY_STATUS.contains(transStatus)) {
                    if(!readyList.contains(transCode)){
                        readyList.add(transCode);
                    }
                } else {
                    if(!goingList.contains(transCode)){
                        goingList.add(transCode);
                    }
                }
            }

            readyCount += readyList.size();
            goingCount += goingList.size();
            finishedCount += finishedList.size();
            failedCount += failedList.size();


            boolean allCompleted = transportList.stream()
                    .allMatch(d -> DashBoardContant.COMPLETED_STATUS.contains(d.getTransportStatus()));
            if (allCompleted) {
                completedVehicleCount++;
            } else {
                boolean hasImpossibleStatus = transportList.stream()
                        .anyMatch(d -> DashBoardContant.IMPOSSIBLE_STATUS.contains(d.getTransportStatus()));
                if (hasImpossibleStatus) {
                    failedVehicleCount++;
                } else {
                    boolean allReady = transportList.stream()
                            .allMatch(d -> DashBoardContant.READY_STATUS.contains(d.getTransportStatus()));
                    if (allReady)
                        readyVehicleCount++;
                    else
                        goingVehicleCount++;
                }
            }
            totalVehicleCount++;
        }

//        countDTO.setTotalTransport(oetmhdList.size());
//        countDTO.setCompletedTransport(finishedCount);
        countDTO.setTotalTransport(totalPallet);               // 주문된 전체 팔레트 갯수
        countDTO.setCompletedTransport(finishedPallet);        // 운송 완료된 팔레트 갯수
        countDTO.setTotalVehicle(totalVehicleCount);
        countDTO.setReadyVehicle(readyVehicleCount);
        countDTO.setGoingVehicle(goingVehicleCount);
        countDTO.setFinishedVehicle(completedVehicleCount);
        countDTO.setFailedVehicle(failedVehicleCount);

        return countDTO;
    }

    public List<DashBoardDTO.VehicleDTO> getVehicle(LocalDate requestDate) {
        List<DashBoardDTO.VehicleDTO> countList = new ArrayList<>();

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        String partnerKey = Objects.requireNonNull(authDTO).getPartnerKey();
        String companyKey = Objects.requireNonNull(authDTO.getCompany());

        List<String> shipList = (partnerKey == null) ?
                dashBoardRepository.findDistinctShipmentAllPartner(requestDate) :
                dashBoardRepository.findDistinctShipment(requestDate, partnerKey);      // shipmentkey list 선택, tmtrinf

        List<DriverInfoDTO.DriverVehicleInfo> vehicleInfoList = driverInfoCustomService.findDriverVehicleInfoByPartnerKey(Collections.singletonList(partnerKey));
        List<Long> personalVehicleIds = vehicleInfoList.stream()
                .filter(info -> info.getVehicleBusinessType() == VehicleBusinessTypeEnum.용차)
                .map(DriverInfoDTO.DriverVehicleInfo::getVehicleId)
                .toList();                                                              // 용차의 vehicleid list 선택

        // shipment를 key로 map을 작성-> 차량별 처리 가능
        Map<String, List<TransportInfoDTO>> totalTransportList = transportInfoService.getTransportByShipmentKeyIn(shipList).stream()
                .collect(Collectors.groupingBy(TransportInfoDTO::getShipmentKey));    // tmtrinf 중에서 하차, 반품의 경우만 선택

        int round = 0;
        String transCode, companyName;
        TransportInfo.TransportStatus transStatus;
        List<String> transCodeList = new ArrayList<>();
        int[] mixedTrans = {0, 0, 0};
        int[] singleTrans = {0, 0, 0};
        int[] deposit = {0, 0, 0};
        int[] rental = {0, 0, 0};
        int[] depositFinish = {0, 0, 0};
        int[] rentalFinish = {0, 0, 0};

        for(Map.Entry<String, List<TransportInfoDTO>> entry : totalTransportList.entrySet()) {

            String shipment = entry.getKey();
            List<TransportInfoDTO> transportList = entry.getValue();

            round = transportList.get(0).getTransportRound();   // 하나의 shipment내의 round는 동일

            if (transportList.size() == 2)
                singleTrans[round-1]++;
            else
                mixedTrans[round-1]++;

            // 지입과 용차의 운송완료 카운트
            boolean isRental = personalVehicleIds.contains(transportList.get(0).getVehicleId());
            boolean allCompleted = transportList.stream()
                    .allMatch(d -> DashBoardContant.COMPLETED_STATUS.contains(d.getTransportStatus()));
            if (isRental) {
                if(allCompleted) {
                    rentalFinish[round-1]++;
                }
                rental[round - 1]++;
            }
            else {
                if(allCompleted) {
                    depositFinish[round-1]++;
                }
                deposit[round - 1]++;
            }
        }

        for(int i = 0; i <3; i++) {
            DashBoardDTO.VehicleDTO dto = DashBoardDTO.VehicleDTO.builder()
                    .round(i + 1)
                    .depositVehicle(deposit[i])
                    .rentalVehicle(rental[i])
                    .depositFinished(depositFinish[i])
                    .rentalFinished(rentalFinish[i])
                    .mixedShipment(mixedTrans[i])
                    .singleShipment(singleTrans[i])
                    .build();
            countList.add(dto);
        }

        return countList;
    }

    public List<DashBoardDTO.ShipyardDTO> getShipyard(LocalDate requestDate) {

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        String partnerKey = Objects.requireNonNull(authDTO).getPartnerKey();
        String companyKey = Objects.requireNonNull(authDTO.getCompany());

        List<String> shipList = (partnerKey == null) ?
                dashBoardRepository.findDistinctShipmentAllPartner(requestDate) :
                dashBoardRepository.findDistinctShipment(requestDate, partnerKey);      // shipmentkey list 선택

        // shipment를 key로 map을 작성-> 차량별 처리 가능
        Map<String, List<TransportInfoDTO>> totalTransportList = transportInfoService.getTransportByShipmentKeyIn(shipList).stream()
                .collect(Collectors.groupingBy(TransportInfoDTO::getShipmentKey));    // tmtrinf 중에서 하차, 반품의 경우만 선택

        String transCode, companyName, dropoffArea;
        int hyundaiTransporting =0 , hyundaiFailed = 0, hyundaiFinished = 0, hyundaiReady = 0;
        int samsungTransporting =0 , samsungFailed = 0, samsungFinished = 0, samsungReady = 0;
        int hanhwaTransporting =0 , hanhwaFailed = 0, hanhwaFinished = 0, hanhwaReady = 0;
        int etcTransporting = 0, etcFailed = 0, etcFinished = 0, etcReady = 0;

        for(Map.Entry<String, List<TransportInfoDTO>> entry : totalTransportList.entrySet()) {
            String shipment = entry.getKey();
            List<TransportInfoDTO> transportList = entry.getValue();

            List<TransportInfoDTO> hyundaiList = new ArrayList<>();
            List<TransportInfoDTO> samsungList = new ArrayList<>();
            List<TransportInfoDTO> hanhwaList = new ArrayList<>();
            List<TransportInfoDTO> etcList = new ArrayList<>();

            // 원래 코드. 비교를 위해 막음
//            // 조선소로의 배송만 묶는다. 상차+하차의 쌍으로 리스트 구성함
//            for(TransportInfoDTO code : transportList){
//                companyName = code.getCompanyName();        // 운송지명칭(한화오션,현대중공업,삼성중공업,...)
//                dropoffArea = code.getDropOffArea();
//
//                if(Objects.isNull(dropoffArea)) {           // 조선사가 하차지가 아닐때
//                    continue;
//                }
//
//                if(dropoffArea == null || dropoffArea.isEmpty() || dropoffArea.isBlank()) {
//                    continue;
//                }
//                // 주문번호는 같고 운송순서가 다른 항목 찾기. 즉 상차는 하차, 하차는 상차를 찾는다.
//                Optional<TransportInfoDTO> optionalDto = transportList.stream()
//                        .filter(dto -> dto.getTransportCode().equals(code.getTransportCode()))
//                        .filter(dto -> dto.getTransportOrder() != code.getTransportOrder())
//                        .findFirst();
//
//                TransportInfoDTO dtoSelect = optionalDto.get();
//
//                // 상,하차,상,하차 순으로 순서 정렬
//                if(companyName.contains("현대중공업")){
//                    if(code.getTransportOrder() < dtoSelect.getTransportOrder()) {
//                        hyundaiList.add(code);
//                        hyundaiList.add(dtoSelect);
//                    } else {
//                        hyundaiList.add(dtoSelect);
//                        hyundaiList.add(code);
//                    }
//                } else if(companyName.contains("삼성중공업")){
//                    if(code.getTransportOrder() < dtoSelect.getTransportOrder()) {
//                        samsungList.add(code);
//                        samsungList.add(dtoSelect);
//                    } else {
//                        samsungList.add(dtoSelect);
//                        samsungList.add(code);
//                    }
//                } else if(companyName.contains("한화오션")){
//                    if(code.getTransportOrder() < dtoSelect.getTransportOrder()) {
//                        hanhwaList.add(code);
//                        hanhwaList.add(dtoSelect);
//                    } else {
//                        hanhwaList.add(dtoSelect);
//                        hanhwaList.add(code);
//                    }
//                }
//            }

            /// //////////////////////////////
            // 리스트를 주문번호를 키로 하는 맵으로 변환
            Map<String, List<TransportInfoDTO>> totalMap = transportList.stream()
                    .sorted(Comparator.comparing(TransportInfoDTO::getTransportCode).thenComparing(TransportInfoDTO::getTransportOrder))
                    .collect(Collectors.groupingBy(TransportInfoDTO::getTransportCode));
            // 맵을 돌면서 조선소별 리스트 작성
//            List<TransportInfoDTO> hyList = new ArrayList<>();
//            List<TransportInfoDTO> saList = new ArrayList<>();
//            List<TransportInfoDTO> haList = new ArrayList<>();
//            List<TransportInfoDTO> etList = new ArrayList<>();
            for(Map.Entry<String, List<TransportInfoDTO>> ent : totalMap.entrySet() ) {
                transCode = ent.getKey();
                List<TransportInfoDTO> tList = ent.getValue();      // 하나의 주문번호에 대한 상,하차의 쌍
                boolean isHyMatched = false;
                boolean isSaMatched = false;
                boolean isHaMatched = false;
                for(TransportInfoDTO l : tList) {
                    if(Objects.nonNull(l.getDropOffArea())) {
                        isHyMatched = l.getCompanyName().contains("현대중공업");
                        isSaMatched = l.getCompanyName().contains("삼성중공업");
                        isHaMatched = l.getCompanyName().contains("한화오션");
                    }

                    if(isHyMatched) {
                        hyundaiList.add(tList.get(0));
                        hyundaiList.add(tList.get(1));
                        break;
                    } else if(isSaMatched) {
                        samsungList.add(tList.get(0));
                        samsungList.add(tList.get(1));
                        break;
                    } else if(isHaMatched) {
                        hanhwaList.add(tList.get(0));
                        hanhwaList.add(tList.get(1));
                        break;
                    }
                }
                if(!isHyMatched && !isSaMatched && !isHaMatched) {
                    etcList.add(tList.get(0));
                    etcList.add(tList.get(1));
                }
            }

            // 원래 코드. 비교를 위해 막음
//            // 조선소 이외의 배송을 묶는다. 조선소로의 상차 정보도 포함(주문번호 중복가능)
//            for(TransportInfoDTO code : transportList){
//                transCode = code.getTransportCode();
//                companyName = code.getCompanyName();        // 운송지명칭(한화오션,현대중공업,삼성중공업,...)
//
//                // 상하차 두번 들어가니 한번만 연산하게
//                if(etcList.contains(code)) {
//                    continue;
//                }
//
//                if(!hyundaiList.contains(code) && !hanhwaList.contains(code) && !samsungList.contains(code) ){
//                    Optional<TransportInfoDTO> optionalDto = transportList.stream()
//                            .filter(dto -> dto.getTransportCode().equals(code.getTransportCode()))
//                            .filter(dto -> dto.getTransportOrder() != code.getTransportOrder())
//                            .findFirst();
//
//                    TransportInfoDTO dtoSelect = optionalDto.get();
//
//                    if(code.getTransportOrder() < dtoSelect.getTransportOrder()) {
//                        etcList.add(code);
//                        etcList.add(dtoSelect);
//                    } else {
//                        etcList.add(dtoSelect);
//                        etcList.add(code);
//                    }
//                }
//            }

            // 현대중공업
            for(int i = 0 ; i < hyundaiList.size(); i++){
                TransportInfoDTO code = hyundaiList.get(i);
                if(DashBoardContant.IMPOSSIBLE_STATUS.contains(code.getTransportStatus())) {
                    hyundaiFailed++;
                    if(i % 2 == 0) {
                        i++;
                    }
                } else if(DashBoardContant.COMPLETED_STATUS.contains(code.getTransportStatus())) {
                    if(i % 2 == 0) {            // 상차가 완료일때 하차도 완료여야 성공
                        i++;
                        code = hyundaiList.get(i);
                        if(DashBoardContant.COMPLETED_STATUS.contains(code.getTransportStatus())) {
                            hyundaiFinished++;
                        } else if(DashBoardContant.IMPOSSIBLE_STATUS.contains(code.getTransportStatus())) {
                            hyundaiFailed++;
                        }
                    }
                }
                else if(DashBoardContant.READY_STATUS.contains(code.getTransportStatus())) {
                    if(i % 2 == 0) {            // 대기는 상차에만 있음
                        hyundaiReady++;
                        i++;
                    }
                }
                else {
                    hyundaiTransporting++;
                    if(i % 2 == 0) {
                        i++;
                    }
                }
            }

            // 삼성중공업
            for(int i = 0 ; i < samsungList.size(); i++){
                TransportInfoDTO code = samsungList.get(i);
                if(DashBoardContant.IMPOSSIBLE_STATUS.contains(code.getTransportStatus())) {
                    samsungFailed++;
                    if(i % 2 == 0) {
                        i++;
                    }
                } else if(DashBoardContant.COMPLETED_STATUS.contains(code.getTransportStatus())) {
                    if(i % 2 == 0) {            // 상차가 완료일때 하차도 완료여야 성공
                        i++;
                        code = samsungList.get(i);
                        if(DashBoardContant.COMPLETED_STATUS.contains(code.getTransportStatus())) {
                            samsungFinished++;
                        } else if(DashBoardContant.IMPOSSIBLE_STATUS.contains(code.getTransportStatus())) {
                            samsungFailed++;
                        }
                    }
                }
                else if(DashBoardContant.READY_STATUS.contains(code.getTransportStatus())) {
                    if(i % 2 == 0) {            // 대기는 상차에만 있음
                        samsungReady++;
                        i++;
                    }
                }
                else {
                    samsungTransporting++;
                    if(i % 2 == 0) {
                        i++;
                    }
                }
            }

            // 한화오션
            for(int i = 0 ; i < hanhwaList.size(); i++){
                TransportInfoDTO code = hanhwaList.get(i);
                if(DashBoardContant.IMPOSSIBLE_STATUS.contains(code.getTransportStatus())) {
                    hanhwaFailed++;
                    if(i % 2 == 0) {
                        i++;
                    }
                } else if(DashBoardContant.COMPLETED_STATUS.contains(code.getTransportStatus())) {
                    if(i % 2 == 0) {            // 상차가 완료일때 하차도 완료여야 성공
                        i++;
                        code = hanhwaList.get(i);
                        if(DashBoardContant.COMPLETED_STATUS.contains(code.getTransportStatus())) {
                            hanhwaFinished++;
                        } else if(DashBoardContant.IMPOSSIBLE_STATUS.contains(code.getTransportStatus())) {
                            hanhwaFailed++;
                        }
                    }
                }
                else if(DashBoardContant.READY_STATUS.contains(code.getTransportStatus())) {
                    if(i % 2 == 0) {            // 대기는 상차에만 있음
                        hanhwaReady++;
                        i++;
                    }
                }
                else {
                    hanhwaTransporting++;
                    if(i % 2 == 0) {
                        i++;
                    }
                }
            }

            // 기타
            for(int i = 0 ; i < etcList.size(); i++){
                TransportInfoDTO code = etcList.get(i);
                if(DashBoardContant.IMPOSSIBLE_STATUS.contains(code.getTransportStatus())) {
                    etcFailed++;
                    if(i % 2 == 0) {
                        i++;
                    }
                } else if(DashBoardContant.COMPLETED_STATUS.contains(code.getTransportStatus())) {
                    if(i % 2 == 0) {            // 상차가 완료일때 하차도 완료여야 성공
                        i++;
                        code = etcList.get(i);
                        if(DashBoardContant.COMPLETED_STATUS.contains(code.getTransportStatus())) {
                            etcFinished++;
                        } else if(DashBoardContant.IMPOSSIBLE_STATUS.contains(code.getTransportStatus())) {
                            etcFailed++;
                        }
                    }
                }
                else if(DashBoardContant.READY_STATUS.contains(code.getTransportStatus())) {
                    if(i % 2 == 0) {
                        etcReady++;
                        i++;
                    }
                }
                else {
                    etcTransporting++;
                    if(i % 2 == 0) {
                        i++;
                    }
                }
            }

            // 원래 코드. 비교를 위해 막음
//            // 현대중공업
//            for(int i = 0 ; i < hyundaiList.size(); i++){
//                TransportInfoDTO code = hyundaiList.get(i);
//                if(DashBoardContant.IMPOSSIBLE_STATUS.contains(code.getTransportStatus())) {
//                    hyundaiFailed++;
//                    if(i % 2 == 0) {
//                        i++;
//                    }
//                } else if(DashBoardContant.COMPLETED_STATUS.contains(code.getTransportStatus())) {
//                    if(i % 2 == 0) {            // 상차가 완료일때 하차도 완료여야 성공
//                        i++;
//                        code = hyundaiList.get(i);
//                        if(DashBoardContant.COMPLETED_STATUS.contains(code.getTransportStatus())) {
//                            hyundaiFinished++;
//                        } else if(DashBoardContant.IMPOSSIBLE_STATUS.contains(code.getTransportStatus())) {
//                            hyundaiFailed++;
//                        }
//                    }
//                }
//                else if(DashBoardContant.READY_STATUS.contains(code.getTransportStatus())) {
//                    if(i % 2 == 0) {            // 대기는 상차에만 있음
//                        hyundaiReady++;
//                        i++;
//                    }
//                }
//                else {
//                    hyundaiTransporting++;
//                    if(i % 2 == 0) {
//                        i++;
//                    }
//                }
//            }
//
//            // 삼성중공업
//            for(int i = 0 ; i < samsungList.size(); i++){
//                TransportInfoDTO code = samsungList.get(i);
//                if(DashBoardContant.IMPOSSIBLE_STATUS.contains(code.getTransportStatus())) {
//                    samsungFailed++;
//                    if(i % 2 == 0) {
//                        i++;
//                    }
//                } else if(DashBoardContant.COMPLETED_STATUS.contains(code.getTransportStatus())) {
//                    if(i % 2 == 0) {            // 상차가 완료일때 하차도 완료여야 성공
//                        i++;
//                        code = samsungList.get(i);
//                        if(DashBoardContant.COMPLETED_STATUS.contains(code.getTransportStatus())) {
//                            samsungFinished++;
//                        } else if(DashBoardContant.IMPOSSIBLE_STATUS.contains(code.getTransportStatus())) {
//                            samsungFailed++;
//                        }
//                    }
//                }
//                else if(DashBoardContant.READY_STATUS.contains(code.getTransportStatus())) {
//                    if(i % 2 == 0) {            // 대기는 상차에만 있음
//                        samsungReady++;
//                        i++;
//                    }
//                }
//                else {
//                    samsungTransporting++;
//                    if(i % 2 == 0) {
//                        i++;
//                    }
//                }
//            }
//
//            // 한화오션
//            for(int i = 0 ; i < hanhwaList.size(); i++){
//                TransportInfoDTO code = hanhwaList.get(i);
//                if(DashBoardContant.IMPOSSIBLE_STATUS.contains(code.getTransportStatus())) {
//                    hanhwaFailed++;
//                    if(i % 2 == 0) {
//                        i++;
//                    }
//                } else if(DashBoardContant.COMPLETED_STATUS.contains(code.getTransportStatus())) {
//                    if(i % 2 == 0) {            // 상차가 완료일때 하차도 완료여야 성공
//                        i++;
//                        code = hanhwaList.get(i);
//                        if(DashBoardContant.COMPLETED_STATUS.contains(code.getTransportStatus())) {
//                            hanhwaFinished++;
//                        } else if(DashBoardContant.IMPOSSIBLE_STATUS.contains(code.getTransportStatus())) {
//                            hanhwaFailed++;
//                        }
//                    }
//                }
//                else if(DashBoardContant.READY_STATUS.contains(code.getTransportStatus())) {
//                    if(i % 2 == 0) {            // 대기는 상차에만 있음
//                        hanhwaReady++;
//                        i++;
//                    }
//                }
//                else {
//                    hanhwaTransporting++;
//                    if(i % 2 == 0) {
//                        i++;
//                    }
//                }
//            }
//
//            // 기타
//            for(int i = 0 ; i < etcList.size(); i++){
//                TransportInfoDTO code = etcList.get(i);
//                if(DashBoardContant.IMPOSSIBLE_STATUS.contains(code.getTransportStatus())) {
//                    etcFailed++;
//                    if(i % 2 == 0) {
//                        i++;
//                    }
//                } else if(DashBoardContant.COMPLETED_STATUS.contains(code.getTransportStatus())) {
//                    if(i % 2 == 0) {            // 상차가 완료일때 하차도 완료여야 성공
//                        i++;
//                        code = etcList.get(i);
//                        if(DashBoardContant.COMPLETED_STATUS.contains(code.getTransportStatus())) {
//                            etcFinished++;
//                        } else if(DashBoardContant.IMPOSSIBLE_STATUS.contains(code.getTransportStatus())) {
//                            etcFailed++;
//                        }
//                    }
//                }
//                else if(DashBoardContant.READY_STATUS.contains(code.getTransportStatus())) {
//                    if(i % 2 == 0) {
//                        etcReady++;
//                        i++;
//                    }
//                }
//                else {
//                    etcTransporting++;
//                    if(i % 2 == 0) {
//                        i++;
//                    }
//                }
//            }

        }

        List<DashBoardDTO.ShipyardDTO> returnList = new ArrayList<>();

        DashBoardDTO.ShipyardDTO hyundaiDTO = DashBoardDTO.ShipyardDTO.builder()
                .readyCount(hyundaiReady)
                .completeCount(hyundaiFinished)
                .nameShipyard("현대중공업")
                .failedCount(hyundaiFailed)
                .transportCount(hyundaiTransporting)
                .build();
        returnList.add(hyundaiDTO);

        DashBoardDTO.ShipyardDTO samsungDTO = DashBoardDTO.ShipyardDTO.builder()
                .readyCount(samsungReady)
                .completeCount(samsungFinished)
                .nameShipyard("삼성중공업")
                .failedCount(samsungFailed)
                .transportCount(samsungTransporting)
                .build();
        returnList.add(samsungDTO);

        DashBoardDTO.ShipyardDTO hanhwaDTO = DashBoardDTO.ShipyardDTO.builder()
                .readyCount(hanhwaReady)
                .completeCount(hanhwaFinished)
                .nameShipyard("한화오션")
                .failedCount(hanhwaFailed)
                .transportCount(hanhwaTransporting)
                .build();
        returnList.add(hanhwaDTO);

        DashBoardDTO.ShipyardDTO etcDTO = DashBoardDTO.ShipyardDTO.builder()
                .readyCount(etcReady)
                .completeCount(etcFinished)
                .nameShipyard("기타")
                .failedCount(etcFailed)
                .transportCount(etcTransporting)
                .build();
        returnList.add(etcDTO);

        return returnList;
    }

    public List<DashBoardDTO.PlanDTO> getPlan(LocalDate fromDate, LocalDate toDate) {
        List<DashBoardDTO.PlanDTO> newDtoList = new ArrayList<>();

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        String partnerKey = Objects.requireNonNull(authDTO).getPartnerKey();
        String companyKey = Objects.requireNonNull(authDTO.getCompany());

        LocalDate requestDate = fromDate;
        int index = 0;

        Map<LocalDate, List<DashBoardDTO.OetmhdPartialDTO>> totalOetmhdList = qDashBoardRepository.findOrderByDateIn(fromDate, toDate, partnerKey, companyKey).stream()
                .collect(Collectors.groupingBy(DashBoardDTO.OetmhdPartialDTO::getUldrqdt));
        
        while (!requestDate.isAfter(toDate)) {
            List<DashBoardDTO.PlanDTO> notPlanDtoList = new ArrayList<>();
            List<DashBoardDTO.PlanDTO> notAllocateDtoList = new ArrayList<>();
            List<DashBoardDTO.PlanDTO> notPlanDtoOrderList;
            List<DashBoardDTO.PlanDTO> notAllocateDtoOrderList;

            List<DashBoardDTO.OetmhdPartialDTO> oetmhdList = totalOetmhdList.get(requestDate);     // 해당날짜의 전체 운송주문 리스트. oetmhd
            if(oetmhdList == null) {
                requestDate = requestDate.plusDays(1);
                index++;
                continue;
            }
            // 운송계획이 없는 주문
            List<DashBoardDTO.OetmhdPartialDTO> notPlanedList = oetmhdList.stream()
                    .filter(oetm -> oetm.getTmshpno().length() < 3)
                    .toList();
            // 배차가 안된 주문
            List<DashBoardDTO.OetmhdPartialDTO> notAllocatedList = oetmhdList.stream()
                    .filter(oetm -> oetm.getVhctncd().length() < 3)
                    .toList();

            for(DashBoardDTO.OetmhdPartialDTO dto: notPlanedList) {
                int round = 0;
                LocalDate pickupDate = dto.getLodrqdt();
                LocalTime pickupTime = dto.getLodrqtm();
                LocalTime refTime = LocalTime.of(12, 0, 0);
//                if(requestDate.isAfter(pickupDate)) {               // 1round
                if(!requestDate.isEqual(pickupDate)) {               // 1round
                    round = 1;
                } else {
                    if(pickupTime.isBefore(refTime)) {
                        round = 2;
                    }
                    else {
                        round = 3;
                    }
                }
                String stringIndex = String.format("D-%d", index);
                DashBoardDTO.PlanDTO notPlanDto = DashBoardDTO.PlanDTO.builder()
                        .index(stringIndex)
                        .nameList("미지정 리스트")
                        .dateTransport(dto.getUldrqdt().toString())
                        .roundTransport(round)
                        .numberOrder(dto.getOetmsky())
                        .build();

                notPlanDtoList.add(notPlanDto);
            }

            for(DashBoardDTO.OetmhdPartialDTO dto: notAllocatedList) {
                int round = 0;
                LocalDate pickupDate = dto.getLodrqdt();
                LocalTime pickupTime = dto.getLodrqtm();
                LocalTime refTime = LocalTime.of(12, 0, 0);
                if(requestDate.isAfter(pickupDate)) {               // 1round
                    round = 1;
                } else {
                    if(pickupTime.isAfter(refTime)) {
                        round = 3;
                    }
                    else {
                        round = 2;
                    }
                }

                String stringIndex = String.format("D-%d", index);
                DashBoardDTO.PlanDTO notAllocatDto = DashBoardDTO.PlanDTO.builder()
                        .index(stringIndex)
                        .nameList("미배차 리스트")
                        .dateTransport(dto.getUldrqdt().toString())
                        .roundTransport(round)
                        .numberOrder(dto.getOetmsky())
                        .build();

                notAllocateDtoList.add(notAllocatDto);
            }

            // round로 소팅
            notPlanDtoOrderList = notPlanDtoList.stream()
                    .sorted(Comparator.comparing(DashBoardDTO.PlanDTO::getRoundTransport))
                    .toList();
            notAllocateDtoOrderList = notAllocateDtoList.stream()
                    .sorted(Comparator.comparing(DashBoardDTO.PlanDTO::getRoundTransport))
                    .toList();

            newDtoList.addAll(notPlanDtoOrderList);
            newDtoList.addAll(notAllocateDtoOrderList);

            requestDate = requestDate.plusDays(1);
            index++;
        }

        return newDtoList;
    }

    private boolean isRegularPickup(DashBoardDTO.OetmhdPartialDTO oetm) {
        boolean loenDateMatch = false;
        if (oetm.getLoendat() != null) {
            loenDateMatch = oetm.getLodrqdt().isEqual(oetm.getLoendat())
                    && !oetm.getLodrqtm().isBefore(oetm.getLoentim());
        }

        boolean loadDateMatch = false;
        if (oetm.getLoaddat() != null) {
            loadDateMatch = oetm.getLodrqdt().isEqual(oetm.getLoaddat())
                    && !oetm.getLodrqtm().isBefore(oetm.getLoadtim());
        }

        return loenDateMatch || loadDateMatch;
    }

    private boolean isRegularDropoff(DashBoardDTO.OetmhdPartialDTO oetm) {
        boolean dvenMatch = false;
        if (oetm.getDvendat() != null) {
            dvenMatch = oetm.getUldrqdt().isEqual(oetm.getDvendat())
                    && !oetm.getUldrqtm().isBefore(oetm.getDventim());
        }

        boolean dvcmpMatch = false;
        if (oetm.getDvcmpdt() != null) {
            dvcmpMatch = oetm.getUldrqdt().isEqual(oetm.getDvcmpdt())
                    && !oetm.getUldrqtm().isBefore(oetm.getDvcmptm());
        }

        return dvenMatch || dvcmpMatch;
    }

    public List<DashBoardDTO.ArrivalDTO> getArrival(LocalDate fromDate, LocalDate toDate) {
        List<DashBoardDTO.ArrivalDTO> newDtoList = new ArrayList<>();

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        String partnerKey = Objects.requireNonNull(authDTO).getPartnerKey();
        String companyKey = Objects.requireNonNull(authDTO.getCompany());

        Map<LocalDate, List<DashBoardDTO.OetmhdPartialDTO>> totalOetmhdList = qDashBoardRepository.findOrderByDateIn(fromDate, toDate, partnerKey, companyKey).stream()
                .collect(Collectors.groupingBy(DashBoardDTO.OetmhdPartialDTO::getUldrqdt));

        LocalDate requestDate = fromDate;
        while (!requestDate.isAfter(toDate)) {
            int regularArrival = 0;
            int delayedArrival = 0;

            List<DashBoardDTO.OetmhdPartialDTO> oetmhdList = totalOetmhdList.get(requestDate);     // 해당날짜의 전체 운송주문 리스트
            if(oetmhdList == null) {
                requestDate = requestDate.plusDays(1);
                continue;
            }

            // 배차가 된 주문만 선택
            List<DashBoardDTO.OetmhdPartialDTO> allocatedList = oetmhdList.stream()
                    .filter(oetm -> oetm.getVhctncd().length() > 2)
                    .toList();

            // 정시 상차가 된 경우
            List<String> regularPickupList = allocatedList.stream()
//                    .filter(oetm -> oetm.getLoendat() != null ? oetm.getLodrqdt().isEqual(oetm.getLoendat()) && !oetm.getLodrqtm().isBefore(oetm.getLoentim()) : false
                    .filter(this::isRegularPickup)
                    .map(oetm -> oetm.getOetmsky())
                    .toList();
            // 정시 하차가 된 경우
            List<String> regularDropoffList = allocatedList.stream()
//                    .filter(oetm -> oetm.getDvendat() != null ? oetm.getUldrqdt().isEqual(oetm.getDvendat()) && !oetm.getUldrqtm().isBefore(oetm.getDventim()) : false
//                        || oetm.getDvcmpdt() != null ? oetm.getUldrqdt().isEqual(oetm.getDvcmpdt()) && !oetm.getUldrqtm().isBefore(oetm.getDvcmptm()) : false)
                    .filter(this::isRegularDropoff)
                    .map(oetm -> oetm.getOetmsky())
                    .toList();

            // 상하차 모두 전시 도착인 레코드 선택
            for(String dto : regularDropoffList) {
                if(regularPickupList.contains(dto)) {
                    regularArrival++;
                }
            }
            delayedArrival = allocatedList.size() - regularArrival;

            DashBoardDTO.ArrivalDTO dto = DashBoardDTO.ArrivalDTO.builder()
                    .dateArrival(requestDate.toString())
                    .delayedArrival(delayedArrival)
                    .regularArrival(regularArrival)
                    .build();

            newDtoList.add(dto);

            requestDate = requestDate.plusDays(1);
        }

        return newDtoList;
    }


//    public List<DashBoardDTO.SummaryDTO> getSummary(LocalDate requestDate) {
//        List<DashBoardDTO.SummaryDTO> summaryDTOS = new ArrayList<>();
//
//        return summaryDTOS;
//    }

    public List<DashBoardDTO.CostDTO> getCost(LocalDate fromDate, LocalDate toDate) {
        List<DashBoardDTO.CostDTO> costDTOS = new ArrayList<>();

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        String partnerKey = Objects.requireNonNull(authDTO).getPartnerKey();
        LocalDate requestDate = fromDate;
        while (!requestDate.isAfter(toDate)) {
            List<String> shipList = (partnerKey == null) ?
                    dashBoardRepository.findDistinctShipmentAllPartner(requestDate) :
                    dashBoardRepository.findDistinctShipment(requestDate, partnerKey);

            int sumRentalCharge = (shipList.isEmpty()) ? 0 : dashBoardRepository.findSumVehCostByShipmentKeysIn(shipList);

            DashBoardDTO.CostDTO costDTO = new DashBoardDTO.CostDTO(requestDate, sumRentalCharge);
            costDTOS.add(costDTO);

            requestDate = requestDate.plusDays(1);
        }
        return costDTOS;
    }

    public List<DashBoardDTO.IssueDTO> getIssueToDelete(LocalDate fromDate, LocalDate toDate) {
        List<DashBoardDTO.IssueDTO> issueDTOS = new ArrayList<>();

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        String partnerKey = Objects.requireNonNull(authDTO).getPartnerKey();
        LocalDate requestDate = fromDate;
        while (!requestDate.isAfter(toDate)) {
            List<String> shipList = (partnerKey == null) ?
                    dashBoardRepository.findDistinctShipmentAllPartner(requestDate) :
                    dashBoardRepository.findDistinctShipment(requestDate, partnerKey);

            List<TransportEvent> eventEntities = dashBoardRepository.findByShipKeyIn(shipList);
            List<TransportEventDTO> eventDTOS = transportEventMapper.toDtoList(eventEntities);
            Map<String, List<TransportEventDTO>> groupedEvents = eventDTOS.stream()
                    .collect(Collectors.groupingBy(TransportEventDTO::getShipmentKey));
            for (Map.Entry<String, List<TransportEventDTO>> entry : groupedEvents.entrySet()) {
                String shipmentKey = entry.getKey();
                List<TransportEventDTO> events = entry.getValue();
                List<RealTrackingDTO.TrackingEventDTO> eventList = new ArrayList<>();
                for (TransportEventDTO event : events) {
                    RealTrackingDTO.TrackingEventDTO eventDTO = new RealTrackingDTO.TrackingEventDTO(event.getEventType(), event.getDetail());
                    eventList.add(eventDTO);
                }

                DashBoardDTO.IssueDTO issueDTO = new DashBoardDTO.IssueDTO(requestDate, shipmentKey, eventList);
                issueDTOS.add(issueDTO);
            }

            requestDate = requestDate.plusDays(1);
        }

        return issueDTOS;
    }

    public List<DashBoardDTO.MisDeliveryDTO> getMisDelivery(LocalDate startDate, LocalDate endDate) {
        List<DashBoardDTO.MisDeliveryDTO> misDeliveryDTOList = new ArrayList<>();

        List<DashBoardDTO.MisDeliveryInfoDTO> deliveryInfoDTOList = qDashBoardRepository.getMisDelivery(startDate, endDate);
        for (DashBoardDTO.MisDeliveryInfoDTO info : deliveryInfoDTOList) {
            DashBoardDTO.MisDeliveryDTO misDeliveryDTO = new DashBoardDTO.MisDeliveryDTO();
            misDeliveryDTO.setRequestDate(info.getRequestDate());
            misDeliveryDTO.setTransportCode(info.getTransportCode());
            misDeliveryDTO.setCompanyName(Objects.nonNull(info.getDropOffArea()) ? info.getCompanyName() : SHIPMENT_DEFAULT_OWNER_CUNAMLC);
            misDeliveryDTO.setMisDelivery(((info.getTransportStatus() == TransportInfo.TransportStatus.하차불가) || (info.getTransportStatus() == TransportInfo.TransportStatus.반품불가))
                    && Objects.nonNull(info.getImpossible()));
            misDeliveryDTOList.add(misDeliveryDTO);
        }
        return misDeliveryDTOList;
    }

    public List<?> getShipmentLoadartBs(LocalDate startDate, LocalDate endDate) {
        try {
            final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
            final String compkey = Objects.requireNonNull(authDTO).getCompany();
            final String ptnrkey = Objects.requireNonNull(authDTO).getPartnerKey();

            return qDashBoardRepository.findShipmentByDate(startDate, endDate, ptnrkey, compkey);
        } catch (Exception ex) {
            log.info("Exception: {}", ex.toString());
        }
        return null;
    }

//    public static <T> java.util.function.Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
//        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
//        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
//    }

    public DashBoardDTO.ShipyarStatusDTO getShipyardStatus(LocalDate requestDate, String partnerKey) {

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
//        String partnerKey = Objects.requireNonNull(authDTO).getPartnerKey();
        String companyKey = Objects.requireNonNull(Objects.requireNonNull(authDTO).getCompany());

        // oetmhd, 하차/회수 인것만, 배차 된것 만 조회
        List<DashBoardDTO.ShipyardVehicleDTO> shipyardList = qDashBoardRepository.findShipyardOrderAndVehicleByDate(requestDate, partnerKey, companyKey);

        Map<String, List<DashBoardDTO.ShipyardVehicleDTO>> shipmentMap = shipyardList.stream()
                .collect(Collectors.groupingBy(DashBoardDTO.ShipyardVehicleDTO::getShpmtky));

        LocalTime refTime = LocalTime.of(12, 0, 0);
        int amGoingCount = 0, amFinishedCount = 0, pmGoingCount = 0, pmFinishedCount = 0, goingCount = 0;

        for (Map.Entry<String, List<DashBoardDTO.ShipyardVehicleDTO>> entry : shipmentMap.entrySet()) {
            String key = entry.getKey();                            //shipment
            List<DashBoardDTO.ShipyardVehicleDTO> list = entry.getValue();          //운송list
            LocalTime dtoTime = list.get(0).getDvcmptm();                           //납품완료시간
            if(dtoTime == null) {
                dtoTime = list.get(0).getDventim();                                 //하차지 도착시간
            }

            boolean allCompleted = list.stream()
                    .allMatch(d -> DashBoardContant.COMPLETED_STATUS.contains(d.getTrstat()));
            boolean anyReady = list.stream()
                    .anyMatch(d -> DashBoardContant.READY_STATUS.contains(d.getTrstat()));

            if(dtoTime.isAfter(refTime)) {              // 오후
                if(allCompleted){           // 모든 운송 완료된건만
                    pmFinishedCount++;
                } else if (!anyReady) {     // 모두 준비중이 아니라면 운행중으로 판단
                    pmGoingCount++;
                }
            } else {                                    // 오전
                if(allCompleted){           // 모든 운송 완료된건만
                    amFinishedCount++;
                } else if (!anyReady) {     // 모두 준비중이 아니라면 운행중으로 판단
                    amGoingCount++;
                }
            }
        }

        goingCount = amGoingCount + pmGoingCount;
        DashBoardDTO.ShipyarStatusDTO statusDTO = DashBoardDTO.ShipyarStatusDTO.builder()
                .goingTransport(goingCount)
                .amFinished(amFinishedCount)
                .pmFinished(pmFinishedCount)
                .build();

        return statusDTO;
    }

    public List<DashBoardDTO.VehicleWeightDTO> getWeightArrival(LocalDate requestDate, String partnerKey) {

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
///        String partnerKey = Objects.requireNonNull(authDTO).getPartnerKey();
        String companyKey = Objects.requireNonNull(Objects.requireNonNull(authDTO).getCompany());

        List<DashBoardDTO.VehicleWeightDTO> vehicleWeightList = new ArrayList<>();

        // oetmhd에서 하차/회수, 배차 완료된것만 조회
        List<DashBoardDTO.ShipyardVehicleDTO> shipyardList = qDashBoardRepository.findShipyardOrderAndVehicleByDate(requestDate, partnerKey, companyKey);

        // 배차가 완료된 운송을 shipment 를 키로 해서 맵으로 변환
        Map<String, List<DashBoardDTO.ShipyardVehicleDTO>> shipmentMap = shipyardList.stream()
                .collect(Collectors.groupingBy(DashBoardDTO.ShipyardVehicleDTO::getShpmtky));

        // 차량정보 조회해서 map으로 변경
        List<DriverInfoDTO.DriverVehicleInfo> vehicleInfoList = qDashBoardRepository.getVehicleWeight();

        Map<Long, Float> vehicleInfoMap = vehicleInfoList.stream()
                .collect(Collectors.toMap(
                        DriverInfoDTO.DriverVehicleInfo::getVehicleId,
                        DriverInfoDTO.DriverVehicleInfo::getMaxPayload
                ));

        for(Map.Entry<String, List<DashBoardDTO.ShipyardVehicleDTO>> entry : shipmentMap.entrySet()) {
            String shipment = entry.getKey();
            List<DashBoardDTO.ShipyardVehicleDTO> transportList = entry.getValue();
            // 같은 shipment 일때 조선수 입문 시간은 하나의 운송주문에만 기입되므로 조선소내 전체 운송 중 찾아야 한다
            for(DashBoardDTO.ShipyardVehicleDTO dto : transportList) {
                // 조선소입문 시각
                LocalDate dateShipyard = dto.getCucmpdt();
                LocalTime timeShipyard = dto.getCucmptm();

                if(dateShipyard != null) {
                    Long vehicleId = dto.getVhclid();
                    Float weight = vehicleInfoMap.get(vehicleId);
                    if(weight != null) {
                        DashBoardDTO.VehicleWeightDTO vDto = DashBoardDTO.VehicleWeightDTO.builder()
                                .vehicleWeight(weight)
                                .shipmentNumber(shipment)
                                .timeArrival(timeShipyard.toString().replace(":", ""))
                                .build();

                        vehicleWeightList.add(vDto);
                    }

                    break;                          // shipment 하나엔 한번만 카운트
                }
            }
        }

        if(vehicleWeightList.size() == 0) {
            DashBoardDTO.VehicleWeightDTO dummyDto = DashBoardDTO.VehicleWeightDTO.builder()
                    .vehicleWeight(0F)
                    .shipmentNumber("0000000000")
                    .timeArrival("0000")
                    .build();

            vehicleWeightList.add(dummyDto);
        }
        return vehicleWeightList;
    }

    public List<DashBoardDTO.IssueDTO> getIssue(LocalDate fromDate, LocalDate toDate) {

        List<DashBoardDTO.IssueDTO> issueDTOS = new ArrayList<>();

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        String partnerKey = Objects.requireNonNull(authDTO).getPartnerKey();
        LocalDate requestDate = fromDate;
        while (!requestDate.isAfter(toDate)) {
            List<String> shipList = (partnerKey == null) ?
                    dashBoardRepository.findDistinctShipmentAllPartner(requestDate) :
                    dashBoardRepository.findDistinctShipment(requestDate, partnerKey);

            List<TransportEvent> eventEntities = dashBoardRepository.findByShipKeyIn(shipList);
            List<TransportEventDTO> eventDTOS = transportEventMapper.toDtoList(eventEntities);
            Map<String, List<TransportEventDTO>> groupedEvents = eventDTOS.stream()
                    .collect(Collectors.groupingBy(TransportEventDTO::getShipmentKey));
            for (Map.Entry<String, List<TransportEventDTO>> entry : groupedEvents.entrySet()) {
                String shipmentKey = entry.getKey();
                List<TransportEventDTO> events = entry.getValue();
                List<RealTrackingDTO.TrackingEventDTO> eventList = new ArrayList<>();
                for (TransportEventDTO event : events) {
                    RealTrackingDTO.TrackingEventDTO eventDTO = new RealTrackingDTO.TrackingEventDTO(event.getEventType(), event.getDetail());
                    eventList.add(eventDTO);
                }

                DashBoardDTO.IssueDTO issueDTO = new DashBoardDTO.IssueDTO(requestDate, shipmentKey, eventList);
                issueDTOS.add(issueDTO);
            }

            requestDate = requestDate.plusDays(1);
        }

        return issueDTOS;
    }

    public List<DashBoardDTO.EventDetailDTO> getIssueDetail(LocalDate fromDate, LocalDate toDate) {

        List<DashBoardDTO.EventDetailDTO> eventList = qDashBoardRepository.getEventAndVehicle(fromDate, toDate);

        return eventList;
    }
}
