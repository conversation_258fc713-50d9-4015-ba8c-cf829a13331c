package com.logistics.tms.loadingoptimizationmanager.feign.type;

import java.time.LocalDateTime;

import com.logistics.tms.common.enumeration.VehicleWeightTypeEnum;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * 아이템 정보를 담는 클래스
 */
@SuperBuilder
@Getter
@Setter
@NoArgsConstructor
public class Item {
    /**
     * 아이템 ID
     */
    private String id;
    /**
     * 하차지 조선소 그룹 키
     */
    private String shipyardGroupKey;


    /**
     * 그룹 아이디
     * 같은 groupId를 가졌으면 동일한 groupId의  트럭에 실려야 함
     * @example "세방HHI004"
     * @see Truck.groupId
     */
    private String groupId;

    /**
     * 픽업 주소
     */
    private Address pickupAddress;
    /**
     * 배송 주소
     */
    private Address dropAddress;
    /**
     * 너비(폭)
     */
    private Integer width;
    /**
     * 깊이
     */
    private Integer depth;
    /**
     * 높이
     */
    private Integer height;
    /**
     * 무게
     */
    private Integer weight;
    /**
     * 상차시간
     */
    private LocalDateTime pickupTime;
    /**
     * 하차시간
     */
    private LocalDateTime dropTime;

    /**
     * 포장ONE-SIDE 타입
     */
    private String packagingOneSideType;

    /**
     * 팔레트 박스 수량
     */
    private Integer palletBoxQuantity;

    /**
     * 포장형태: PLT(팔레트), NSTANDARD(비표준), ONESIDE(원사이드), BULK(벌크)
     */
    private String packagingForm;

    /**
     * 포장타입: T1100(PLT 표준), T550(PLT 1/2), T225(PLT 1/4), NORMAL(비표준), WOOD(우드),
     * OT1100(PLT 표준), OT550(PLT 1/2), OT225(PLT 1/4), BULK(벌크)
     */
    private String packagingType;

    /**
     * PartnerTypeEnum에 아이템 픽업지에 대한 파트너 타입을 지정
     * @example "3PL", "CARRIER", "CUSTOMER", "OUTCENTER", "LOADING", "OWNER", "VENDOR", "WAREHOUSE"
     * @see PartnerTypeEnum
     */
    private String pickupPartnerType;

    /**
     * PartnerTypeEnum에 아이템 배송지에 대한 파트너 타입을 지정
     * @example "3PL", "CARRIER", "CUSTOMER", "OUTCENTER", "LOADING", "OWNER", "VENDOR", "WAREHOUSE"
     * @see PartnerTypeEnum
     */
    private String dropoffPartnerType;

    /**
     * 상차지 적재 불가능한 상한 톤수
     * 해당 톤수 이상의 트럭에는 적재 불가능
     * 예: T11이면 11톤 이상의 트럭에는 적재 불가능
     * @example T11
     */
    private VehicleWeightTypeEnum pickupCannotLoadOverTonnage;

    /**
     * 하차지 적재 불가능한 상한 톤수
     * 해당 톤수 이상의 트럭에는 적재 불가능
     * 예: T11이면 11톤 이상의 트럭에는 적재 불가능
     * @example T11
     */
    private VehicleWeightTypeEnum dropoffCannotLoadOverTonnage;

    /**
     * 상차지 적재 불가능한 하한 톤수
     * 해당 톤수 이하의 트럭에는 적재 불가능
     * 예: T05면 5톤 이하의 트럭에는 적재 불가능
     * @example T05
     */
    private VehicleWeightTypeEnum pickupCannotLoadUnderTonnage;

    /**
     * 하차지 적재 불가능한 하한 톤수
     * 해당 톤수 이하의 트럭에는 적재 불가능
     * 예: T05면 5톤 이하의 트럭에는 적재 불가능
     * @example T05
     */
    private VehicleWeightTypeEnum dropoffCannotLoadUnderTonnage;

    /**
     * 하단 적재 가능 여부 (Bottom Stackable YN)
     * 우든타입에서 하단에 적재 가능한지 여부
     *  @example false
     */
    private Boolean bottomStackable;

    /**
     * 상단 적재 가능 여부 (Top Stackable YN)
     * 우든타입에서 상단에 적재 가능한지 여부
     * @example false
     */
    private Boolean topStackable;

}
