package com.logistics.tms.loadingoptimizationmanager.feign.type;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class Position {
    // x,y 좌표 (단위: mm)
    private Integer x;
    private Integer y;

    // 550mm = 1 unit 으로 계산된 좌표값
    private Integer unitX;
    private Integer unitY;

    // 550mm = 1 unit 으로 계산된 너비, 높이.
    // 먄약에 중량에 때라서 가운데 정렬일 경우에는 unitWidth 값이 트럭폭을 가지도록 한다.
    private Integer unitWidth;
    private Integer unitDepth;

    //적재 층수 (0: 하단/1층, 1: 상단/2층) 없을 경우 0으로 간주함
    private Integer level;
}
