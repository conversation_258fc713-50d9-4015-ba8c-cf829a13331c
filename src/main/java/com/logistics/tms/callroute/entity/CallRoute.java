package com.logistics.tms.callroute.entity;

import com.logistics.tms.common.constant.TmsConstant;
import com.logistics.tms.common.model.BaseEntity;
import com.logistics.tms.framework.converter.AsetecDateYmdConverter;

import jakarta.persistence.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import lombok.experimental.SuperBuilder;

import org.hibernate.annotations.Comment;
import org.hibernate.annotations.ColumnDefault;
import org.locationtech.jts.geom.Point;
import org.springframework.format.annotation.DateTimeFormat;

import org.locationtech.jts.geom.LineString;
import org.hibernate.annotations.ColumnTransformer;

@Comment("라우트 정보")
@Getter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Entity
@Table(name = "tmcallrt",
        indexes = {
            @Index(name = "IDX_VEHICID", columnList = "VEHICID"),
            @Index(name = "IDX_SHPMTKY", columnList = "SHPMTKY"),
            @Index(name = "IDX_VEHICID_SHPMTKY", columnList = "VEHICID, SHPMTKY"),
            @Index(name = "IDX_TRRQDT_TRROUND", columnList = "TRRQDT, TRROUND"),
        }
)
public class CallRoute  extends BaseEntity {

    @Builder.Default
    @Size(max = 20)
    @NotNull
    @Comment("Service Client")
    @Column(nullable = false, length = 20)
    private String compkey = TmsConstant.DEFAULT_COMPKEY;

    @Id
    @Comment("라우트 로그 ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "route_id")
    private Long routeId;

    @Comment("차량 ID")
    @NotNull
    @Column(name = "VEHICID", nullable = false)
    private Long vehicleId;

    @Comment("운송 ID")
    @Column(name = "TRANSID", nullable = true)
    private Long transportId;

    @Comment("Shipment No.")
    @NotNull
    @Column(name = "SHPMTKY", length = 10, nullable = false)
    // @ManyToOne(fetch = FetchType.EAGER, optional = false)
    // @JoinColumn(name = "SHPMTKY", nullable = false, foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT)) // FK 제약조건 제거
    private String shpmtky;

    @Comment("운송 회차")
    @Column(name = "TRROUND", nullable = false, columnDefinition = "INT default 1")
    private Integer transportRound;

    @Comment("운송요청 일자")
    @ColumnDefault("' '")
    @Column(name = "TRRQDT", length = 8, nullable = false)
    @Convert(converter = AsetecDateYmdConverter.class)
    private LocalDate requestDate;

    @Comment("실제 운행 경로")
    @Column(name = "LINE",columnDefinition = "MEDIUMTEXT")
    @ColumnTransformer(
            read = "ST_GeomFromText(FN_AES_DECRYPT(LINE))",
            write = "FN_AES_ENCRYPT(ST_AsText(?))"
    )
    private LineString route;

    @Comment("실제 시간(초)")
    @Column(name = "TIME")
    private Integer time;

    @Comment("실제 거리(미터)")
    @Column(name = "DISTANCE")
    private Integer distance;

    @Comment("최초 의뢰등록 시 예상소요시간(초)")
    @Column(name = "EXPTIME")
    private Integer expectedTime;

    @Comment("최초 의뢰등록 시 예상소요거리(미터)")
    @Column(name = "EXPDIST")
    private Integer expectedDistance;

    @Comment("남은 시간(초)")
    @Column(name = "RMATIME")
    private Integer remainedTime;

    @Comment("남은 거리(미터)")
    @Column(name = "RMADIST")
    private Integer remainedDistance;

    @Comment("암호화된 GPS 위치")
    @Column(name = "GPSLOCA", columnDefinition = "MEDIUMTEXT")
    @ColumnTransformer(
            read = "ST_GeomFromText(FN_AES_DECRYPT(GPSLOCA))",
            write = "FN_AES_ENCRYPT(ST_AsText(?))"
    )
    private Point gpsLocation;

    @Comment("암호화된 맵매칭된 위치")
    @Column(name = "MATLOCA", columnDefinition = "MEDIUMTEXT")
    @ColumnTransformer(
            read = "ST_GeomFromText(FN_AES_DECRYPT(MATLOCA))",
            write = "FN_AES_ENCRYPT(ST_AsText(?))"
    )
    private Point matchedLocation;

    @Comment("방위각")
    @Column(name = "ANGLE")
    private Integer angle;

    @Comment("속도")
    @Column(name = "SPEED")
    private Integer speed;

    @Comment("최근 운행 경로")
    @Column(name = "LTSROUTE",columnDefinition = "MEDIUMTEXT")
    @ColumnTransformer(
            read = "ST_GeomFromText(FN_AES_DECRYPT(LTSROUTE))",
            write = "FN_AES_ENCRYPT(ST_AsText(?))"
    )
    private LineString latestRoute;

    @Comment("등록 일시")
    @NotNull
	@ColumnDefault("CURRENT_TIMESTAMP")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "REGISDT", nullable = false)
    private LocalDateTime regDt;

    @Comment("사용자ID")
    @Size(max = 60)
    @NotNull
    @Column(name = "USERACT", nullable = false, length = 60)
    private String useract;

    // Getters and Setters
}
