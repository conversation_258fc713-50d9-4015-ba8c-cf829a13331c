package com.logistics.tms.transport.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public class TmsToVmsDTO {
    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class RequestDTO {
        String company;
        String eventCode;
        Map<String, Object> params;
//        List<String> targetUsers;
        String userId;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class ResponseDTO {
        boolean success;
        PushMessageCode messageCode;
    }

    public enum PushMessageCode {
        pushSuccess, pushError, pushVerificationFail
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class SafetyRequestDTO {
        String company;
        String category;
        Long eventId;
        BigDecimal longitude;
        BigDecimal latitude;
        String userId;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class SpeedRequestDTO {
        String company;
        String category;
        Long trackId;
        BigDecimal longitude;
        BigDecimal latitude;
        String userId;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class SuddenRequestDTO {
        String company;
        String category;
        Long trackId;
        BigDecimal longitude;
        BigDecimal latitude;
        String userId;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class DispatchConfirmedDTO {
//        String company;
//        String shipmentKey;
//        Long vehicleId;
//        String userId;
        String company;
        String eventCode;
        Map<String, Object> params;
        //        List<String> targetUsers;
        String userId;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @SuperBuilder
    public static class LoadAndUnloadDTO {
//        String company;
//        String shipmentKey;
//        String partnerCode;
//        Long vehicleId;
//        String userId;

        String company;
        String eventCode;
        Map<String, Object> params;
        String userId;

    }
}
