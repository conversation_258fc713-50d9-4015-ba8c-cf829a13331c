package com.logistics.tms.transport.controller;

import com.logistics.tms.app.util.FileStorage;
import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.configuration.security.AuthUser;
import com.logistics.tms.lbs.domain.route.LbsRouteOnDemandResponse;
import com.logistics.tms.transport.constant.TransportConstant;
import com.logistics.tms.transport.dto.*;
import com.logistics.tms.transport.entity.TransportInfo;
import com.logistics.tms.transport.interfaces.TransportInfoSwagger;
import com.logistics.tms.transport.service.TransportInfoService;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/api/transport")
@RequiredArgsConstructor
public class TransportInfoController implements TransportInfoSwagger {
    private final TransportInfoService transportInfoService;

    @GetMapping("/byId")
    public TransportInfoDTO getTransportInfoById(@RequestParam final Long transportId) {
        return transportInfoService.findByTransportId(transportId);
    }

    @GetMapping("/summary")
    public ResponseEntity<?> getTransportSummary(
            @RequestParam final Long vehicleId,
            @RequestParam(value = "today", required = false) @Nullable final String today) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate localDate = (today != null) ? LocalDate.parse(today, formatter) : LocalDate.now();
            return ResponseEntity.ok(transportInfoService.getSummaryNew(vehicleId, localDate));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/byVehicle")
    public ResponseEntity<?> getTransportInfoByVehicle(
            @RequestParam final Long vehicleId,
            @RequestParam final int transportRound,
            @RequestParam @NotBlank final String requestDate) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate localDate = LocalDate.parse(requestDate, formatter);
            TransportInfoResponseDTO.ByVehicleDTO response = transportInfoService.findByVehicleIdAndRequestDate(vehicleId, transportRound, localDate);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/route")
    public ResponseEntity<?> getRoute(
            @RequestParam final Long vehicleId,
            @RequestParam final int transportRound,
            @RequestParam @NotBlank final String requestDate,
            @RequestParam final Double gpsX,
            @RequestParam final Double gpsY
            ) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate localDate = LocalDate.parse(requestDate, formatter);
            TransportInfoResponseDTO.ByMapDTO response = transportInfoService.findRoute(vehicleId, transportRound, localDate, gpsX, gpsY);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/confirmation")
    public ResponseEntity<?> getTransportConfirmation(@RequestParam @NotBlank final String shipmentKey) {
        try {
            TransportConfirmationDTO.ShippingDTO response = transportInfoService.getTransportConfirmation(shipmentKey);

            return ResponseEntity.ok(response);
        } catch (DateTimeParseException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PostMapping
    public ResponseEntity<?> createTransportInfo(@RequestBody TransportInfoUpdateDTO transportInfoUpdateDTO) {
        try {
            return ResponseEntity.ok(transportInfoService.save(transportInfoUpdateDTO));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PostMapping("/list")
    public ResponseEntity<?> createTransportInfos(@RequestBody List<TransportInfoUpdateDTO> transportInfoUpdateDTOs) {
        try {
            return ResponseEntity.ok(transportInfoService.saveAll(transportInfoUpdateDTOs) + " created");
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PostMapping("/shipment")
    public ResponseEntity<?> createTransportByShipment(@RequestBody TransportInfoUpdateByDispatch.ByShipmentDTO updateByShipmentDTO) {
        try {
            log.info("createTransportByShipment: {}", updateByShipmentDTO.getShipmentKey());
            LbsRouteOnDemandResponse lbsResponse = new LbsRouteOnDemandResponse();
            return ResponseEntity.ok(transportInfoService.saveByShipment(updateByShipmentDTO, lbsResponse) + " created");
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/modifyStatus")
    public ResponseEntity<?> modifyTransportStatus(
            @RequestParam final Long transportId,
            @RequestParam final TransportInfo.TransportStatus transportStatus,
            @Nullable final Float latitude,
            @Nullable final Float longitude) {
        try {
            return ResponseEntity.ok(transportInfoService.modifyTransportStatus(transportId, transportStatus, latitude, longitude));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping("/modifyOrder")
    public ResponseEntity<?> modifyOrder(@RequestBody List<Long> transportIds) {
        try {
            List<TransportInfoDTO> dtos = transportInfoService.modifyTransportOrder(transportIds);
            transportInfoService.modifyRequiredTime(dtos.get(0));
            return ResponseEntity.ok(dtos);
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PutMapping(value ="/modifySign", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> modifySign(@RequestParam final Long transportId, @RequestParam("file") MultipartFile file) {
        try {
            return ResponseEntity.ok(transportInfoService.modifySign(transportId, file));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/download")
    public ResponseEntity<StreamingResponseBody> downloadSign(@AuthUser final AuthDTO authDTO,
                                                              @RequestParam @NotBlank final String signUrl) {

        if (Objects.nonNull(authDTO))
            return FileStorage.downloadStreaming(signUrl);
        else {
            throw new RuntimeException(TransportConstant.AUTH_NOTFOUND);
        }
    }

    @PutMapping("/impossible")
    public ResponseEntity<?> addImpossible(@RequestParam final Long transportId,
                                           @RequestParam @NotBlank final String impossible,
                                           @RequestParam(value = "newPickUp", required = false) @Nullable final boolean newPickUp) {
        try {
            return ResponseEntity.ok(transportInfoService.addTransportImpossible(transportId, impossible, newPickUp));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }
}
