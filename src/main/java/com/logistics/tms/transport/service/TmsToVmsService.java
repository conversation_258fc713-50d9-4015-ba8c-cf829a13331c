package com.logistics.tms.transport.service;

import com.logistics.tms.common.util.AuthUtils;
import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.external.dto.OetmhdPushDTO;
import com.logistics.tms.transport.dto.TmsToVmsDTO;
import com.logistics.tms.transport.repository.QTransportInfoRepository;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.DefaultUriBuilderFactory;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service
@Component
@RequiredArgsConstructor
public class TmsToVmsService {
    private final QTransportInfoRepository qTransportInfoRepository;

    @Value("${host.vms-url}")
    private String vmsUrl;

    @Value("${host.vms-token}")
    private String vmsToken;

    public WebClient createWebClient() {
        String token = vmsToken;

        DefaultUriBuilderFactory factory = new DefaultUriBuilderFactory(vmsUrl);
        return WebClient.builder().uriBuilderFactory(factory)
//                .defaultHeader(HttpHeaders.AUTHORIZATION, token)
                .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + token)
                .build();
    }

    @Getter
    @AllArgsConstructor
    public enum PushEventType {
        LOADING_VOLUME_MISMATCH("0000002028"),
        LOADING_UNLOADING_INSPECTION_MISMATCH("0000003002"),
        ENTRY_IMPOSSIBLE("0000003003"),
        LOADING_UNLOADING_INFORMATION_MISMATCH("0000003004"),
        DISPATCH_CONFIRM("0000003024"),
        LOAD_START("0000003025"),
        UNLOAD_START("0000003027"),
        SHIPYARD_START("0000004002"),
        SHIPYARD_EQUIP("0000004003"),
        SHIPYARD_FINISH("0000004004")
        ;

        private final String eventCode;

    }

    public Mono<TmsToVmsDTO.ResponseDTO> postEvent(TmsToVmsDTO.RequestDTO requestDTO) {
        try {
            WebClient client = createWebClient();

            return client.post()
                    .uri("/push")
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(Mono.just(requestDTO), TmsToVmsDTO.RequestDTO.class)
                    .retrieve()
                    .bodyToMono(TmsToVmsDTO.ResponseDTO.class);
        } catch (RuntimeException e) {
            log.info(e.getMessage());
            return null;
        }
    }

    public void postVolumeMismatch(String vehicleNumber) {
        TmsToVmsDTO.RequestDTO requestDTO = new TmsToVmsDTO.RequestDTO();
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            requestDTO.setCompany(authDTO.getCompany());
            requestDTO.setEventCode(PushEventType.LOADING_VOLUME_MISMATCH.getEventCode());
            Map<String, Object> params = Map.of("regnum", vehicleNumber);
            requestDTO.setParams(params);
//            requestDTO.setTargetUsers(qTransportInfoRepository.getTargetUsers(authDTO.getPartnerKey()));
            requestDTO.setUserId(authDTO.getId());
        }
        Mono<TmsToVmsDTO.ResponseDTO> response = postEvent(requestDTO);
        response.subscribe(res -> log.info("postVolumeMismatch: {}", res.isSuccess()));
    }

    public void postInspectionMismatch(String oetmsky, String vehicleNumber) {
        TmsToVmsDTO.RequestDTO requestDTO = new TmsToVmsDTO.RequestDTO();
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            requestDTO.setCompany(authDTO.getCompany());
            requestDTO.setEventCode(PushEventType.LOADING_UNLOADING_INSPECTION_MISMATCH.getEventCode());
//            Map<String, Object> params = Map.of("regnum", vehicleNumber);
            Map<String, Object> params = new HashMap<>();
            params.put("oetmsk", oetmsky);
            params.put("regnum", vehicleNumber);
            requestDTO.setParams(params);
//            requestDTO.setTargetUsers(qTransportInfoRepository.getTargetUsers(authDTO.getPartnerKey()));
            requestDTO.setUserId(authDTO.getId());
        }
        Mono<TmsToVmsDTO.ResponseDTO> response = postEvent(requestDTO);
        response.subscribe(res -> log.info("postInspectionMismatch: {}", res.isSuccess()));
    }

    public void postEntryImpossible(String oetmsky, String vehicleNumber) {
        TmsToVmsDTO.RequestDTO requestDTO = new TmsToVmsDTO.RequestDTO();
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            requestDTO.setCompany(authDTO.getCompany());
            requestDTO.setEventCode(PushEventType.ENTRY_IMPOSSIBLE.getEventCode());
//            Map<String, Object> params = Map.of("regnum", vehicleNumber);
            Map<String, Object> params = new HashMap<>();
            params.put("oetmsk", oetmsky);
            params.put("regnum", vehicleNumber);
            requestDTO.setParams(params);
//            requestDTO.setTargetUsers(qTransportInfoRepository.getTargetUsers(authDTO.getPartnerKey()));
            requestDTO.setUserId(authDTO.getId());
        }
        Mono<TmsToVmsDTO.ResponseDTO> response = postEvent(requestDTO);
        response.subscribe(res -> log.info("postEntryImpossible: {}", res.isSuccess()));
    }

    public void postInformationMismatch(String vehicleNumber) {
        TmsToVmsDTO.RequestDTO requestDTO = new TmsToVmsDTO.RequestDTO();
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            requestDTO.setCompany(authDTO.getCompany());
            requestDTO.setEventCode(PushEventType.LOADING_UNLOADING_INFORMATION_MISMATCH.getEventCode());
            Map<String, Object> params = Map.of("regnum", vehicleNumber);
            requestDTO.setParams(params);
//            requestDTO.setTargetUsers(qTransportInfoRepository.getTargetUsers(authDTO.getPartnerKey()));
            requestDTO.setUserId(authDTO.getId());
        }
        Mono<TmsToVmsDTO.ResponseDTO> response = postEvent(requestDTO);
        response.subscribe(res -> log.info("postInformationMismatch: {}", res.isSuccess()));
    }

    public Mono<TmsToVmsDTO.ResponseDTO> postSafetyEvent(TmsToVmsDTO.SafetyRequestDTO requestDTO) {
        try {
            WebClient client = createWebClient();

            return client.post()
                    .uri("/push/safety")
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(Mono.just(requestDTO), TmsToVmsDTO.SafetyRequestDTO.class)
                    .retrieve()
                    .bodyToMono(TmsToVmsDTO.ResponseDTO.class);
        } catch (RuntimeException e) {
            log.info(e.getMessage());
            return null;
        }
    }

    public void postSafety(String company, String category, Long eventId, BigDecimal longitude, BigDecimal latitude, String userId) {
        TmsToVmsDTO.SafetyRequestDTO safetyRequestDTO = new TmsToVmsDTO.SafetyRequestDTO(company, category, eventId, longitude, latitude, userId);
        Mono<TmsToVmsDTO.ResponseDTO> response = postSafetyEvent(safetyRequestDTO);
        response.subscribe(res -> log.info("postSafetyEvent: {}", res));
    }

    public Mono<TmsToVmsDTO.ResponseDTO> postSpeedEvent(TmsToVmsDTO.SpeedRequestDTO requestDTO) {
        try {
            WebClient client = createWebClient();

            return client.post()
//                    .uri("/push/speeding")
                    .uri("/push/safety")
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(Mono.just(requestDTO), TmsToVmsDTO.SpeedRequestDTO.class)
                    .retrieve()
                    .bodyToMono(TmsToVmsDTO.ResponseDTO.class);
        } catch (RuntimeException e) {
            log.info(e.getMessage());
            return null;
        }
    }

    public void postSpeed(String company, String category, Long trackId, BigDecimal longitude, BigDecimal latitude, String userId) {
        TmsToVmsDTO.SpeedRequestDTO speedRequestDTO = new TmsToVmsDTO.SpeedRequestDTO(company, category, trackId, longitude, latitude, userId);
        Mono<TmsToVmsDTO.ResponseDTO> response = postSpeedEvent(speedRequestDTO);
        response.subscribe(res -> log.info("postSpeedEvent: {}", res));
    }

    public Mono<TmsToVmsDTO.ResponseDTO> postSuddenEvent(TmsToVmsDTO.SuddenRequestDTO requestDTO) {
        try {
            WebClient client = createWebClient();

            return client.post()
//                    .uri("/push/sudden")
                    .uri("/push/safety")
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(Mono.just(requestDTO), TmsToVmsDTO.SuddenRequestDTO.class)
                    .retrieve()
                    .bodyToMono(TmsToVmsDTO.ResponseDTO.class);
        } catch (RuntimeException e) {
            log.info(e.getMessage());
            return null;
        }
    }

    public void postSudden(String company, String category, Long trackId, BigDecimal longitude, BigDecimal latitude, String userId) {
        TmsToVmsDTO.SuddenRequestDTO suddenRequestDTO = new TmsToVmsDTO.SuddenRequestDTO(company, category, trackId, longitude, latitude, userId);
        Mono<TmsToVmsDTO.ResponseDTO> response = postSuddenEvent(suddenRequestDTO);
        response.subscribe(res -> log.info("postSuddenEvent: {}", res));
    }

    public Mono<TmsToVmsDTO.ResponseDTO> postDispatchedEvent(TmsToVmsDTO.DispatchConfirmedDTO requestDTO) {
        try {
            WebClient client = createWebClient();

            return client.post()
//                    .uri("/push/tmsconfirm")
                    .uri("/push")
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(Mono.just(requestDTO), TmsToVmsDTO.DispatchConfirmedDTO.class)
                    .retrieve()
                    .bodyToMono(TmsToVmsDTO.ResponseDTO.class);
        } catch (RuntimeException e) {
            log.info(e.getMessage());
            return null;
        }
    }

    public void postDispatchConfirmed(String shipmentKey, String useract, String username, String telphnm) {
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
//        TmsToVmsDTO.DispatchConfirmedDTO dispatchConfirmedDTO = new TmsToVmsDTO.DispatchConfirmedDTO(
//                Objects.requireNonNull(authDTO).getCompany(), shipmentKey, vehicleId, authDTO.getId());
        TmsToVmsDTO.DispatchConfirmedDTO dispatchConfirmedDTO = new TmsToVmsDTO.DispatchConfirmedDTO();
        dispatchConfirmedDTO.setCompany(Objects.requireNonNull(authDTO).getCompany());
        dispatchConfirmedDTO.setEventCode(PushEventType.DISPATCH_CONFIRM.getEventCode());
        dispatchConfirmedDTO.setUserId(Objects.requireNonNull(authDTO).getId());
        Map<String, Object> params = new HashMap<>();
        params.put("tmshpno", shipmentKey);
        params.put("useract", useract);
        params.put("username", username);
        params.put("telphnm", telphnm);
        dispatchConfirmedDTO.setParams(params);

        Mono<TmsToVmsDTO.ResponseDTO> response = postDispatchedEvent(dispatchConfirmedDTO);
        response.subscribe(res -> log.info("postDispatchedEvent: {}", res));
    }

    public Mono<TmsToVmsDTO.ResponseDTO> postLoadUnloadMoveEvent(TmsToVmsDTO.LoadAndUnloadDTO requestDTO, Boolean isLoad) {
        try {
            WebClient client = createWebClient();

            if (isLoad) {
                return client.post()
//                        .uri("/push/load")
                        .uri("/push")
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(Mono.just(requestDTO), TmsToVmsDTO.LoadAndUnloadDTO.class)
                        .retrieve()
                        .bodyToMono(TmsToVmsDTO.ResponseDTO.class);
            } else {
                return client.post()
//                        .uri("/push/unload")
                        .uri("/push")
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(Mono.just(requestDTO), TmsToVmsDTO.LoadAndUnloadDTO.class)
                        .retrieve()
                        .bodyToMono(TmsToVmsDTO.ResponseDTO.class);
            }
        } catch (RuntimeException e) {
            log.info(e.getMessage());
            return null;
        }
    }

    public void postLoadUnloadMove(String shipmentKey, String username, String telphnm, String estTime, String partnerKey, Boolean isLoad) {
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
//        TmsToVmsDTO.LoadAndUnloadDTO loadAndUnloadDTO = new TmsToVmsDTO.LoadAndUnloadDTO(
//                Objects.requireNonNull(authDTO).getCompany(), shipmentKey, partnerKey, vehicleId, authDTO.getId());
        TmsToVmsDTO.LoadAndUnloadDTO loadAndUnloadDTO = new TmsToVmsDTO.LoadAndUnloadDTO();
        loadAndUnloadDTO.setCompany(Objects.requireNonNull(authDTO).getCompany());
        if(isLoad) {
            loadAndUnloadDTO.setEventCode(PushEventType.LOAD_START.getEventCode());
        } else {
            loadAndUnloadDTO.setEventCode(PushEventType.UNLOAD_START.getEventCode());
        }
        loadAndUnloadDTO.setUserId(Objects.requireNonNull(authDTO).getId());
        Map<String, Object> params = new HashMap<>();
        params.put("tmshpno", shipmentKey);
        params.put("ptnrkey", partnerKey);
        params.put("username", username);
        params.put("telphnm", telphnm);
        params.put("eta", estTime);

        loadAndUnloadDTO.setParams(params);

        Mono<TmsToVmsDTO.ResponseDTO> response = postLoadUnloadMoveEvent(loadAndUnloadDTO, isLoad);
        response.subscribe(res -> log.info("postLoadUnloadMoveEvent: {}", res));
    }

    public void postGoingShipyard(String arrTime, OetmhdPushDTO pushDTO) {
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        TmsToVmsDTO.LoadAndUnloadDTO loadAndUnloadDTO = new TmsToVmsDTO.LoadAndUnloadDTO();
        loadAndUnloadDTO.setCompany(Objects.requireNonNull(authDTO).getCompany());
        loadAndUnloadDTO.setEventCode(PushEventType.SHIPYARD_START.getEventCode());
        loadAndUnloadDTO.setUserId(Objects.requireNonNull(authDTO).getId());

        Map<String, Object> params = new HashMap<>();
        params.put("ata", arrTime);
        params.put("ptnamlcs", pushDTO.getPtnamlc());
        params.put("vehicno", pushDTO.getVehicno());
        params.put("vhctncd", pushDTO.getVhctncd());
        params.put("driverName", pushDTO.getDrivernm());
        params.put("telphnm", pushDTO.getDriverTel());
        params.put("denamlc", pushDTO.getDenamlc());
        params.put("opakqty", pushDTO.getOpakqty());
        params.put("recipientPhoneNumber", pushDTO.getRecTelnm());
        params.put("recipientName", pushDTO.getRecinm());

        loadAndUnloadDTO.setParams(params);

        Mono<TmsToVmsDTO.ResponseDTO> response = postGoingShipyardEvent(loadAndUnloadDTO);
        response.subscribe(res -> log.info("postGoingShipyardEvent: {}", res));
    }

    public void postGoingShipyardWithEquipment(String startTime, String arrTime, OetmhdPushDTO pushDTO) {
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        TmsToVmsDTO.LoadAndUnloadDTO loadAndUnloadDTO = new TmsToVmsDTO.LoadAndUnloadDTO();
        loadAndUnloadDTO.setCompany(Objects.requireNonNull(authDTO).getCompany());
        loadAndUnloadDTO.setEventCode(PushEventType.SHIPYARD_EQUIP.getEventCode());
        loadAndUnloadDTO.setUserId(Objects.requireNonNull(authDTO).getId());

        Map<String, Object> params = new HashMap<>();
        params.put("startDateTime", startTime);
        params.put("ptnamlcs", pushDTO.getPtnamlc());
        params.put("vehicno", pushDTO.getVehicno());
        params.put("vhctncd", pushDTO.getVhctncd());
        params.put("eta", arrTime);
        params.put("driverName", pushDTO.getDrivernm());
        params.put("telphnm", pushDTO.getDriverTel());
        params.put("denamlc", pushDTO.getDenamlc());
        params.put("opakqty", pushDTO.getOpakqty());
        params.put("droprnm", pushDTO.getEquipment());
        params.put("recipientPhoneNumber", pushDTO.getRecTelnm());
        params.put("recipientName", pushDTO.getRecinm());

        loadAndUnloadDTO.setParams(params);

        Mono<TmsToVmsDTO.ResponseDTO> response = postGoingShipyardEvent(loadAndUnloadDTO);
        response.subscribe(res -> log.info("postGoingShipyardEvent: {}", res));
    }

    public void postFinishShipyard(String startTime, String arrTime, OetmhdPushDTO pushDTO) {
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        TmsToVmsDTO.LoadAndUnloadDTO loadAndUnloadDTO = new TmsToVmsDTO.LoadAndUnloadDTO();
        loadAndUnloadDTO.setCompany(Objects.requireNonNull(authDTO).getCompany());
        loadAndUnloadDTO.setEventCode(PushEventType.SHIPYARD_FINISH.getEventCode());
        loadAndUnloadDTO.setUserId(Objects.requireNonNull(authDTO).getId());

        Map<String, Object> params = new HashMap<>();
        params.put("startDateTime", startTime);
        params.put("ptnamlcs", pushDTO.getPtnamlc());
        params.put("vehicno", pushDTO.getVehicno());
        params.put("vhctncd", pushDTO.getVhctncd());
        params.put("eta", arrTime);
        params.put("driverName", pushDTO.getDrivernm());
        params.put("telphnm", pushDTO.getDriverTel());
        params.put("denamlc", pushDTO.getDenamlc());
        params.put("opakqty", pushDTO.getOpakqty());
        params.put("droprnm", pushDTO.getEquipment());
        params.put("recipientPhoneNumber", pushDTO.getRecTelnm());
        params.put("recipientName", pushDTO.getRecinm());

        loadAndUnloadDTO.setParams(params);

        Mono<TmsToVmsDTO.ResponseDTO> response = postGoingShipyardEvent(loadAndUnloadDTO);
        response.subscribe(res -> log.info("postGoingShipyardEvent: {}", res));
    }

    public Mono<TmsToVmsDTO.ResponseDTO> postGoingShipyardEvent(TmsToVmsDTO.LoadAndUnloadDTO requestDTO) {
        try {
            WebClient client = createWebClient();

            return client.post()
                    .uri("/push/kakao")
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(Mono.just(requestDTO), TmsToVmsDTO.LoadAndUnloadDTO.class)
                    .retrieve()
                    .bodyToMono(TmsToVmsDTO.ResponseDTO.class);

        } catch (RuntimeException e) {
            log.info(e.getMessage());
            return null;
        }
    }
}
