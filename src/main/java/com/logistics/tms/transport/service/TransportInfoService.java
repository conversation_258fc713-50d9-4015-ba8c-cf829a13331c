package com.logistics.tms.transport.service;

import com.logistics.tms.app.util.FileStorage;
import com.logistics.tms.callroute.dto.CallRouteCustomDTO;
import com.logistics.tms.callroute.service.CallRouteService;
import com.logistics.tms.common.enumeration.OrderStatusEnum;
import com.logistics.tms.common.enumeration.PartnerTypeEnum;
import com.logistics.tms.common.enumeration.VehicleStatusEnum;
import com.logistics.tms.common.enumeration.VisitTypeEnum;
import com.logistics.tms.common.util.AuthUtils;
import com.logistics.tms.common.util.GeometryUtils;
import com.logistics.tms.common.util.NumberUtils;
import com.logistics.tms.configuration.error.TransportException;
import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.driver.dto.DriverInfoDTO;
import com.logistics.tms.driver.dto.DriverVehicleInfoDTO;
import com.logistics.tms.driver.service.DriverInfoCustomService;
import com.logistics.tms.driver.service.DriverVehicleInfoService;
import com.logistics.tms.external.dto.MptnmaAddressResponseDTO;
import com.logistics.tms.external.dto.OetmhdPushDTO;
import com.logistics.tms.external.service.McodemService;
import com.logistics.tms.external.service.MptnmaService;
import com.logistics.tms.external.service.OetmhdService;
import com.logistics.tms.lbs.constant.LbsConstant;
import com.logistics.tms.lbs.domain.route.LbsRouteDestination;
import com.logistics.tms.lbs.domain.route.LbsRouteOnDemandRequest;
import com.logistics.tms.lbs.domain.route.LbsRouteOnDemandResponse;
import com.logistics.tms.lbs.service.LbsRouteService;
import com.logistics.tms.push.service.PushService;
import com.logistics.tms.realtracking.repository.QRealTrackingRepository;
import com.logistics.tms.shipment.dto.ShipmentSectionDTO;
import com.logistics.tms.shipment.service.ShipmentSectionService;
import com.logistics.tms.transport.constant.TransportConstant;
import com.logistics.tms.transport.dto.*;
import com.logistics.tms.transport.entity.TransportInfo;
import com.logistics.tms.transport.mapper.TransportInfoMapper;
import com.logistics.tms.transport.repository.QTransportInfoRepository;
import com.logistics.tms.transport.repository.TransportInfoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Transactional(readOnly = true)
@Service
public class TransportInfoService {
    private final TransportInfoRepository transportInfoRepository;
    private final TransportInfoMapper transportInfoMapper;
    private final MptnmaService mptnmaService;
    private final ShipmentSectionService shipmentSectionService;
    private final QRealTrackingRepository qRealTrackingRepository;
    private final DriverVehicleInfoService driverVehicleInfoService;
    private final CallRouteService callRouteService;
    private final QTransportInfoRepository qTransportInfoRepository;
    private final PushService pushService;
    private final TmsToVmsService tmsToVmsService;
    private final DriverInfoCustomService driverInfoCustomService;
    private final McodemService mcodemService;
//    private final TransportInfoService transportInfoService;
    private final OetmhdService oetmhdService;

    @Autowired
    private LbsRouteService lbsRouteService;

    @Autowired
    private FileStorage fileStorage;

    public List<TransportInfoDTO> getTransportByShipyard(final Long transportId) {
        TransportInfo entity = transportInfoRepository.findByTransportId(transportId);
        if (entity.getDropOffArea() != null) {
            Long vehicleId = entity.getVehicleId();
            int transportRound = entity.getTransportRound();
            LocalDate requestDate = entity.getRequestDate();

            return transportInfoRepository.findByVehicleIdAndTransportRoundAndRequestDateOrderByTransportOrderAsc(vehicleId, transportRound, requestDate)
                    .stream()
                    .map(transportInfoMapper::toDto)
                    .filter(transport -> Objects.nonNull(transport.getDropOffArea()))
                    .toList();
        } else {
            return null;
        }
    }

    public TransportInfoDTO findByTransportId(final Long transportId) {
        TransportInfo entity = transportInfoRepository.findByTransportId(transportId);
        return transportInfoMapper.toDto(entity);
    }

    public TransportInfoResponseDTO.SummaryDTO getSummaryHistory(final Long vehicleId, LocalDate localDate) {
        LocalDate requestDate = transportInfoRepository.findDistinctRequestDateByVehicleIdAndRequestDateEqual(vehicleId, localDate);
        if (requestDate == null)
            return null;

        TransportInfoResponseDTO.SummaryDTO dto = new TransportInfoResponseDTO.SummaryDTO();

        int roundCount = transportInfoRepository.maxDistinctByTransportRoundAndVehicleIdAndRequestDate(vehicleId, requestDate);
        int[] transportCount = new int[roundCount];
        for (int round = 1; round <= roundCount; round++) {
            transportCount[round - 1] = transportInfoRepository.countDistinctByTransportCodeAndVehicleIdAndRequestDateEqualsAndTransportRoundEquals(vehicleId, requestDate, round);
        }

        dto.setRequestDate(requestDate);
        dto.setRoundCount(roundCount);
        dto.setTransportCount(transportCount);

        return dto;
    }

    public List<TransportInfoResponseDTO.SummaryDTO> getSummaryNew(final Long vehicleId, LocalDate localDate) {
        List<TransportInfoResponseDTO.SummaryDTO> transportInfoResponseBySummaryDTO = new ArrayList<>();

        Long limitDate = mcodemService.getLimitDate();
        LocalDate startDate = localDate.minusDays(limitDate);
        List<TransportInfoResponseDTO.DateRoundDTO> dateRoundList = qTransportInfoRepository.getSummaryDateRound(vehicleId, startDate);
        List<List<TransportInfoResponseDTO.DateRoundDTO>> groupList = dateRoundList.stream()
                .collect(Collectors.groupingBy(TransportInfoResponseDTO.DateRoundDTO::getRequestDate))
                .values().stream()
                .toList();

        for (List<TransportInfoResponseDTO.DateRoundDTO> dtoList : groupList) {
            TransportInfoResponseDTO.SummaryDTO dto = new TransportInfoResponseDTO.SummaryDTO();
            List<TransportInfoResponseDTO.RoundDTO> roundList = new ArrayList<>();

            LocalDate requestDate = dtoList.get(0).getRequestDate();
            for (TransportInfoResponseDTO.DateRoundDTO dateRound : dtoList) {
                TransportInfoResponseDTO.RoundDTO roundDTO = new TransportInfoResponseDTO.RoundDTO();
                int transportCount = transportInfoRepository.countDistinctByTransportCodeAndVehicleIdAndRequestDateEqualsAndTransportRoundEquals(
                        vehicleId, requestDate, dateRound.getTransportRound());

                List<TransportInfoDTO> list = transportInfoRepository.findByVehicleIdAndTransportRoundAndRequestDateOrderByTransportOrderAsc(vehicleId, dateRound.getTransportRound(), requestDate)
                        .stream()
                        .map(transportInfoMapper::toDto)
                        .toList();

                if (!list.isEmpty()) {
                    String shipmentKey = list.get(0).getShipmentKey();
                    list = getTransportByShipmentKey(shipmentKey);
                }

                boolean allMatch = list.stream()
                        .allMatch(d -> TransportConstant.COMPLETED_STATUS.contains(d.getTransportStatus()));
                if (allMatch) {
                    continue;
                }
                TransportInfoDTO filterDTO = list.stream()
                                .filter(d -> !TransportConstant.COMPLETED_STATUS.contains(d.getTransportStatus()))
                                .toList().get(0);

                TransportInfo.TransportStatus transportStatus = filterDTO.getTransportStatus();
                if (transportStatus == TransportInfo.TransportStatus.운송대기 && filterDTO.getTransportOrder() > 1) {
                    List<TransportInfoDTO> list2 = getTransportByShipmentKey(filterDTO.getShipmentKey());
                    transportStatus = list2.get(filterDTO.getTransportOrder() - 2).getTransportStatus();
                }
                roundDTO.setTransportRound(dateRound.getTransportRound());
                roundDTO.setTransportStatus(transportStatus);
                roundDTO.setTransportCount(transportCount);

                roundList.add(roundDTO);
            }
            if (roundList.isEmpty())
                continue;

            int[] transportCount = new int[roundList.size()];
            for (int round = 1; round <= roundList.size(); round++) {
                transportCount[round - 1] = roundList.get(round - 1).getTransportCount();
            }

            dto.setTransportCount(transportCount);

            dto.setRequestDate(requestDate);
            dto.setRoundCount(roundList.size());
            dto.setRoundList(roundList);

            transportInfoResponseBySummaryDTO.add(dto);
        }

        return transportInfoResponseBySummaryDTO.stream()
                .sorted(Comparator.comparing(TransportInfoResponseDTO.SummaryDTO::getRequestDate))
                .collect(Collectors.toList());
    }

    public TransportInfoResponseDTO.ByVehicleDTO findByVehicleIdAndRequestDate(final Long vehicleId, final int transportRound, final LocalDate requestDate) {
        List<TransportInfoDTO> list = transportInfoRepository.findByVehicleIdAndTransportRoundAndRequestDateOrderByTransportOrderAsc(vehicleId, transportRound, requestDate)
                .stream()
                .map(transportInfoMapper::toDto)
                .toList();

        if (!list.isEmpty()) {
            String shipmentKey = list.get(0).getShipmentKey();
            list = getTransportByShipmentKey(shipmentKey);
        }

        int pickup = 0;
        int dropoff = 0;
        int returning = 0;
        int shipyardDropoff = 0;
        int shipyardComplete = 0;
        int shipyardIncomplete = 0;
        int shipyardImpossible = 0;
        for (TransportInfoDTO item : list) {
            if (item.getTransportType().equals(TransportInfo.TransportType.상차))
                pickup++;
            else if (item.getTransportType().equals(TransportInfo.TransportType.하차))
                dropoff++;
            else if (item.getTransportType().equals(TransportInfo.TransportType.회수))
                returning++;
            else if (item.getTransportType().equals(TransportInfo.TransportType.반품))
                returning++;
            if (item.getDropOffArea() != null) {
                shipyardDropoff++;
                if (item.getImpossible() != null) {
                    shipyardImpossible++;
                } else {
                    if (item.getTransportStatus().equals(TransportInfo.TransportStatus.하차완료)
                        || item.getTransportStatus().equals(TransportInfo.TransportStatus.운송완료))
                        shipyardComplete++;
                    else
                        shipyardIncomplete++;
                }
            }
        }

        TransportInfoResponseDTO.ByVehicleDTO response = new TransportInfoResponseDTO.ByVehicleDTO();
        response.setTotalCount(list.size());
        response.setPickup(pickup);
        response.setDropoff(dropoff);
        response.setReturning(returning);

        response.setShipyardDropoff(shipyardDropoff);
        response.setShipyardComplete(shipyardComplete);
        response.setShipyardIncomplete(shipyardIncomplete);
        response.setShipyardImpossible(shipyardImpossible);

        response.setList(new ArrayList<>(list));

        return response;
    }

    public TransportInfoResponseDTO.ByMapDTO findRoute(final Long vehicleId, final int transportRound, final LocalDate requestDate, final Double gpsX, final Double gpsY) {
        TransportInfoResponseDTO.ByMapDTO mapDTO = new TransportInfoResponseDTO.ByMapDTO();

        List<TransportInfoDTO> list = transportInfoRepository.findByVehicleIdAndTransportRoundAndRequestDateOrderByTransportOrderAsc(vehicleId, transportRound, requestDate)
                .stream()
                .map(transportInfoMapper::toDto)
                .toList();

        if (!list.isEmpty()) {
            String shipmentKey = list.get(0).getShipmentKey();
            list = getTransportByShipmentKey(shipmentKey);
        }

        List<TransportInfoDTO> remainedList = list.stream()
                .filter(dto -> !dto.getTransportStatus().equals(TransportInfo.TransportStatus.상차완료))
                .filter(dto -> !dto.getTransportStatus().equals(TransportInfo.TransportStatus.하차완료))
                .filter(dto -> !dto.getTransportStatus().equals(TransportInfo.TransportStatus.회수완료))
                .filter(dto -> !dto.getTransportStatus().equals(TransportInfo.TransportStatus.운송완료))
                .toList();

        LbsRouteOnDemandResponse response = new LbsRouteOnDemandResponse();
        if (!remainedList.isEmpty()) {
            List<LbsRouteDestination> destinations = new ArrayList<>();
            for (TransportInfoDTO dto : remainedList) {
                LbsRouteDestination destination = LbsRouteDestination.builder()
                        .nId((long) dto.getTransportOrder())
                        .coordinate(Objects.requireNonNull(GeometryUtils.createPoint(dto.getCompanyLocation().getX(), dto.getCompanyLocation().getY())))
                        .build();
                destinations.add(destination);
            }

            final LbsRouteOnDemandRequest requestBody = LbsRouteOnDemandRequest.builder()
                    .requestId(Long.valueOf(NumberUtils.generateRandomNumeric(9)))
                    .start(Objects.requireNonNull(GeometryUtils.createPoint(gpsX, gpsY)))
                    .routeOption(LbsConstant.ROUTE_OPTION_DEFAULT)
                    .destinations(destinations)
                    .build();

            try {
                response = lbsRouteService.routeOnDemand(requestBody);
                mapDTO.setRoute(response.getRoutePathList().get(0));
            } catch (RuntimeException e) {
                log.warn(e.getMessage());
                return null;
            }
        }

        List<TransportInfoResponseDTO.VisitOrderDTO> visitOrderList = new ArrayList<>();
        for (TransportInfoDTO dto : list) {
            TransportInfoResponseDTO.VisitOrderDTO visitOrder = new TransportInfoResponseDTO.VisitOrderDTO();

            visitOrder.setTransportOrder(dto.getTransportOrder());
            visitOrder.setTransportType(dto.getTransportType());
            visitOrder.setTransportStatus(dto.getTransportStatus());
            visitOrder.setRequestTime(dto.getRequestTime());
            visitOrder.setQuantity(dto.getQuantity());
            visitOrder.setTransportCode(dto.getTransportCode());
            visitOrder.setCompanyName(dto.getCompanyName());
            visitOrder.setCompanyAddress(dto.getCompanyAddress());
            visitOrder.setCompanyLocation(dto.getCompanyLocation());

            if (!remainedList.isEmpty()) {
                response.getVisitList().forEach(visit -> {
                    if (visit.getNId() == dto.getTransportOrder()) {
                        visitOrder.setEstimate(visit.getEstimatedSecs());
                        visitOrder.setDist(visit.getEstimatedMeters());
                    }
                });
            }

            visitOrderList.add(visitOrder);
        }
        mapDTO.setVisitOrderList(visitOrderList);

         if (!remainedList.isEmpty()) {
             TransportInfoDTO dto = list.stream()
                     .filter(d -> TransportInfo.TransportType.하차.equals(d.getTransportType()) || TransportInfo.TransportType.반품.equals(d.getTransportType()))
                     .toList().get(0);

             List<?> result = callRouteService.findByVehicleRequestDateAndRound(vehicleId, dto.getRequestDate(), transportRound);
             if (result.isEmpty()) {
                 CallRouteCustomDTO.CallRouteInnerAddDTO addDTO = new CallRouteCustomDTO.CallRouteInnerAddDTO();
                 addDTO.setVehicleId(vehicleId);
                 addDTO.setTransportId(dto.getTransportId());
                 addDTO.setTransportRound(dto.getTransportRound());
                 addDTO.setShpmtky(dto.getShipmentKey());
                 addDTO.setRequestDate(dto.getRequestDate());

                 addDTO.setRoute(response.getRoutePathList().get(0));
                 int totalEstimatedMeters = response.getVisitList().stream()
                         .mapToInt(LbsRouteOnDemandResponse.LbsRouteOrderResult::getEstimatedMeters).sum();
                 int totalEstimatedSecs = response.getVisitList().stream()
                         .mapToInt(LbsRouteOnDemandResponse.LbsRouteOrderResult::getEstimatedSecs).sum();
                 addDTO.setExpectedDistance(totalEstimatedMeters);
                 addDTO.setExpectedTime(totalEstimatedSecs);

                 callRouteService.innerSave(addDTO);
             } else {
                 CallRouteCustomDTO.UpdateRouteOne updateDto = new CallRouteCustomDTO.UpdateRouteOne();

                 updateDto.setShpmtky(dto.getShipmentKey());
                 updateDto.setRoute(response.getRoutePathList().get(0));

                 int totalEstimatedMeters = response.getVisitList().stream()
                         .mapToInt(LbsRouteOnDemandResponse.LbsRouteOrderResult::getEstimatedMeters).sum();
                 int totalEstimatedSecs = response.getVisitList().stream()
                         .mapToInt(LbsRouteOnDemandResponse.LbsRouteOrderResult::getEstimatedSecs).sum();
                 updateDto.setExpectedDistance(totalEstimatedMeters);
                 updateDto.setExpectedTime(totalEstimatedSecs);

                 callRouteService.updateOne(updateDto);
             }
         }

        return mapDTO;
    }

    public TransportInfoResponseDTO.ByMapDTO findOnlyRoute(final Long vehicleId, final int transportRound, final LocalDate requestDate, final Double gpsX, final Double gpsY) {
        TransportInfoResponseDTO.ByMapDTO mapDTO = new TransportInfoResponseDTO.ByMapDTO();

        List<TransportInfoDTO> list = transportInfoRepository.findByVehicleIdAndTransportRoundAndRequestDateOrderByTransportOrderAsc(vehicleId, transportRound, requestDate)
                .stream()
                .map(transportInfoMapper::toDto)
                .toList();

        if (!list.isEmpty()) {
            String shipmentKey = list.get(0).getShipmentKey();
            list = getTransportByShipmentKey(shipmentKey);
        }

        LbsRouteOnDemandResponse response = new LbsRouteOnDemandResponse();
        if (!list.isEmpty()) {
            List<LbsRouteDestination> destinations = new ArrayList<>();
            for (TransportInfoDTO dto : list) {
                LbsRouteDestination destination = LbsRouteDestination.builder()
                        .nId((long) dto.getTransportOrder())
                        .coordinate(Objects.requireNonNull(GeometryUtils.createPoint(dto.getCompanyLocation().getX(), dto.getCompanyLocation().getY())))
                        .build();
                destinations.add(destination);
            }

            final LbsRouteOnDemandRequest requestBody = LbsRouteOnDemandRequest.builder()
                    .requestId(Long.valueOf(NumberUtils.generateRandomNumeric(9)))
                    .start(Objects.requireNonNull(GeometryUtils.createPoint(gpsX, gpsY)))
                    .routeOption(LbsConstant.ROUTE_OPTION_DEFAULT)
                    .destinations(destinations)
                    .build();

            try {
                response = lbsRouteService.routeOnDemand(requestBody);
                mapDTO.setRoute(response.getRoutePathList().get(0));
            } catch (RuntimeException e) {
                log.warn(e.getMessage());
                return null;
            }
        }

        return mapDTO;
    }

    @Transactional
    public TransportInfoDTO save(final TransportInfoUpdateDTO transportInfoUpdateDTO) {
        TransportInfo existEntity = transportInfoRepository.findByTransportCodeAndTransportType(transportInfoUpdateDTO.getTransportCode(), transportInfoUpdateDTO.getTransportType());
        if (Objects.nonNull(existEntity)) {
            log.info("TransportInfo save : Exception because {} already exists.", transportInfoUpdateDTO.getTransportCode());
            throw new RuntimeException(transportInfoUpdateDTO.getTransportCode() + " Already exists.");
        }

        Optional<DriverVehicleInfoDTO.DriverVehicleInfoCustom> vehicle = driverVehicleInfoService.findVehliceInfoByVehicleId(transportInfoUpdateDTO.getVehicleId());
        vehicle.ifPresent(s -> pushService.sendNewTransport(s.getUserasq()));

        TransportInfo entity = appendAddressAndLocation(transportInfoUpdateDTO);
        TransportInfo savedEntity = transportInfoRepository.save(entity);

        return transportInfoMapper.toDto(savedEntity);
    }

    @Transactional
    public int saveAll(List<TransportInfoUpdateDTO> transportInfoUpdateDTOs) {
        List<TransportInfo> entities = new ArrayList<>();
        for (TransportInfoUpdateDTO updateDTO : transportInfoUpdateDTOs) {
            TransportInfo existEntity = transportInfoRepository.findByTransportCodeAndTransportType(updateDTO.getTransportCode(), updateDTO.getTransportType());
            if (Objects.nonNull(existEntity)) {
                log.info("TransportInfo save : Exception because {} already exists.", updateDTO.getTransportCode());
                throw new RuntimeException(updateDTO.getTransportCode() + " Already exists.");
            }

            TransportInfo entity = appendAddressAndLocation(updateDTO);
            entities.add(entity);
        }
        transportInfoRepository.saveAll(entities);

        Optional<DriverVehicleInfoDTO.DriverVehicleInfoCustom> vehicle = driverVehicleInfoService.findVehliceInfoByVehicleId(transportInfoUpdateDTOs.get(0).getVehicleId());
        vehicle.ifPresent(s -> pushService.sendNewTransport(s.getUserasq()));

        return entities.size();
    }

    @Transactional
    public int saveByShipment(TransportInfoUpdateByDispatch.ByShipmentDTO updateByShipmentDTO, LbsRouteOnDemandResponse lbsResponse) {
        List<TransportInfo> entities = new ArrayList<>();

        List<TransportInfoDTO> infoDTOList = this.getTransportByShipmentKey(updateByShipmentDTO.getShipmentKey());
        if (infoDTOList.isEmpty()) {
            List<ShipmentSectionDTO> shipmentDTOList = shipmentSectionService.getShipmentSectionListByShipment(
                    updateByShipmentDTO.getCompkey(), updateByShipmentDTO.getShipmentKey());

            for (ShipmentSectionDTO shipmentDTO : shipmentDTOList) {
                TransportInfoUpdateDTO updateDTO = new TransportInfoUpdateDTO();

                updateDTO.setShipmentKey(updateByShipmentDTO.getShipmentKey());
                updateDTO.setDispatchId(updateByShipmentDTO.getDispatchId());
                updateDTO.setVehicleId(updateByShipmentDTO.getVehicleId());
                updateDTO.setTransportRound(updateByShipmentDTO.getTransportRound());
                updateDTO.setTransportCode(shipmentDTO.getOetmsky());
                updateDTO.setPartnerKey(updateByShipmentDTO.getPartnerKey());

                TransportInfo entity = setUpdateDTO(shipmentDTO, updateDTO, lbsResponse);
                entities.add(entity);
            }
            transportInfoRepository.saveAll(entities);
        } else {
            log.info("Skip because {} already exists.", updateByShipmentDTO.getShipmentKey());
        }
        return entities.size();
    }

    private TransportInfo setUpdateDTO(ShipmentSectionDTO shipmentDTO, TransportInfoUpdateDTO updateDTO, LbsRouteOnDemandResponse lbsResponse) {
        updateDTO.setTransportOrder(shipmentDTO.getVstordr());

        TransportInfo.TransportType transportType;
        VisitTypeEnum visitTypeEnum = shipmentDTO.getVsttype();
        Boolean isReturn = shipmentDTO.getIsreturn();
        if (visitTypeEnum == VisitTypeEnum.PICKUP) {
            transportType =isReturn ? TransportInfo.TransportType.회수 : TransportInfo.TransportType.상차;
        } else {
            transportType =isReturn ? TransportInfo.TransportType.반품 : TransportInfo.TransportType.하차;
        }
        updateDTO.setTransportType(transportType);

        updateDTO.setTransportStatus(TransportInfo.TransportStatus.운송대기);
        updateDTO.setCompanyName(shipmentDTO.getPtnamlc());
        if (shipmentDTO.getPtnrtyp().equals(PartnerTypeEnum.CUSTOMER)) {
            updateDTO.setDropOffArea(shipmentDTO.getDenamlc());
        }
        updateDTO.setCompanyAddress(shipmentDTO.getPtaddr());
        updateDTO.setCompanyLocation(shipmentDTO.getDecoord());

        updateDTO.setRequestDate(shipmentDTO.getLudrqdt());
        updateDTO.setRequestTime(shipmentDTO.getLudrqtm());

        int requiredTime = Objects.nonNull(shipmentDTO.getLudptim()) ? shipmentDTO.getLudptim() : 0;
        List<LbsRouteOnDemandResponse.LbsRouteOrderResult> visitList = lbsResponse.getVisitList().stream()
                .filter(d -> d.getVisitOrder().equals(shipmentDTO.getVstordr()))
                .toList();
        if (!visitList.isEmpty()) {
            requiredTime += (int) Math.round(visitList.get(0).getEstimatedSecs() / 60.0);
        }
        updateDTO.setRequiredTime(requiredTime);

        updateDTO.setQuantity(shipmentDTO.getOpkcate() + " / " + shipmentDTO.getOpakqty() + "ea");
        updateDTO.setRestriction(shipmentDTO.getUnavat1());
        updateDTO.setContactName(shipmentDTO.getTrctname());
        updateDTO.setContactNumber(shipmentDTO.getTrctnum());

        return appendAddressAndLocation(updateDTO);
    }

    @Transactional
    public void saveByTransportCode(TransportInfoUpdateByDispatch.ByTransportCodeDTO updateByTransportCodeDTO, LbsRouteOnDemandResponse lbsResponse) {
        List<TransportInfoDTO> infoDTOList = getTransportByShipmentKeyAndTransportCode(updateByTransportCodeDTO.getShipmentKey(), updateByTransportCodeDTO.getTransportCode());
        if (infoDTOList.isEmpty()) {
            List<TransportInfo> entities = new ArrayList<>();

            List<ShipmentSectionDTO> shipmentDTOList = shipmentSectionService.getShipmentSectionListByShipment(
                            updateByTransportCodeDTO.getCompkey(), updateByTransportCodeDTO.getShipmentKey()).stream()
                    .filter(s -> s.getOetmsky().equals(updateByTransportCodeDTO.getTransportCode()))
                    .toList();

            for (ShipmentSectionDTO shipmentDTO : shipmentDTOList) {
                TransportInfoUpdateDTO updateDTO = new TransportInfoUpdateDTO();

                updateDTO.setShipmentKey(updateByTransportCodeDTO.getShipmentKey());
                updateDTO.setDispatchId(updateByTransportCodeDTO.getDispatchId());
                updateDTO.setVehicleId(updateByTransportCodeDTO.getVehicleId());
                updateDTO.setTransportRound(updateByTransportCodeDTO.getTransportRound());
                updateDTO.setTransportCode(updateByTransportCodeDTO.getTransportCode());
                updateDTO.setPartnerKey(updateByTransportCodeDTO.getPartnerKey());
                if (shipmentDTO.getVsttype() == VisitTypeEnum.PICKUP)
                    updateDTO.setTransportOrder(updateByTransportCodeDTO.getPickupOrder());
                else
                    updateDTO.setTransportOrder(updateByTransportCodeDTO.getDropOffOrder());

                TransportInfo entity = setUpdateDTO2(shipmentDTO, updateDTO, lbsResponse);
                TransportInfoDTO dto = transportInfoMapper.toDto(entity);
                if (dto.getTransportType().equals(TransportInfo.TransportType.상차) || dto.getTransportType().equals(TransportInfo.TransportType.회수))
                    dto.setTransportOrder(updateByTransportCodeDTO.getPickupOrder());
                else
                    dto.setTransportOrder(updateByTransportCodeDTO.getDropOffOrder());
                entities.add(transportInfoMapper.toEntity(dto));
            }
            transportInfoRepository.saveAll(entities);
        } else {
            log.info("Skip because {} - {} already exists.", updateByTransportCodeDTO.getShipmentKey(), updateByTransportCodeDTO.getTransportCode());
        }
    }

    private TransportInfo setUpdateDTO2(ShipmentSectionDTO shipmentDTO, TransportInfoUpdateDTO updateDTO, LbsRouteOnDemandResponse lbsResponse) {
        TransportInfo.TransportType transportType;
        VisitTypeEnum visitTypeEnum = shipmentDTO.getVsttype();
        Boolean isReturn = shipmentDTO.getIsreturn();
        if (visitTypeEnum == VisitTypeEnum.PICKUP) {
            transportType =isReturn ? TransportInfo.TransportType.회수 : TransportInfo.TransportType.상차;
        } else {
            transportType =isReturn ? TransportInfo.TransportType.반품 : TransportInfo.TransportType.하차;
        }
        updateDTO.setTransportType(transportType);

        updateDTO.setTransportStatus(TransportInfo.TransportStatus.운송대기);
        updateDTO.setCompanyName(shipmentDTO.getPtnamlc());
        if (shipmentDTO.getPtnrtyp().equals(PartnerTypeEnum.CUSTOMER)) {
            updateDTO.setDropOffArea(shipmentDTO.getDenamlc());
        }
        updateDTO.setCompanyAddress(shipmentDTO.getPtaddr());
        updateDTO.setCompanyLocation(shipmentDTO.getDecoord());

        updateDTO.setRequestDate(shipmentDTO.getLudrqdt());
        updateDTO.setRequestTime(shipmentDTO.getLudrqtm());

        int requiredTime = Objects.nonNull(shipmentDTO.getLudptim()) ? shipmentDTO.getLudptim() : 0;
        List<LbsRouteOnDemandResponse.LbsRouteOrderResult> visitList = lbsResponse.getVisitList().stream()
                .filter(d -> d.getVisitOrder().equals(updateDTO.getTransportOrder()))
                .toList();
        if (!visitList.isEmpty()) {
            requiredTime += (int) Math.round(visitList.get(0).getEstimatedSecs() / 60.0);
        }
        updateDTO.setRequiredTime(requiredTime);

        updateDTO.setQuantity(shipmentDTO.getOpkcate() + " / " + shipmentDTO.getOpakqty() + "ea");
        updateDTO.setRestriction(shipmentDTO.getUnavat1());
        updateDTO.setContactName(shipmentDTO.getTrctname());
        updateDTO.setContactNumber(shipmentDTO.getTrctnum());

        return appendAddressAndLocation(updateDTO);
    }

    public TransportInfo appendAddressAndLocation(TransportInfoUpdateDTO transportInfoUpdateDTO) {
        TransportInfoDTO transportInfoDTO = new TransportInfoDTO(transportInfoUpdateDTO);

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            transportInfoDTO.setCreuser(authDTO.getId());
            transportInfoDTO.setLmouser(" ");
        }

        if (transportInfoDTO.getCompanyAddress() == null) {
            MptnmaAddressResponseDTO mptnmaAddressResponseDTO = mptnmaService.findByCompanyName(transportInfoDTO.getCompanyName());
            if (mptnmaAddressResponseDTO != null) {
                String address = transportInfoDTO.getCompanyAddress();
                if (address == null || address.trim().isEmpty()) {
                    address = mptnmaAddressResponseDTO.getPtaddr1();
                    if (address == null || address.trim().isEmpty()) {
                        address = mptnmaAddressResponseDTO.getPtaddr2();
                    }
                    transportInfoDTO.setCompanyAddress(address);
                }
                if (mptnmaAddressResponseDTO.getPtnrlat().compareTo(BigDecimal.ZERO) != 0) {
                    Point point = GeometryUtils.createPoint(mptnmaAddressResponseDTO.getPtnrlon().setScale(6, RoundingMode.HALF_UP),
                            mptnmaAddressResponseDTO.getPtnrlat().setScale(6, RoundingMode.HALF_UP));
                    transportInfoDTO.setCompanyLocation(point);
                }
            }
        }

        return transportInfoMapper.toEntity(transportInfoDTO);
    }

    @Transactional
    public TransportInfoDTO modifyTransportStatus(final Long transportId, final TransportInfo.TransportStatus transportStatus,
                                                  final Float latitude, final Float longitude) {
        TransportInfo oldEntity = transportInfoRepository.findByTransportId(transportId);
        TransportInfoDTO dto = transportInfoMapper.toDto(oldEntity);

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            dto.setLmouser(authDTO.getId());
        }

        TransportInfo.TransportStatus newStatus = transportStatus;
        if (transportStatus.equals(TransportInfo.TransportStatus.하차완료)) {
            List<TransportInfoDTO> infoDTOList = getTransportByShipmentKey(dto.getShipmentKey());
            boolean allCompleted = infoDTOList.stream()
                    .filter(d -> !d.getTransportId().equals(transportId))
                    .allMatch(d -> TransportConstant.COMPLETED_STATUS.contains(d.getTransportStatus()));
            boolean hasCompleted = infoDTOList.stream()
                    .anyMatch(d -> TransportInfo.TransportStatus.운송완료.equals(d.getTransportStatus()));
            if (allCompleted && !hasCompleted) {
                newStatus = TransportInfo.TransportStatus.운송완료;
                qRealTrackingRepository.updateDeliveryCompleted(dto.getShipmentKey());
            }
        }
        if (Objects.nonNull(dto.getDropOffArea()) ) {
            if (newStatus.equals(TransportInfo.TransportStatus.조선소로이동중) ||
                    newStatus.equals(TransportInfo.TransportStatus.조선소입문) ||
                    newStatus.equals(TransportInfo.TransportStatus.입문불가) ||
                    newStatus.equals(TransportInfo.TransportStatus.운송완료)) {
                List<TransportInfoDTO> shipyardList = transportInfoRepository.findByVehicleIdAndTransportRoundAndRequestDateOrderByTransportOrderAsc(
                                dto.getVehicleId(), dto.getTransportRound(), dto.getRequestDate())
                        .stream()
                        .map(transportInfoMapper::toDto)
                        .filter(transport -> Objects.nonNull(transport.getDropOffArea()))
                        .filter(transport -> !Objects.equals(transport.getTransportId(), transportId))
                        .toList();
                for (TransportInfoDTO item : shipyardList) {
                    if (item.getTransportStatus().equals(TransportInfo.TransportStatus.하차불가))
                        continue;
                    if (Objects.nonNull(authDTO)) {
                        item.setLmouser(authDTO.getId());
                    }

                    item.setTransportStatus(transportStatus);
                    TransportInfo entity = transportInfoMapper.toEntity(item);
                    transportInfoRepository.save(entity);
                }
            }
        }
        switch (newStatus) {
            case 운송시작, 상차지로이동중, 회수지로이동중 -> qTransportInfoRepository.updateTransportVehicleStatus(dto.getTransportCode(), VehicleStatusEnum.START);
            case 상차지도착, 회수지도착 -> qTransportInfoRepository.updateTransportVehicleStatus(dto.getTransportCode(), VehicleStatusEnum.LOADARR);
            case 상차완료, 회수완료 -> qTransportInfoRepository.updateTransportOrderStatus(dto.getTransportCode(), OrderStatusEnum.LOAD);
//            case 조선소입문, 하차지도착, 반품지도착 -> qTransportInfoRepository.updateTransportVehicleStatus(dto.getTransportCode(), VehicleStatusEnum.UNLOADARR);
            case 조선소입문 -> qTransportInfoRepository.updateTransportVehicleStatusShipyard(dto.getTransportCode(), VehicleStatusEnum.UNLOADARR);
            case 하차지도착, 반품지도착 -> qTransportInfoRepository.updateTransportVehicleStatus(dto.getTransportCode(), VehicleStatusEnum.UNLOADARR);
            case 하차완료 -> qTransportInfoRepository.updateTransportOrderStatus(dto.getTransportCode(), OrderStatusEnum.UNLOAD);
        }
        LocalDateTime nowTime = LocalDateTime.now();
        int reqTime = dto.getRequiredTime();
        LocalDateTime resultTime = nowTime.plusMinutes(reqTime);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        String formatTime = resultTime.format(formatter);
        String startTime = nowTime.format(formatter);

        switch (newStatus) {
            case 운송시작, 상차지로이동중, 회수지로이동중 -> {
                String username = "", telphnm = "";
                Optional<DriverInfoDTO.DriverAllInfo> driverInfoCustomDTO = driverInfoCustomService.findByUseract(authDTO.getId());

                if (driverInfoCustomDTO.isPresent()) {
                    username = driverInfoCustomDTO.get().getUsernam();
                    telphnm = driverInfoCustomDTO.get().getTelphnm();
                }

                //Optional<DriverInfoDTO.DriverDetailInfo> driverDetailInfo =
                //        driverInfoCustomService.findByUserasqWithDriverHolidayInfo(Objects.requireNonNull(dto).getDriverId());
                List<String> pickupKeyList = shipmentSectionService.getShipmentSectionListByShipment(dto.getCompkey(), dto.getShipmentKey()).stream()
                        .filter(d -> d.getOetmsky().equals(dto.getTransportCode()) && d.getVsttype() == VisitTypeEnum.PICKUP)
                        .map(ShipmentSectionDTO::getPtnrkey)
                        .toList();
                if (!pickupKeyList.isEmpty()) {
                    tmsToVmsService.postLoadUnloadMove(dto.getShipmentKey(), username, telphnm, formatTime, pickupKeyList.get(0), true);
                }
            }
            case 하차지로이동중, 반품지로이동중 -> {
                String username = "", telphnm = "";
                Optional<DriverInfoDTO.DriverAllInfo> driverInfoCustomDTO = driverInfoCustomService.findByUseract(authDTO.getId());

                if (driverInfoCustomDTO.isPresent()) {
                    username = driverInfoCustomDTO.get().getUsernam();
                    telphnm = driverInfoCustomDTO.get().getTelphnm();
                }
                List<String> dropOffKeyList = shipmentSectionService.getShipmentSectionListByShipment(dto.getCompkey(), dto.getShipmentKey()).stream()
                        .filter(d -> d.getOetmsky().equals(dto.getTransportCode()) && d.getVsttype() == VisitTypeEnum.DROPOFF)
                        .map(ShipmentSectionDTO::getPtnrkey)
                        .toList();
                if (!dropOffKeyList.isEmpty()) {
                    tmsToVmsService.postLoadUnloadMove(dto.getShipmentKey(), username, telphnm, formatTime, dropOffKeyList.get(0), false);
                }
            }
            case 조선소로이동중 -> {

                String username = "", telphnm = "";
                Optional<DriverInfoDTO.DriverAllInfo> driverInfoCustomDTO = driverInfoCustomService.findByUseract(authDTO.getId());

                if (driverInfoCustomDTO.isPresent()) {
                    username = driverInfoCustomDTO.get().getUsernam();
                    telphnm = driverInfoCustomDTO.get().getTelphnm();
                }

                // shipmentkey로 해당되는 주문번호 조회
                String eta = LocalDate.now().toString() + " " + LocalTime.now().toString();
                // 해당 shipment에 포함된 모든 주문에 대한 정보 조회
                List<OetmhdPushDTO> oetm = oetmhdService.findShipyardByShipmentkey(dto.getShipmentKey());
                // 조선소,하차지,인수자,인수자연락처 로 group
                Map<String, List<OetmhdPushDTO>> oetmMap = oetm.stream()
                        .collect(Collectors.groupingBy(o -> o.getDenamlc() + "|" + o.getRecinm()));
                for(Map.Entry<String, List<OetmhdPushDTO>> entry : oetmMap.entrySet()) {
                    String key = entry.getKey();
                    String partnerName = "";
                    List<OetmhdPushDTO> value = entry.getValue();
                    OetmhdPushDTO returnDTO = new OetmhdPushDTO(value.get(0));
                    for(OetmhdPushDTO pushDTO : value) {
                        partnerName = partnerName.concat(pushDTO.getPtnamlc());
                    }
                    returnDTO.setPtnamlc(partnerName);
                    returnDTO.setDrivernm(username);
                    returnDTO.setDriverTel(telphnm);

                    tmsToVmsService.postGoingShipyard(startTime, returnDTO);
                }

                // 조선소,하차지,상하역장비 로 group
                Map<String, List<OetmhdPushDTO>> oetmEquipMap = oetm.stream()
                        .collect(Collectors.groupingBy(o -> o.getDenamlc() + "|" + o.getEquipment()));
                for(Map.Entry<String, List<OetmhdPushDTO>> entry : oetmEquipMap.entrySet()) {
                    String key = entry.getKey();
                    String partnerName = "", equipment = "";
                    List<OetmhdPushDTO> value = entry.getValue();
                    OetmhdPushDTO returnDTO = new OetmhdPushDTO(value.get(0));
                    for(OetmhdPushDTO pushDTO : value) {
                        partnerName = partnerName.concat(pushDTO.getPtnamlc());
                        if(pushDTO.getEquipment() != null && !pushDTO.getEquipment().trim().isEmpty()) {
                            equipment = equipment.concat(pushDTO.getEquipment());
                        }
                    }
                    returnDTO.setPtnamlc(partnerName);
                    returnDTO.setDrivernm(username);
                    returnDTO.setDriverTel(telphnm);
                    returnDTO.setEquipment(equipment);

                    if(equipment.length() > 2) {
                        tmsToVmsService.postGoingShipyardWithEquipment(startTime, formatTime, returnDTO);
                    }
                }
            }
            case 하차완료 -> {
                String oetmsky = dto.getTransportCode();

                String username = "", telphnm = "";
                Optional<DriverInfoDTO.DriverAllInfo> driverInfoCustomDTO = driverInfoCustomService.findByUseract(authDTO.getId());

                if (driverInfoCustomDTO.isPresent()) {
                    username = driverInfoCustomDTO.get().getUsernam();
                    telphnm = driverInfoCustomDTO.get().getTelphnm();
                }

                // shipmentkey로 해당되는 주문번호 조회
                String eta = LocalDate.now().toString() + " " + LocalTime.now().toString();
                // 해당 oetmsky에 대한 정보 조회
                OetmhdPushDTO oetm = oetmhdService.findShipyardByOetmsky(dto.getTransportCode());

                // 조회된 결과가 있으면 조선소나 사외제작사이다
                if(oetm != null) {
                    OetmhdPushDTO returnDTO = new OetmhdPushDTO(oetm);
                    returnDTO.setDrivernm(username);
                    returnDTO.setDriverTel(telphnm);

                    tmsToVmsService.postFinishShipyard(startTime, formatTime, returnDTO);
                }
            }
        }

        dto.setTransportStatus(newStatus);
        TransportInfo newEntity = transportInfoMapper.toEntity(dto);
        TransportInfo savedEntity = transportInfoRepository.save(newEntity);
        return transportInfoMapper.toDto(savedEntity);
    }

    @Transactional
    public TransportInfoDTO modifyStatusByManager(final String transportCode,
                                                  final TransportInfo.TransportType transportType,
                                                  final TransportInfo.TransportStatus transportStatus,
                                                  final String reason) {
        TransportInfo oldEntity = transportInfoRepository.findByTransportCodeAndTransportType(transportCode, transportType);
        TransportInfoDTO dto = transportInfoMapper.toDto(oldEntity);

        if (transportStatus.equals(TransportInfo.TransportStatus.상차불가) ||
                transportStatus.equals(TransportInfo.TransportStatus.하차불가) ||
                transportStatus.equals(TransportInfo.TransportStatus.회수불가) ||
                transportStatus.equals(TransportInfo.TransportStatus.반품불가) ||
                transportStatus.equals(TransportInfo.TransportStatus.입문불가)) {
            return addTransportImpossible(dto.getTransportId(), reason, true);
        }

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            dto.setLmouser(authDTO.getId());
        }

        if (Objects.nonNull(dto.getDropOffArea()) ) {
            if (transportStatus.equals(TransportInfo.TransportStatus.조선소입문) ||
                    transportStatus.equals(TransportInfo.TransportStatus.운송완료)) {
                List<TransportInfoDTO> shipyardList = transportInfoRepository.findByVehicleIdAndTransportRoundAndRequestDateOrderByTransportOrderAsc(
                                dto.getVehicleId(), dto.getTransportRound(), dto.getRequestDate())
                        .stream()
                        .map(transportInfoMapper::toDto)
                        .filter(transport -> Objects.nonNull(transport.getDropOffArea()))
                        .filter(transport -> !Objects.equals(transport.getTransportCode(), transportCode))
                        .toList();
                for (TransportInfoDTO item : shipyardList) {
                    if (Objects.nonNull(authDTO)) {
                        item.setLmouser(authDTO.getId());
                    }

                    item.setTransportStatus(transportStatus);
                    item.setChangeReason(reason);
                    TransportInfo entity = transportInfoMapper.toEntity(item);
                    transportInfoRepository.save(entity);
                }
            }
        }
        TransportInfo.TransportStatus newStatus = transportStatus;
        if (transportStatus.equals(TransportInfo.TransportStatus.하차완료)) {
            List<TransportInfoDTO> infoDTOList = getTransportByShipmentKey(dto.getShipmentKey());
            boolean allCompleted = infoDTOList.stream()
                    .filter(d -> !d.getTransportId().equals(dto.getTransportId()))
                    .allMatch(d -> TransportConstant.COMPLETED_STATUS.contains(d.getTransportStatus()));
            boolean hasCompleted = infoDTOList.stream()
                    .anyMatch(d -> TransportInfo.TransportStatus.운송완료.equals(d.getTransportStatus()));
            if (allCompleted && !hasCompleted) {
                newStatus = TransportInfo.TransportStatus.운송완료;
                qRealTrackingRepository.updateDeliveryCompleted(dto.getShipmentKey());
            }
        }

        dto.setTransportStatus(newStatus);
        dto.setChangeReason(reason);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yy/MM/dd");
        String formattedDate = dto.getRequestDate().format(formatter);
        String pushData = "(" + formattedDate + " " + dto.getTransportRound() + "회차 - " + dto.getCompanyName() + ")";
        Optional<DriverVehicleInfoDTO.DriverVehicleInfoCustom> vehicle = driverVehicleInfoService.findVehliceInfoByVehicleId(dto.getVehicleId());
        vehicle.ifPresent(s -> {
            switch (transportStatus) {
                case 상차완료:
                case 하차완료:
                case 회수완료:
                case 운송완료:
                    pushService.sendTransportCompleted(s.getUserasq(), pushData);
                    break;
            }
        });
        switch (newStatus) {
            case 상차완료, 회수완료 -> qTransportInfoRepository.updateTransportOrderStatus(dto.getTransportCode(), OrderStatusEnum.LOAD);
            case 하차완료 -> qTransportInfoRepository.updateTransportOrderStatus(dto.getTransportCode(), OrderStatusEnum.UNLOAD);
        }

        TransportInfo newEntity = transportInfoMapper.toEntity(dto);
        TransportInfo savedEntity = transportInfoRepository.save(newEntity);
        return transportInfoMapper.toDto(savedEntity);
    }

    @Transactional
    public void unablePickup(final String transportCode) {
        TransportInfo oldEntity = transportInfoRepository.findByTransportCodeAndTransportType(transportCode, TransportInfo.TransportType.상차);
        TransportInfoDTO dto = transportInfoMapper.toDto(oldEntity);

        switch (dto.getTransportStatus()) {
            case 운송시작:
            case 상차지로이동중:
            case 상차지도착:
                break;
            default:
                throw new TransportException(HttpStatus.BAD_REQUEST, "상차불가 처리할 수 있는 운행 상태가 아닙니다.");
        }

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            dto.setLmouser(authDTO.getId());
        }
        dto.setTransportStatus(TransportInfo.TransportStatus.상차불가);

        TransportInfo newEntity = transportInfoMapper.toEntity(dto);
        transportInfoRepository.save(newEntity);
    }

    @Transactional
    public List<TransportInfoDTO> modifyTransportOrder(final List<Long> transportIds) {
        List<TransportInfoDTO> dtos = transportInfoRepository.findByTransportIdIn(transportIds).stream()
                .map(transportInfoMapper::toDto)
                .collect(Collectors.toList());

        List<Integer> orders = new ArrayList<>();
        for (TransportInfoDTO dto : dtos) {
            orders.add(dto.getTransportOrder());
        }
        Collections.sort(orders);

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();

        List<TransportInfo> entities = new ArrayList<>();
        int index = 0;
        for (Long transportId : transportIds) {
            TransportInfoDTO dto = dtos.stream().filter(transport -> Objects.equals(transport.getTransportId(), transportId))
                    .findFirst().orElse(null);
            if (Objects.isNull(dto))
                continue;
            Objects.requireNonNull(dto).setTransportOrder(orders.get(index));

            if (!dto.getTransportStatus().equals(TransportInfo.TransportStatus.조선소입문)
                    && !TransportConstant.COMPLETED_STATUS.contains(dto.getTransportStatus())) {
                Objects.requireNonNull(dto).setTransportStatus(TransportInfo.TransportStatus.운송대기);
            }

            if (Objects.nonNull(authDTO)) {
                Objects.requireNonNull(dto).setLmouser(authDTO.getId());
            }
            index++;
            TransportInfo entity = transportInfoMapper.toEntity(dto);
            entities.add(entity);
        }
        transportInfoRepository.saveAll(entities);

        dtos.sort(Comparator.comparingInt(TransportInfoDTO::getTransportOrder));
        return dtos;
    }

    @Transactional
    public void modifyRequiredTime(TransportInfoDTO dto) {
        List<TransportInfoDTO> list = transportInfoRepository.findByVehicleIdAndTransportRoundAndRequestDateOrderByTransportOrderAsc(
                        dto.getVehicleId(), dto.getTransportRound(), dto.getRequestDate()).stream()
                .map(transportInfoMapper::toDto)
                .toList();
        if (!list.isEmpty()) {
            String shipmentKey = list.get(0).getShipmentKey();
            list = getTransportByShipmentKey(shipmentKey);
        }

        List<LbsRouteDestination> destinations = new ArrayList<>();
        for (TransportInfoDTO d : list) {
            LbsRouteDestination destination = LbsRouteDestination.builder()
                    .nId((long) d.getTransportOrder())
                    .coordinate(Objects.requireNonNull(GeometryUtils.createPoint(d.getCompanyLocation().getX(), d.getCompanyLocation().getY())))
                    .build();
            destinations.add(destination);
        }

        final LbsRouteOnDemandRequest requestBody = LbsRouteOnDemandRequest.builder()
                .requestId(Long.valueOf(NumberUtils.generateRandomNumeric(9)))
                .start(Objects.requireNonNull(GeometryUtils.createPoint(dto.getCompanyLocation().getX(), dto.getCompanyLocation().getY())))
                .routeOption(LbsConstant.ROUTE_OPTION_DEFAULT)
                .destinations(destinations)
                .build();

        try {
            LbsRouteOnDemandResponse lbsResponse = lbsRouteService.routeOnDemand(requestBody);
            List<TransportInfo> entities = new ArrayList<>();
            for (TransportInfoDTO infoDTO : list) {
                int requiredTime = 0;
                List<LbsRouteOnDemandResponse.LbsRouteOrderResult> visitList = lbsResponse.getVisitList().stream()
                        .filter(d -> d.getVisitOrder().equals(infoDTO.getTransportOrder()))
                        .toList();
                if (!visitList.isEmpty()) {
                    requiredTime += (int) Math.round(visitList.get(0).getEstimatedSecs() / 60.0);
                }
                infoDTO.setRequiredTime(requiredTime);
                entities.add(transportInfoMapper.toEntity(infoDTO));
            }
            transportInfoRepository.saveAll(entities);

            CallRouteCustomDTO.UpdateRouteOne updateDto = new CallRouteCustomDTO.UpdateRouteOne();
            updateDto.setShpmtky(dto.getShipmentKey());
            updateDto.setRoute(lbsResponse.getRoutePathList().get(0));
            int totalEstimatedMeters = lbsResponse.getVisitList().stream()
                    .mapToInt(LbsRouteOnDemandResponse.LbsRouteOrderResult::getEstimatedMeters).sum();
            int totalEstimatedSecs = lbsResponse.getVisitList().stream()
                    .mapToInt(LbsRouteOnDemandResponse.LbsRouteOrderResult::getEstimatedSecs).sum();
            updateDto.setExpectedDistance(totalEstimatedMeters);
            updateDto.setExpectedTime(totalEstimatedSecs);
           callRouteService.updateOne(updateDto);

        } catch (RuntimeException e) {
            log.warn(e.getMessage());
        }
    }

    @Transactional
    public TransportInfoDTO modifySign(final Long transportId, MultipartFile file) {
        TransportInfo oldEntity = transportInfoRepository.findByTransportId(transportId);
        TransportInfoDTO dto = transportInfoMapper.toDto(oldEntity);

        if (fileStorage.deleteFile(dto.getSignUrl())) {
            log.info("기존 서명 파일 제거: " + dto.getSignUrl());
        }

        String uploadFilename = file.getOriginalFilename();
        String uploadPath = fileStorage.getUploadFilePath("transportSign");
        uploadPath += transportId;
        fileStorage.createFolder(uploadPath);
        String uploadFullName = uploadPath + "/" + uploadFilename;
        fileStorage.uploadStreaming(file, uploadFullName);

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            dto.setLmouser(authDTO.getId());
        }

        dto.setSignUrl(uploadFullName);
        TransportInfo newEntity = transportInfoMapper.toEntity(dto);
        TransportInfo savedEntity = transportInfoRepository.save(newEntity);
        return transportInfoMapper.toDto(savedEntity);
    }

    @Transactional
    public TransportInfoDTO addTransportImpossible(final Long transportId, final String impossible, final Boolean newPickUp) {
        TransportInfo oldEntity = transportInfoRepository.findByTransportId(transportId);
        TransportInfoDTO dto = transportInfoMapper.toDto(oldEntity);
        TransportInfo.TransportType transportType = dto.getTransportType();
        TransportInfo.TransportStatus newStatus = processStatusAfterImpossible(transportType);

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            dto.setLmouser(authDTO.getId());
        }

        if (Objects.nonNull(dto.getDropOffArea()) && dto.getTransportStatus() != TransportInfo.TransportStatus.조선소입문) {
            newStatus = TransportInfo.TransportStatus.입문불가;

            if (Objects.nonNull(authDTO)) {
                TransportInfoDTO transportInfoDTO = findByTransportId(transportId);
                String regNumber = null;
                Optional<DriverInfoDTO.DriverAllInfo> driverInfoCustomDTO = driverInfoCustomService.findByUseract(authDTO.getId());
                if(driverInfoCustomDTO.isPresent()) {
                    regNumber = driverInfoCustomDTO.get().getRegistrationNumber();
                }
                tmsToVmsService.postEntryImpossible(transportInfoDTO.getTransportCode(), regNumber);
            }

            List<TransportInfoDTO> shipyardList = transportInfoRepository.findByVehicleIdAndTransportRoundAndRequestDateOrderByTransportOrderAsc(
                            dto.getVehicleId(), dto.getTransportRound(), dto.getRequestDate())
                    .stream()
                    .map(transportInfoMapper::toDto)
                    .filter(transport -> Objects.nonNull(transport.getDropOffArea()))
                    .filter(transport -> !Objects.equals(transport.getTransportId(), transportId))
                    .toList();
            for (TransportInfoDTO item : shipyardList) {
                if (item.getTransportStatus().equals(TransportInfo.TransportStatus.하차불가))
                    continue;
                item.setImpossible(impossible);
                item.setNewPickUp(newPickUp);
                item.setTransportStatus(newStatus);
                if (Objects.nonNull(authDTO)) {
                    item.setLmouser(authDTO.getId());
                }
                TransportInfo entity = transportInfoMapper.toEntity(item);
                transportInfoRepository.save(entity);
            }
        }
        if (newStatus == TransportInfo.TransportStatus.상차불가) {
            List<TransportInfoDTO> dropOffList = transportInfoRepository.findByTransportCode(dto.getTransportCode())
                    .stream()
                    .map(transportInfoMapper::toDto)
                    .filter(transport -> Objects.equals(transport.getTransportType(), TransportInfo.TransportType.하차))
                    .filter(transport -> !Objects.equals(transport.getTransportId(), transportId))
                    .toList();
            if (!dropOffList.isEmpty()) {
                TransportInfoDTO item = dropOffList.get(0);
                item.setNewPickUp(newPickUp);
                item.setTransportStatus(TransportInfo.TransportStatus.하차불가);
                if (Objects.nonNull(authDTO)) {
                    item.setLmouser(authDTO.getId());
                }
                TransportInfo entity = transportInfoMapper.toEntity(item);
                transportInfoRepository.save(entity);
            }
            qTransportInfoRepository.updateTransportOrderStatus(dto.getTransportCode(), OrderStatusEnum.LOADHOLD);
        } else if (newStatus == TransportInfo.TransportStatus.회수불가) {
            List<TransportInfoDTO> dropOffList = transportInfoRepository.findByTransportCode(dto.getTransportCode())
                    .stream()
                    .map(transportInfoMapper::toDto)
                    .filter(transport -> Objects.equals(transport.getTransportType(), TransportInfo.TransportType.반품))
                    .filter(transport -> !Objects.equals(transport.getTransportId(), transportId))
                    .toList();
            if (!dropOffList.isEmpty()) {
                TransportInfoDTO item = dropOffList.get(0);
                item.setNewPickUp(newPickUp);
                item.setTransportStatus(TransportInfo.TransportStatus.반품불가);
                if (Objects.nonNull(authDTO)) {
                    item.setLmouser(authDTO.getId());
                }
                TransportInfo entity = transportInfoMapper.toEntity(item);
                transportInfoRepository.save(entity);
            }
            qTransportInfoRepository.updateTransportOrderStatus(dto.getTransportCode(), OrderStatusEnum.LOADHOLD);
        }

        dto.setImpossible(impossible);
        dto.setNewPickUp(newPickUp);
        dto.setTransportStatus(newStatus);
        TransportInfo newEntity = transportInfoMapper.toEntity(dto);
        TransportInfo savedEntity = transportInfoRepository.save(newEntity);

        Optional<DriverVehicleInfoDTO.DriverVehicleInfoCustom> vehicle = driverVehicleInfoService.findVehliceInfoByVehicleId(dto.getVehicleId());
        TransportInfo.TransportStatus finalNewStatus = newStatus;
        vehicle.ifPresent(s -> {
            String pushData = String.format("Shipment No. %s | %s (%s/%s, %s)\n%s 사유\n%s",
                    dto.getShipmentKey(), s.getUsernam(), s.getVehicleType(), s.getVehicleWeight().getKorean(), s.getRegistrationNumber(),
                    finalNewStatus, impossible);

            switch (finalNewStatus) {
                case 상차불가 -> pushService.sendImpossiblePickup(pushData);
                case 하차불가 -> pushService.sendImpossibleDropOff(pushData);
                case 회수불가 -> pushService.sendImpossibleRecovery(pushData);
                case 반품불가 -> pushService.sendImpossibleReturn(pushData);
                case 입문불가 -> pushService.sendShipyardNoEntry(pushData);
            }
        });

        List<TransportInfoDTO> infoDTOList = getTransportByShipmentKey(dto.getShipmentKey());
        boolean allCompleted = infoDTOList.stream()
                .filter(d -> !d.getTransportId().equals(transportId))
                .allMatch(d -> TransportConstant.COMPLETED_STATUS.contains(d.getTransportStatus()));
        boolean hasCompleted = infoDTOList.stream()
                .anyMatch(d -> TransportInfo.TransportStatus.운송완료.equals(d.getTransportStatus()));
        if (allCompleted && !hasCompleted) {
            dto.setTransportStatus(TransportInfo.TransportStatus.운송완료);
            dto.setImpossible(TransportConstant.FORCE_COMPLETE);
            newEntity = transportInfoMapper.toEntity(dto);
            savedEntity = transportInfoRepository.save(newEntity);
            qRealTrackingRepository.updateDeliveryCompleted(dto.getShipmentKey());
        }

        return transportInfoMapper.toDto(savedEntity);
    }

    public TransportInfo.TransportStatus processStatusAfterImpossible(TransportInfo.TransportType transportType) {
        return switch (transportType) {
            case 상차 -> TransportInfo.TransportStatus.상차불가;
            case 하차 -> TransportInfo.TransportStatus.하차불가;
            case 회수 -> TransportInfo.TransportStatus.회수불가;
            case 반품 -> TransportInfo.TransportStatus.반품불가;
        };
    }

    public List<TransportInfoDTO> getTransportByShipmentKey(final String shipmentKey) {
        return transportInfoRepository.findByShipmentKey(shipmentKey).stream()
                .map(transportInfoMapper::toDto)
                .sorted(Comparator.comparing(TransportInfoDTO::getTransportOrder))
                .collect(Collectors.toList());
    }

    public List<TransportInfoDTO> getTransportByShipmentKeyIn(List<String> shipmentKey) {
        return transportInfoRepository.findByShipmentKeyIn(shipmentKey).stream()
                .map(transportInfoMapper::toDto)
                .sorted(Comparator.comparing(TransportInfoDTO::getTransportOrder))
                .collect(Collectors.toList());
    }

    public List<TransportInfoDTO> getTransportByShipmentKeyAndTransportCode(final String shipmentKey, final String transportCode) {
        return transportInfoRepository.findByTransportCodeAndShipmentKey(transportCode, shipmentKey).stream()
                .map(transportInfoMapper::toDto)
                .sorted(Comparator.comparing(TransportInfoDTO::getTransportOrder))
                .collect(Collectors.toList());
    }

    public List<String> getSignUrl(final String shipmentKey) {
        return transportInfoRepository.findByShipmentKey(shipmentKey).stream()
                .map(transportInfoMapper::toDto)
                .map(TransportInfoDTO::getSignUrl)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public TransportConfirmationDTO.ShippingDTO getTransportConfirmation(final String shipmentKey) {
        TransportConfirmationDTO.ShippingDTO shippingDTO = new TransportConfirmationDTO.ShippingDTO();
        TransportConfirmationDTO.TCDriverVehicleDTO vehicleInfo = new TransportConfirmationDTO.TCDriverVehicleDTO();
        TransportConfirmationDTO.TransportSectionDTO sectionInfo = new TransportConfirmationDTO.TransportSectionDTO();
        List<TransportConfirmationDTO.TransportLocationDTO> pickupInfoList = new ArrayList<>();
        List<TransportConfirmationDTO.TransportLocationDTO> dropOffInfoList = new ArrayList<>();

        List<TransportInfoDTO> infoDTOList = this.getTransportByShipmentKey(shipmentKey);
        if (!infoDTOList.isEmpty()) {
            TransportInfoDTO infoDTO = infoDTOList.get(0);
            shippingDTO.setTransportRound(infoDTO.getTransportRound());
            shippingDTO.setRequestDate(infoDTO.getRequestDate());

            Optional<DriverVehicleInfoDTO.DriverVehicleInfoCustom> vehicle = driverVehicleInfoService.findVehliceInfoByVehicleId(infoDTO.getVehicleId());
            vehicle.ifPresent(s -> {
                vehicleInfo.setUsername(s.getUsernam());
                vehicleInfo.setTelphnm(s.getTelphnm());
                vehicleInfo.setVehicleType(s.getVehicleType());
                vehicleInfo.setVehicleWeight(s.getVehicleWeight());
                vehicleInfo.setRegistrationNumber(s.getRegistrationNumber());
                vehicleInfo.setSignUrl(s.getSignUrl());
            });

            List<TransportInfoDTO> pickupList = infoDTOList.stream()
                    .filter(dto -> dto.getTransportType().equals(TransportInfo.TransportType.상차) ||
                            dto.getTransportType().equals(TransportInfo.TransportType.회수))
                    .toList();
            List<TransportInfoDTO> dropOffList = infoDTOList.stream()
                    .filter(dto -> dto.getTransportType().equals(TransportInfo.TransportType.하차) ||
                            dto.getTransportType().equals(TransportInfo.TransportType.반품))
                    .toList();
            List<TransportInfoDTO> dropOffGroupList = infoDTOList.stream()
                    .filter(dto -> dto.getTransportType().equals(TransportInfo.TransportType.하차) ||
                            dto.getTransportType().equals(TransportInfo.TransportType.반품))
                    .collect(Collectors.groupingBy(TransportInfoDTO::getCompanyName))
                    .values().stream()
                    .flatMap(list -> list.stream().limit(1))
                    .toList();
            sectionInfo.setPickupCount(pickupList.size());
            sectionInfo.setDropOffCount(dropOffGroupList.size());

            for (TransportInfoDTO dto : pickupList) {
                TransportConfirmationDTO.TransportLocationDTO pickupInfo = getTransportLocationDTO(dto);

                pickupInfoList.add(pickupInfo);
            }

            for (TransportInfoDTO dto : dropOffList) {
                TransportConfirmationDTO.TransportLocationDTO dropOffInfo = getTransportLocationDTO(dto);

                dropOffInfoList.add(dropOffInfo);
            }
        }

        shippingDTO.setShipmentKey(shipmentKey);
        shippingDTO.setVehicleInfo(vehicleInfo);
        shippingDTO.setSectionInfo(sectionInfo);
        shippingDTO.setPickupInfo(pickupInfoList);
        shippingDTO.setDropOffInfo(dropOffInfoList);
        
        return shippingDTO;
    }

    private static TransportConfirmationDTO.TransportLocationDTO getTransportLocationDTO(TransportInfoDTO dto) {
        TransportConfirmationDTO.TransportLocationDTO locationInfo = new TransportConfirmationDTO.TransportLocationDTO();

        locationInfo.setTransportCode(dto.getTransportCode());
        locationInfo.setTransportType(dto.getTransportType());
        locationInfo.setCompanyName(dto.getCompanyName());
        locationInfo.setDropOffArea(dto.getDropOffArea());
        locationInfo.setSignUrl(dto.getSignUrl());
        locationInfo.setContactNumber(dto.getContactNumber());
        locationInfo.setContactName(dto.getContactName());
        locationInfo.setQuantity(dto.getQuantity());
        return locationInfo;
    }
}
