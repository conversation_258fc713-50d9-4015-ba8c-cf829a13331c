package com.logistics.tms.transport.service;

import com.logistics.tms.common.enumeration.VisitTypeEnum;
import com.logistics.tms.common.util.AuthUtils;
import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.driver.dto.DriverInfoDTO;
import com.logistics.tms.driver.dto.DriverVehicleInfoDTO;
import com.logistics.tms.driver.service.DriverInfoCustomService;
import com.logistics.tms.driver.service.DriverVehicleInfoService;
import com.logistics.tms.push.service.PushService;
import com.logistics.tms.realtracking.dto.RealTrackingDTO;
import com.logistics.tms.shipment.dto.ShipmentSectionDTO;
import com.logistics.tms.shipment.service.ShipmentSectionService;
import com.logistics.tms.transport.dto.TransportEventDTO;
import com.logistics.tms.transport.dto.TransportEventUpdateDTO;
import com.logistics.tms.transport.dto.TransportInfoDTO;
import com.logistics.tms.transport.entity.TransportEvent;
import com.logistics.tms.transport.entity.TransportInfo;
import com.logistics.tms.transport.mapper.TransportEventMapper;
import com.logistics.tms.transport.repository.TransportEventRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@RequiredArgsConstructor
@Transactional(readOnly = true)
@Service
public class TransportEventService {
    private final TransportEventRepository transportEventRepository;
    private final TransportEventMapper transportEventMapper;
    private final TransportInfoService transportInfoService;
    private final PushService pushService;
    private final DriverVehicleInfoService driverVehicleInfoService;
    private final ShipmentSectionService shipmentSectionService;
    private final TmsToVmsService tmsToVmsService;
    private final DriverInfoCustomService driverInfoCustomService;

    public TransportEventDTO findByEventId(final Long eventId) {
        TransportEvent entity = transportEventRepository.findByEventId(eventId);
        return transportEventMapper.toDto(entity);
    }

    public TransportEventDTO findByTransportId(final Long transportId) {
        List<TransportEvent> entities = transportEventRepository.findByTransportId(transportId);
        if (entities.isEmpty())
            return new TransportEventDTO();
        return transportEventMapper.toDto(entities.get(0));
    }

    public List<RealTrackingDTO.TrackingEventDTO> findByTransportIdList(final Long transportId) {
        List<TransportEvent> entities = transportEventRepository.findByTransportId(transportId);
        return transportEventMapper.toEventList(entities);
    }

    @Transactional
    public TransportEventDTO save(final TransportEventUpdateDTO transportEventUpdateDTO) {
        Long transportId = transportEventUpdateDTO.getTransportId();

        TransportEventDTO eventDTO = findByTransportId(transportId);
        if (Objects.nonNull(eventDTO) && eventDTO.getEventId() != null) {
            return modifyEvent(transportEventUpdateDTO);
        }

        TransportEventDTO transportEventDTO = new TransportEventDTO(transportEventUpdateDTO);

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            transportEventDTO.setCreuser(authDTO.getId());
        }

        TransportInfoDTO transportInfoDTO = transportInfoService.findByTransportId(transportId);
        transportEventDTO.setShipmentKey(transportInfoDTO.getShipmentKey());
        TransportEvent entity = transportEventMapper.toEntity(transportEventDTO);
        TransportEvent savedEntity = transportEventRepository.save(entity);

        postMisMatchEvent(transportEventUpdateDTO, transportInfoDTO.getTransportCode());
        postOtherEvent(transportEventUpdateDTO, transportEventDTO, transportInfoDTO, savedEntity);

        return transportEventMapper.toDto(savedEntity);
    }

    public void postMisMatchEvent(final TransportEventUpdateDTO transportEventUpdateDTO, String oetmsky) {
        if (transportEventUpdateDTO.getEventType().equals(TransportEvent.EventType.상차물량체적불일치)
                || transportEventUpdateDTO.getEventType().equals(TransportEvent.EventType.상하차검수불일치)
                || transportEventUpdateDTO.getEventType().equals(TransportEvent.EventType.상하차주소정보불일치)) {
            final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
            if (Objects.nonNull(authDTO)) {
                String regNumber = null;
                Optional<DriverInfoDTO.DriverAllInfo> driverInfoCustomDTO = driverInfoCustomService.findByUseract(authDTO.getId());
                if(driverInfoCustomDTO.isPresent()) {
                    regNumber = driverInfoCustomDTO.get().getRegistrationNumber();
                }
                switch (transportEventUpdateDTO.getEventType()) {
                    // 제거
//                    case 상차물량체적불일치 -> tmsToVmsService.postVolumeMismatch(regNumber);
                    case 상하차검수불일치 -> tmsToVmsService.postInspectionMismatch(oetmsky, regNumber);
                    // 제거
//                    case 상하차주소정보불일치 -> tmsToVmsService.postInformationMismatch(regNumber);
                }
            }
        }
    }

    public void postOtherEvent(final TransportEventUpdateDTO transportEventUpdateDTO,
                               final TransportEventDTO transportEventDTO,
                               final TransportInfoDTO transportInfoDTO,
                               final TransportEvent savedEntity) {
        Optional<DriverVehicleInfoDTO.DriverVehicleInfoCustom> vehicle = driverVehicleInfoService.findVehliceInfoByVehicleId(transportEventDTO.getVehicleId());
        vehicle.ifPresent(s -> {
            if (transportEventDTO.getEventType() == TransportEvent.EventType.기타지급비용) {
                List<ShipmentSectionDTO> shipList = shipmentSectionService.getShipmentSectionListByOetmsky(transportInfoDTO.getCompkey(), transportInfoDTO.getTransportCode())
                        .stream()
                        .toList();
                if (!shipList.isEmpty()) {
                    ShipmentSectionDTO pickup = shipList.stream()
                            .filter(d -> d.getVsttype().equals(VisitTypeEnum.PICKUP))
                            .toList().get(0);
                    ShipmentSectionDTO dropOff = shipList.stream()
                            .filter(d -> d.getVsttype().equals(VisitTypeEnum.DROPOFF))
                            .toList().get(0);

                    String pushData = String.format("Shipment No. %s | %s (%s/%s, %s)\n운송주문번호 %s %s/%s %s\n상차 %s %s\n하차 %s %s",
                            transportEventDTO.getShipmentKey(), s.getUsernam(), s.getVehicleType(), s.getVehicleWeight().getKorean(), s.getRegistrationNumber(),
                            transportInfoDTO.getTransportCode(), pickup.getOpktype().getEnglish(), pickup.getOpakqty(), pickup.getShipment().getCargoty().getKorean(),
                            pickup.getPtnamlc(), pickup.getLudrqtm(), dropOff.getPtnamlc(), dropOff.getLudrqtm()
                    );

                    pushService.sendOtherCharge(pushData);
                }
            } else {
                String pushData = String.format("Shipment No. %s | %s (%s/%s, %s)\n%s\n%s",
                        transportEventDTO.getShipmentKey(), s.getUsernam(), s.getVehicleType(), s.getVehicleWeight().getKorean(), s.getRegistrationNumber(),
                        transportEventDTO.getEventType(), transportEventDTO.getDetail());

                if (transportEventDTO.getEventType().equals(TransportEvent.EventType.정차시사고발생)
                        || transportEventDTO.getEventType().equals(TransportEvent.EventType.주행중운전사고발생)
                        || transportEventDTO.getEventType().equals(TransportEvent.EventType.주행중제품파손발생)) {
                    pushService.sendSafetyAccident(pushData);

                    final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
                    tmsToVmsService.postSafety(Objects.requireNonNull(authDTO).getCompany(), "NA", savedEntity.getEventId(), transportEventUpdateDTO.getLongitude(), transportEventUpdateDTO.getLatitude(), Objects.requireNonNull(authDTO).getId());
                } else {
                    pushService.sendNewEvent(pushData);
                }
            }
        });
    }

    @Transactional
    public TransportEventDTO modifyEvent(final TransportEventUpdateDTO transportEventUpdateDTO) {
        List<TransportEvent> oldEntity = transportEventRepository.findByTransportId(transportEventUpdateDTO.getTransportId());
        if (oldEntity.isEmpty())
            return new TransportEventDTO();
        TransportEventDTO dto = transportEventMapper.toDto(oldEntity.get(0));

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            dto.setLmouser(authDTO.getId());
        }

        dto.setEventType(transportEventUpdateDTO.getEventType());
        dto.setEventCode(TransportEventDTO.getEventCode(transportEventUpdateDTO.getEventType()));
        dto.setDetail(transportEventUpdateDTO.getDetail());
        dto.setConfirmDt(null);

        TransportEvent newEntity = transportEventMapper.toEntity(dto);
        TransportEvent savedEntity = transportEventRepository.save(newEntity);

        Long transportId = transportEventUpdateDTO.getTransportId();
        TransportInfoDTO transportInfoDTO = transportInfoService.findByTransportId(transportId);

        postMisMatchEvent(transportEventUpdateDTO, transportInfoDTO.getTransportCode());
        postOtherEvent(transportEventUpdateDTO, dto, transportInfoDTO, savedEntity);

        return transportEventMapper.toDto(savedEntity);
    }

    @Transactional
    public TransportEventDTO modifyConfirm(final Long eventId) {
        TransportEvent oldEntity = transportEventRepository.findByEventId(eventId);
        TransportEventDTO dto = transportEventMapper.toDto(oldEntity);

        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            dto.setLmouser(authDTO.getId());
        }

        Long transportId = dto.getTransportId();
        transportInfoService.modifyTransportStatus(transportId, TransportInfo.TransportStatus.운송완료, (float)0, (float)0);

        dto.setConfirmDt(LocalDateTime.now());
        TransportEvent newEntity = transportEventMapper.toEntity(dto);
        TransportEvent savedEntity = transportEventRepository.save(newEntity);
        return transportEventMapper.toDto(savedEntity);
    }
}
