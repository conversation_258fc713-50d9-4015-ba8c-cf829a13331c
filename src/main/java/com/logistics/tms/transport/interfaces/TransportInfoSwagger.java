package com.logistics.tms.transport.interfaces;

import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.configuration.security.AuthUser;
import com.logistics.tms.transport.dto.*;
import com.logistics.tms.transport.entity.TransportInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.util.List;

@Tag(name = "TransportInfo", description = "운송 상태 설정 & 조회 API")
public interface TransportInfoSwagger {
    @Operation(summary = "운송 transportId로 조회")
    @Parameters({
            @Parameter(name = "transportId", description = "운송 ID", required = true, example = "1")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = TransportInfoDTO.class)))
    })
    TransportInfoDTO getTransportInfoById(@RequestParam final Long transportId);

    @Operation(summary = "날짜별 운송 요약",
            description = """
                    Response Body: json 형식 등록된 운송 날짜별 요약 응답 \s
                    [ \s
                    &nbsp;{ \s
                    &nbsp;&nbsp;"requestDate": "2024-10-07", \s
                    &nbsp;&nbsp;"roundCount": 2, \s
                    &nbsp;&nbsp;"transportCount": [ \s
                    &nbsp;&nbsp;&nbsp;2, \s
                    &nbsp;&nbsp;&nbsp;1 \s
                    &nbsp;&nbsp;] \s
                    &nbsp;} \s
                    ] \s
                     \s
                    - requestDate: 요청날짜 \s
                    - roundCount: 회차 개수 2이면 2회차 까지 있다  \s
                    - transportCount[]: 운송 개수 배열 1이면 단독, 2이상이면 혼적""")
    @Parameters({
            @Parameter(name = "vehicleId", description = "차량 ID", required = true, example = "1"),
            @Parameter(name = "today", description = "기준 일자", example = "20241007")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            array = @ArraySchema(schema = @Schema(implementation = TransportInfoResponseDTO.SummaryDTO.class))))
    })
    ResponseEntity<?> getTransportSummary(
            @RequestParam final Long vehicleId,
            @RequestParam(value = "today", required = false) @Nullable final String today);

    @Operation(summary = "차량별 운송 조회")
    @Parameters({
            @Parameter(name = "vehicleId", description = "차량 ID", required = true, example = "1"),
            @Parameter(name = "transportRound", description = "운송 회차", required = true, example = "1"),
            @Parameter(name = "requestDate", description = "요청 일자", required = true, example = "20240927")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = TransportInfoResponseDTO.ByVehicleDTO.class)))
    })
    ResponseEntity<?> getTransportInfoByVehicle(
            @RequestParam final Long vehicleId,
            @RequestParam final int transportRound,
            @RequestParam @NotBlank final String requestDate);

    @Operation(summary = "지도용 운송 조회",
            description = "완료된 운송지는 제거하고 경로를 표시한다.")
    @Parameters({
            @Parameter(name = "vehicleId", description = "차량 ID", required = true, example = "2"),
            @Parameter(name = "transportRound", description = "운송 회차", required = true, example = "1"),
            @Parameter(name = "requestDate", description = "요청 일자", required = true, example = "20241010"),
            @Parameter(name = "gpsX", description = "현재 위치 경도", required = true, example = "128.828931"),
            @Parameter(name = "gpsY", description = "현재 위치 위도", required = true, example = "35.143067")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = TransportInfoResponseDTO.ByMapDTO.class)))
    })
    ResponseEntity<?> getRoute(
            @RequestParam final Long vehicleId,
            @RequestParam final int transportRound,
            @RequestParam @NotBlank final String requestDate,
            @RequestParam final Double gpsX,
            @RequestParam final Double gpsY
    );

    @Operation(summary = "운송확인증 조회")
    @Parameters({
            @Parameter(name = "shipmentKey", description = "Shipment No.", required = true, example = "S000000002")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "조회 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = TransportConfirmationDTO.ShippingDTO.class)))
    })
    ResponseEntity<?> getTransportConfirmation(@RequestParam @NotBlank final String shipmentKey);

    @Operation(summary = "운송 등록",
            description = """
                    Request Body: json 형식 \s
                    **{ \s
                        "shipmentKey": "S123456789", \s
                        "dispatchId": 1, \s
                        "vehicleId": 4, \s
                        "transportRound": 1, \s
                        "transportOrder": 14, \s
                        "transportType": "하차", \s
                        "transportStatus": "운송대기", \s
                        "companyName": "현대중공업", \s
                        "companyAddress": "울산 동구 전하동 1", \s
                        "dropOffArea": "제 1 적치장", \s
                        "requestDate": "2024-09-27", \s
                        "requestTime": "08:00:00", \s
                        "quantity": "one-side / 2ea", \s
                        "transportCode": "C0002", \s
                        "requiredTime": 60, \s
                        "restriction": "크레인 상차, 윙바디 불가", \s
                        "contactName": "최담당", \s
                        "contactNumber": "01012345678" \s
                    }** \s
                    Response: 등록된 운송 정보 응답""")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "등록 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = TransportInfoDTO.class)))
    })
    ResponseEntity<?> createTransportInfo(@RequestBody TransportInfoUpdateDTO transportInfoUpdateDTO);

    @Operation(summary = "운송 배열 등록",
            description = """
                    Request Body: json 배열 \s
                    **[{ \s
                        "shipmentKey": "S123456789", \s
                        "dispatchId": 1, \s
                        "vehicleId": 4, \s
                        "transportRound": 1, \s
                        "transportOrder": 14, \s
                        "transportType": "하차", \s
                        "transportStatus": "운송대기", \s
                        "companyName": "현대중공업", \s
                        "companyAddress": "울산 동구 전하동 1", \s
                        "dropOffArea": "제 1 적치장", \s
                        "requestDate": "2024-09-27", \s
                        "requestTime": "08:00:00", \s
                        "quantity": "one-side / 2ea", \s
                        "transportCode": "C0002", \s
                        "requiredTime": 60, \s
                        "restriction": "크레인 상차, 윙바디 불가", \s
                        "contactName": "최담당", \s
                        "contactNumber": "01012345678" \s
                    }]** \s
                    Response: 등록된 운송 개수 응답""")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "size() created")
    })
    ResponseEntity<?> createTransportInfos(@RequestBody List<TransportInfoUpdateDTO> transportInfoUpdateDTOs);

    @Operation(summary = "운송 등록 - Shipment",
            description = """
                    Request Body: json 배열 \s
                    **{ \s
                        "compkey": "100", \s
                        "shipmentKey": "S241031003", \s
                        "dispatchId": 1, \s
                        "vehicleId": 4 \s
                    }** \s
                    Response: 등록된 운송 개수 응답""")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "size() created")
    })
    ResponseEntity<?> createTransportByShipment(@RequestBody TransportInfoUpdateByDispatch.ByShipmentDTO updateByShipmentDTO);

    @Operation(summary = "운송 상태 변경")
    @Parameters({
            @Parameter(name = "transportId", description = "운송 ID", required = true, example = "1"),
            @Parameter(name = "transportStatus", description = "운송 상태", required = true, example = "운송대기"),
            @Parameter(name = "latitude", description = "위도", required = false, example = "36.123456"),
            @Parameter(name = "longitude", description = "경도", required = false, example = "127.234567")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "상태 변경 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = TransportInfoDTO.class)))
    })
    ResponseEntity<?> modifyTransportStatus(@RequestParam final Long transportId,
                                            @RequestParam final TransportInfo.TransportStatus transportStatus,
                                            @Nullable final Float latitude,
                                            @Nullable final Float longitude);

    @Operation(summary = "운송 순서 또는 조선소 하차 순서 변경",
            description = """
                    운송 순서나 조선소 하차 순서를 변경할 때 사용하며 \s
                    Request Body: 운송 ID 배열 입력. 예시 [10, 6, 7, 11] \s
                    Response: 변경된 운송 정보 배열 응답""")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "순서 변경 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE,
                            array = @ArraySchema(schema = @Schema(implementation = TransportInfoDTO.class))))
    })
    ResponseEntity<?> modifyOrder(@RequestBody List<Long> transportIds);

    @Operation(summary = "서명, 반품증 등록",
            description = """
                    상차 서명, 하차 서명, 반품증 등록할 때 사용하며 \s
                    Request Body: form-data 방식 \s
                    Response: 변경된 운송 정보 응답""")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "서명, 반품증 등록 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = TransportInfoDTO.class)))
    })
    ResponseEntity<?> modifySign(@RequestParam @Schema(description = "운송 ID") final Long transportId,
                                 @RequestParam("file") MultipartFile file);

    @Operation(summary = "서명, 반품증 다운로드")
    @Parameters({
            @Parameter(name = "signUrl", description = "이미지 파일 경로", required = true, example = "/app/BMEA/transportSign/2/서명.png")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "다운로드 성공")
    })
    ResponseEntity<StreamingResponseBody> downloadSign(@AuthUser final AuthDTO authDTO,
                                                       @RequestParam @NotBlank final String signUrl);

    @Operation(summary = "운송 불가 등록")
    @Parameters({
            @Parameter(name = "transportId", description = "운송 ID", required = true, example = "1"),
            @Parameter(name = "impossible", description = "불가 사유", required = true, example = "날씨 악화로 운송 불가 합니다."),
            @Parameter(name = "newPickUp", description = "신규 상차 요청 여부", example = "0")
    })
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "불가 등록 성공",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = TransportInfoDTO.class)))
    })
    ResponseEntity<?> addImpossible(@RequestParam final Long transportId,
                                    @RequestParam @NotBlank final String impossible,
                                    @RequestParam(value = "newPickUp", required = false) @Nullable final boolean newPickUp);
}
