package com.logistics.tms.transport.repository;

import com.logistics.tms.callroute.entity.QCallRoute;
import com.logistics.tms.common.enumeration.OrderStatusEnum;
import com.logistics.tms.common.enumeration.VehicleStatusEnum;
import com.logistics.tms.common.util.AuthUtils;
import com.logistics.tms.configuration.security.AuthDTO;
import com.logistics.tms.external.model.QOetmhd;
import com.logistics.tms.external.model.QOetmit;
import com.logistics.tms.external.model.QSusrma;
import com.logistics.tms.transport.dto.TransportInfoResponseDTO;
import com.logistics.tms.transport.entity.QTransportInfo;
import com.logistics.tms.transport.entity.TransportInfo;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.repository.support.QuerydslRepositorySupport;
import org.springframework.stereotype.Repository;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;

@Slf4j
@Validated
@Repository
public class QTransportInfoRepository extends QuerydslRepositorySupport {
    private final JPAQueryFactory queryFactory;

    public QTransportInfoRepository(final JPAQueryFactory queryFactory) {
        super(TransportInfo.class);
        this.queryFactory = queryFactory;
    }

    public List<TransportInfoResponseDTO.DateRoundDTO> getSummaryDateRound(Long vehicleId, LocalDate requestDate) {
        QTransportInfo ti = QTransportInfo.transportInfo;

        return queryFactory
                .select(Projections.fields(TransportInfoResponseDTO.DateRoundDTO.class, ti.requestDate, ti.transportRound))
                .from(ti)
                .where(
                        ti.requestDate.goe(requestDate),
                        ti.vehicleId.eq(vehicleId)
                )
                .groupBy(ti.requestDate, ti.transportRound)
                .orderBy(ti.requestDate.asc(), ti.transportRound.asc())
                .distinct()
                .fetch();
    }

    public TransportInfo.TransportStatus getLastStatus(Long vehicleId, LocalDate requestDate, int transportRound) {
        QTransportInfo ti = QTransportInfo.transportInfo;

        return queryFactory
                .select(ti.transportStatus)
                .from(ti)
                .where(
                        ti.requestDate.eq(requestDate),
                        ti.transportRound.eq(transportRound),
                        ti.vehicleId.eq(vehicleId)
                )
                .orderBy(ti.lmodate.desc(), ti.lmotime.desc())
                .fetchFirst();
    }

    public void updateExpectedDistance(String shipmentKey, int totalEstimatedMeters, int totalEstimatedSecs) {
        QCallRoute qCallRoute = QCallRoute.callRoute;

        String modifyUser = " ";
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            modifyUser = authDTO.getId();
        }
        queryFactory.update(qCallRoute)
                .set(qCallRoute.expectedDistance, totalEstimatedMeters)
                .set(qCallRoute.expectedTime, totalEstimatedSecs)
                .set(qCallRoute.lmouser, modifyUser)
                .set(qCallRoute.lmodate, LocalDate.now())
                .set(qCallRoute.lmotime, LocalTime.now())
                .where(qCallRoute.shpmtky.eq(shipmentKey))
                .execute();
    }

    public void updateTransportOrderStatus(final String oeTmsKey, OrderStatusEnum orderStatus) {
        QOetmhd qOetmhd = QOetmhd.oetmhd;
        QOetmit qOetmit = QOetmit.oetmit;

        String modifyUser = " ";
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            modifyUser = authDTO.getId();
        }
        final JPAUpdateClause updateClause = queryFactory.update(qOetmhd)
                .set(qOetmhd.tshitst, orderStatus)
                .set(qOetmhd.lmouser, modifyUser)
                .set(qOetmhd.lmodate, LocalDate.now())
                .set(qOetmhd.lmotime, LocalTime.now());

        if (orderStatus.equals( OrderStatusEnum.LOAD)) {
            updateClause.set(qOetmhd.loaddat, LocalDate.now())
                    .set(qOetmhd.loadtim, LocalTime.now());
        }
        else if(orderStatus.equals( OrderStatusEnum.UNLOAD)) {
            updateClause.set(qOetmhd.dvcmpdt, LocalDate.now())
                    .set(qOetmhd.dvcmptm, LocalTime.now());
        }

        updateClause.where(qOetmhd.oetmsky.eq(oeTmsKey))
                .execute();

        queryFactory.update(qOetmit)
                .set(qOetmit.tshitst, orderStatus)
                .set(qOetmit.lmouser, modifyUser)
                .set(qOetmit.lmodate, LocalDate.now())
                .set(qOetmit.lmotime, LocalTime.now())
                .where(qOetmit.oetmsky.eq(oeTmsKey))
                .execute();
    }

    public void updateTransportVehicleStatus(final String oeTmsKey, VehicleStatusEnum vehicleStatus) {
        QOetmhd qOetmhd = QOetmhd.oetmhd;
        QOetmit qOetmit = QOetmit.oetmit;

        String modifyUser = " ";
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            modifyUser = authDTO.getId();
        }
        final JPAUpdateClause updateClause = queryFactory.update(qOetmhd)
                .set(qOetmhd.trnstat, vehicleStatus)
                .set(qOetmhd.lmouser, modifyUser)
                .set(qOetmhd.lmodate, LocalDate.now())
                .set(qOetmhd.lmotime, LocalTime.now());

        if (vehicleStatus.equals( VehicleStatusEnum.START)) {
            updateClause.set(qOetmhd.lodstdt, LocalDate.now())
                    .set(qOetmhd.lodsttm, LocalTime.now());
        } else if (vehicleStatus.equals( VehicleStatusEnum.LOADARR)) {
            updateClause.set(qOetmhd.loadArrDat, LocalDate.now())
                    .set(qOetmhd.loadArrTim, LocalTime.now());
        } else if (vehicleStatus.equals( VehicleStatusEnum.UNLOADARR)) {
            updateClause.set(qOetmhd.unloadArrDat, LocalDate.now())
                    .set(qOetmhd.unloadArrTim, LocalTime.now());
        }

        updateClause.where(qOetmhd.oetmsky.eq(oeTmsKey))
                .execute();

        queryFactory.update(qOetmit)
                .set(qOetmit.trnstat, vehicleStatus)
                .set(qOetmit.lmouser, modifyUser)
                .set(qOetmit.lmodate, LocalDate.now())
                .set(qOetmit.lmotime, LocalTime.now())
                .where(qOetmit.oetmsky.eq(oeTmsKey))
                .execute();
    }

    public List<String> getTargetUsers(String partnerKey) {
        QSusrma qSusrma = QSusrma.susrma;

        return queryFactory
                .select(qSusrma.useract)
                .from(qSusrma)
                .where(
                        qSusrma.ptnrkey.eq(partnerKey),
                        qSusrma.usertyp.ne("DRIVER")
                )
                .fetch();
    }

    public void updateTransportVehicleStatusShipyard(final String oeTmsKey, VehicleStatusEnum vehicleStatus) {
        QOetmhd qOetmhd = QOetmhd.oetmhd;
        QOetmit qOetmit = QOetmit.oetmit;

        String modifyUser = " ";
        final AuthDTO authDTO = AuthUtils.getUserAuthDTO();
        if (Objects.nonNull(authDTO)) {
            modifyUser = authDTO.getId();
        }
        final JPAUpdateClause updateClause = queryFactory.update(qOetmhd)
                .set(qOetmhd.trnstat, vehicleStatus)
                .set(qOetmhd.lmouser, modifyUser)
                .set(qOetmhd.lmodate, LocalDate.now())
                .set(qOetmhd.lmotime, LocalTime.now());

        updateClause.set(qOetmhd.cucmpdt, LocalDate.now())
                .set(qOetmhd.cucmptm, LocalTime.now());

        updateClause.where(qOetmhd.oetmsky.eq(oeTmsKey))
                .execute();

        queryFactory.update(qOetmit)
                .set(qOetmit.trnstat, vehicleStatus)
                .set(qOetmit.lmouser, modifyUser)
                .set(qOetmit.lmodate, LocalDate.now())
                .set(qOetmit.lmotime, LocalTime.now())
                .where(qOetmit.oetmsky.eq(oeTmsKey))
                .execute();
    }
}
