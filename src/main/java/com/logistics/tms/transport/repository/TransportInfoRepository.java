package com.logistics.tms.transport.repository;

import com.logistics.tms.transport.entity.TransportInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TransportInfoRepository extends JpaRepository<TransportInfo, Long> {
    TransportInfo findByTransportId(Long transportId);
    TransportInfo findByTransportCodeAndTransportType(String transportCode, TransportInfo.TransportType transportType);
    List<TransportInfo> findByTransportIdIn(List<Long> transportIds);
    List<TransportInfo> findByVehicleIdAndTransportRoundAndRequestDateOrderByTransportOrderAsc(Long vehicleId, int transportRound, LocalDate requestDate);

    @Query("""
            SELECT DISTINCT i.requestDate
            FROM TransportInfo i
            WHERE i.requestDate = :requestDate
            AND (i.transportType = '하차' OR i.transportType = '반품')
            AND i.vehicleId = :vehicleId""")
    LocalDate findDistinctRequestDateByVehicleIdAndRequestDateEqual(Long vehicleId, LocalDate requestDate);

    @Query("""
            SELECT MAX(i.transportRound)
            FROM TransportInfo i
            WHERE i.requestDate = :requestDate
            AND (i.transportType = '하차' OR i.transportType = '반품')
            AND i.vehicleId = :vehicleId""")
    int maxDistinctByTransportRoundAndVehicleIdAndRequestDate(Long vehicleId, LocalDate requestDate);

    @Query("""
            SELECT COUNT(DISTINCT i.transportCode)
            FROM TransportInfo i
            WHERE i.requestDate = :requestDate
            AND i.transportRound = :transportRound
            AND (i.transportType = '하차' OR i.transportType = '반품')
            AND i.vehicleId = :vehicleId""")
    int countDistinctByTransportCodeAndVehicleIdAndRequestDateEqualsAndTransportRoundEquals(Long vehicleId, LocalDate requestDate, int transportRound);

    List<TransportInfo> findByShipmentKey(String shipmentKey);
    List<TransportInfo> findByTransportCode(String transportCode);
    List<TransportInfo> findByTransportCodeAndShipmentKey(String transportCode, String shipmentKey);
    List<TransportInfo> findByShipmentKeyIn(List<String> shipmentKey);
}
