plugins {
    id 'java'
    id 'org.springframework.boot' version '3.2.8'
    id 'io.spring.dependency-management' version '1.1.6'
    id 'com.google.cloud.tools.jib' version '3.4.0'  // Jib 버전 업데이트
}

group = 'com.logistics'
version = '0.0.1-SNAPSHOT'


java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}

repositories {
    mavenCentral()
}

ext {
    set('springCloudVersion', "2023.0.0")
    CREATION_UTC_DATETIME = new Date().format("yyyy-MM-dd'T'HH:mm:ss'Z'", TimeZone.getTimeZone("UTC"))
    DATE_VER = new Date().format("yyyyMMdd'_'HHmm", TimeZone.getTimeZone("Asia/Seoul"))
}

// https://gogo-jjm.tistory.com/102
// FeignClient사용시  spring과 버전이 맞아야 오류가 나지 않는다
dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

// https://docs.spring.io/spring-boot/docs/3.2.8/reference/html/dependency-versions.html
dependencies {

    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-logging'

    // MapStruct
    implementation 'org.mapstruct:mapstruct:1.6.0'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.6.0'

    // Lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'

    // Lombok and MapStruct integration
    annotationProcessor 'org.projectlombok:lombok-mapstruct-binding:0.2.0'

    // Swagger
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.6.0'

    // hibernate-spatial
    implementation "org.hibernate:hibernate-spatial:${dependencyManagement.importedProperties['hibernate.version']}"

    // MariaDB
    runtimeOnly 'org.mariadb.jdbc:mariadb-java-client'

    // QueryDSL
    implementation "com.querydsl:querydsl-jpa:${dependencyManagement.importedProperties['querydsl.version']}:jakarta"
    annotationProcessor "com.querydsl:querydsl-apt:${dependencyManagement.importedProperties['querydsl.version']}:jakarta"
    annotationProcessor 'jakarta.annotation:jakarta.annotation-api'
    annotationProcessor 'jakarta.persistence:jakarta.persistence-api'

    // Apache
//    implementation 'org.apache.httpcomponents.client5:httpclient5:5.3.1'
    implementation 'org.apache.commons:commons-lang3:3.17.0'
    implementation 'org.apache.commons:commons-collections4:4.4'
    implementation 'org.apache.commons:commons-math3:3.6.1'

    // jwt
    implementation 'io.jsonwebtoken:jjwt-api:0.11.5'        // https://mvnrepository.com/artifact/io.jsonwebtoken/jjwt-api
    implementation 'io.jsonwebtoken:jjwt-impl:0.11.5'       // https://mvnrepository.com/artifact/io.jsonwebtoken/jjwt-impl
    implementation 'io.jsonwebtoken:jjwt-jackson:0.11.5'
    implementation 'org.jsoup:jsoup:1.15.3'

    //Excel
    implementation 'org.apache.poi:poi-ooxml:5.3.0'

    // WebFlux 의존성 추가
    implementation 'org.springframework.boot:spring-boot-starter-webflux'

    // Validation
    implementation 'com.google.code.findbugs:jsr305:3.0.2'

    // OpenFeign
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'

    // Firebase Admin
    implementation 'com.google.firebase:firebase-admin:9.4.1'

    // Retry
    implementation 'org.springframework.retry:spring-retry:1.2.5.RELEASE'
//    implementation 'org.springframework:spring-context-support:5.2.12.RELEASE'
//    implementation 'org.springframework:spring-aspects:5.2.9.RELEASE'

    // Test
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
//	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

tasks.named('test') {
    useJUnitPlatform()
}

clean {
    delete file('src/main/generated')
}

jib {
    def mode = project.hasProperty("mode") ? project.mode : null
    def profile = mode
    if (mode) {
        println "jib Mode: " + mode

        from {
            image = "openjdk:17"  // Java 17로 변경
        }
        to {
            image = "harbor.logisteq.com/bmea/logistics-tms"
            tags = [ mode + "_" + "${DATE_VER}" , 'latest']
        }
        container {
            mainClass = "com.logistics.tms.TmsApplication"  // 메인 클래스 경로를 프로젝트에 맞게 수정
            creationTime = "${CREATION_UTC_DATETIME}"
            ports = ["8088"]
            volumes = ["/tmp"]
            environment = [
                    "SPRING_PROFILES_ACTIVE": profile,
                    "TZ"                    : "Asia/Seoul"
            ]
        }
    }
}